/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const margenDeltasSummary = type({
  margen: 'number',
  nombre: 'string',
  saldo: 'number',
  tipo: 'number'
})

export const margenDeltasSummaryMetadata = new Set([
  {
    header: 'Área',
    key: 'nombre'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Margen',
    key: 'margen'
  }
])

export type MargenDeltasSummary = typeof margenDeltasSummary.infer

export const margenDeltaSummaryValidate = (attempt?: MargenDeltasSummary) =>
  margenDeltasSummary(attempt) instanceof type.errors
    ? resetObject(margenDeltasSummary)
    : attempt
