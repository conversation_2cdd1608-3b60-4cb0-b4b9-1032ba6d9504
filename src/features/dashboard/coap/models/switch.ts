/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const switchTrade = type({
  comentarios: 'string',
  concepto: 'string',
  fecha: 'string',
  importe: 'number'
})

export const switchTradeMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Concepto',
    key: 'concepto'
  },
  {
    header: 'Importe',
    key: 'importe'
  },
  {
    header: 'Comentarios',
    key: 'comentarios'
  }
])

export type SwitchTrade = typeof switchTrade.infer

export const switchTradeValidate = (attempt?: SwitchTrade) =>
  switchTrade(attempt) instanceof Error
    ? resetObject(switchTrade.infer)
    : attempt
