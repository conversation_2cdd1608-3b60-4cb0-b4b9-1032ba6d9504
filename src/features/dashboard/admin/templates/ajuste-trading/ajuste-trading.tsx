import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type AjusteTrading,
  ajusteTrading,
  ajusteTradingDefault,
  ajusteTradingMetadata
} from '@d/admin/models/ajuste-trading'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Box, Button, Container, Grid, Link } from '@mui/material'
import dayjs from 'dayjs'
import { nth } from 'rambdax'
import type { FC } from 'react'

import { ajusteTradingController, validate } from './ajuste-trading-controller'

export const AjusteTradingTemplate: FC<{
  links: string[]
  unicId?: 'MCAP' | 'TESO'
}> = ({ links, unicId = 'TESO' }) => {
  const {
    activeConcepto,
    condConcept,
    dataAjuste,
    dataAjusteRefetch,
    deleteAction,
    isDataAjustePending,
    loadingDialog,
    openCreateEditDialog,
    openDeleteDialog,
    selectedData,
    selectedIndex,
    setActiveConcepto,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  } = ajusteTradingController(unicId)

  return isDataAjustePending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => dataAjusteRefetch()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = ajusteTradingDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box
              onClick={e => e.preventDefault()}
              sx={{
                '& > :not(style) ~ :not(style)': {
                  ml: 2
                },
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'start',
                typography: 'body1'
              }}
            >
              {links.map((link, ind) => (
                <Link
                  href='#'
                  key={link}
                  onClick={() =>
                    setActiveConcepto(
                      nth(ind, condConcept.current) ?? 'A_DERIV'
                    )
                  }
                  underline={
                    activeConcepto === nth(ind, condConcept.current)
                      ? 'always'
                      : 'hover'
                  }
                >
                  {link}
                </Link>
              ))}
            </Box>
          </Grid>
          <Grid item xs={12}>
            <GenericTable
              data={dataAjuste ?? []}
              entries={ajusteTrading}
              isDeleteAction
              isEditAction
              metadata={ajusteTradingMetadata}
              onDeleteClick={index => {
                selectedIndex.current = index
                setOpenDeleteDialog(true)
              }}
              onEditClick={index => {
                selectedIndex.current = index
                selectedData.current =
                  nth(index, dataAjuste ?? []) ?? ajusteTradingDefault
                setOpenCreateEditDialog(true)
              }}
            />
          </Grid>
        </Grid>
      </Container>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog open={loadingDialog} />
      <DialogForm<AjusteTrading>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validate={validate}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<AjusteTrading>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<AjusteTrading>
              field='importe'
              label='Importe:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.importe?.toString() ?? '0'}
            />
            <TextFormControl<AjusteTrading>
              field='comentarios'
              label='Comentarios:'
              multiline
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.comentarios?.toString() ?? ''}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
