import { Role, type UserProfile } from '~/types/auth'
import type { NavItemType } from '~/types/menu'

import { admin } from './admin'
import { bancaCorporativa } from './bancaCorporativa'
import { banco } from './banco'
import { coap } from './coap'
import { derivados } from './derivados'
import { instrumentos } from './instrumentos'
import { mercadoDivisas } from './mercadoDivisas'
import { mercadoMonetario } from './mercadoMonetario'
import { rentaFija } from './rentaFija'
import { riesgoMercado } from './riesgoMercado'
import { saldosTesoreros } from './saldosTesoreros'

const standardItems = (user?: UserProfile | null) => [
  banco,
  mercadoMonetario,
  rentaFija,
  derivados,
  mercadoDivisas,
  coap,
  saldosTesoreros,
  bancaCorporativa,
  riesgoMercado(user)
]

export const menuItems = (
  user?: UserProfile | null
): { items: NavItemType[] } => ({
  items:
    user?.role === Role.ADMIN
      ? [...standardItems(user), instrumentos, admin]
      : standardItems(user)
})
