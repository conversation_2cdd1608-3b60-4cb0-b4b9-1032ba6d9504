import { Upload } from '@d/admin/models/upload'
import { inject, injectable } from 'inversify'
import { piped } from 'rambdax'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class UploadsRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/uploads'

  readonly deleteFile = (filename: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${filename}`)

  readonly getFiles = () =>
    this.api.client
      .get<Upload[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly uploadFile = (files: FileList) =>
    this.api.client.post<{ message: string }>(
      this.route,
      piped(new FormData(), form => {
        form.append('file', files[0] ?? '')
        return form
      }),
      { headers: { 'Content-Type': 'multipart/form-data' } }
    )

  readonly uploadMultipleFile = (files: FileList) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/multiple`,
      piped(new FormData(), form => {
        for (const file of files) form.append('file', file)
        return form
      }),
      { headers: { 'Content-Type': 'multipart/form-data' } }
    )
}
