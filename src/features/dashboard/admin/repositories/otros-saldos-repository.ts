/* eslint-disable sonarjs/use-type-alias */
import type { OtrosSaldos } from '@d/admin/models/otros-saldos'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class OtrosSaldosRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/otrossaldos'

  readonly deleteOtrosSaldos = (osid: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${osid}`)

  readonly getOtrosSaldos = () =>
    this.api.client
      .get<OtrosSaldos[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertOtrosSaldos = (otrosSaldos: OtrosSaldos) =>
    this.api.client.post<{ message: string }>(this.route, otrosSaldos)

  readonly updateOtrosSaldos = (osid: string, otrosSaldos: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${osid}`,
      otrosSaldos
    )
}
