/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const ajusteManual = type({
  accountingAreaID: 'string',
  concepto: 'string',
  fecha: 'string',
  importe: 'number'
})

export const ajusteManualMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Importe',
    key: 'importe'
  },
  {
    header: 'Equity',
    key: 'concepto'
  }
])

export type AjusteManual = typeof ajusteManual.infer

export const ajusteManualValidate = (attempt?: AjusteManual) =>
  ajusteManual(attempt) instanceof Error
    ? resetObject(ajusteManual.infer)
    : attempt
