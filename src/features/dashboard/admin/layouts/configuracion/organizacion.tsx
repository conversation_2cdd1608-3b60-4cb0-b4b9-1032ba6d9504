/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable sonarjs/void-use */
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

const route = '/admin/configuracion/organizacion'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/areas-negocio`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/tradegroups/trade`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/portafolios`)
        setMenuId(2)
        break
      }
      case 3: {
        if (val !== menuId) void navigate(`${route}/producto`)
        setMenuId(3)
        break
      }
      case 4: {
        if (val !== menuId) void navigate(`${route}/limites`)
        setMenuId(4)
        break
      }
      case 5: {
        if (val !== menuId) void navigate(`${route}/epigrafes-informe`)
        setMenuId(5)
        break
      }
      case 6: {
        if (val !== menuId) void navigate(`${route}/parametros-generales`)
        setMenuId(6)
        break
      }
      case 7: {
        if (val !== menuId) void navigate(`${route}/perfiles-usuarios`)
        setMenuId(7)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('areas-negocio')) setMenuId(0)
    if (path.includes('trade-group')) setMenuId(1)
    if (path.includes('portafolios')) setMenuId(2)
    if (path.includes('producto')) setMenuId(3)
    if (path.includes('limites')) setMenuId(4)
    if (path.includes('epigrafes-informe')) setMenuId(5)
    if (path.includes('parametros-generales')) setMenuId(6)
    if (path.includes('perfiles-usuarios')) setMenuId(7)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Áreas de Negocio</MenuItem>
                  <MenuItem value={1}>Trade Group</MenuItem>
                  <MenuItem value={2}>Portafolios</MenuItem>
                  <MenuItem value={3}>Productos</MenuItem>
                  <MenuItem value={4}>Límites</MenuItem>
                  <MenuItem value={5}>Epígrafes de informes</MenuItem>
                  <MenuItem value={6}>Parámetros generales</MenuItem>
                  <MenuItem value={7}>Perfiles de usuario</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Outlet />
        </Grid>
      </Grid>
    </Container>
  )
}
