/* eslint-disable sonarjs/use-type-alias */
import type { Pool } from '@d/admin/models/pool'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class PoolRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/pool'

  readonly calcularPool = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/${this.attachDate()}`
    )

  readonly deletePool = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getPool = () =>
    this.api.client
      .get<Pool[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertPool = (pool: Pool) =>
    this.api.client.post<{ message: string }>(this.route, pool)

  readonly updatePool = (id: string, pool: Pool) =>
    this.api.client.put<{ message: string }>(`${this.route}/${id}`, pool)

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
