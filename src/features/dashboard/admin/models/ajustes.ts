/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'
import dayjs from 'dayjs'

import { resetObject } from '~/utils/models'

export const ajustes = type({
  accountingAreaID: 'string',
  comentario: 'string?',
  concepto: 'string',
  fecha: 'string',
  importe: 'number'
})

export const ajustesMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Accounting Área Id',
    key: 'accountingAreaID'
  },
  {
    header: 'Concepto',
    key: 'concepto'
  },
  {
    header: 'Importe',
    key: 'importe'
  },
  {
    header: 'Comentarios',
    key: 'comentario'
  }
])

export type Ajustes = typeof ajustes.infer

export const ajustesDefault: Ajustes = {
  accountingAreaID: '',
  comentario: '',
  concepto: '',
  fecha: dayjs().format('YYYY-MM-DD'),
  importe: 0
}

export const ajustesValidate = (attempt?: Ajustes) =>
  ajustes(attempt) instanceof type.errors ? resetObject(attempt) : attempt
