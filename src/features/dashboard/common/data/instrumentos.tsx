import { FolderOpenOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const instrumentos: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'instrumentos-instrument',
          title: <FormattedMessage id='instrument' />,
          type: 'item',
          url: '/instrumentos/instrument'
        }
        /* {
          id: 'instrumentos-rentaFijaSGT',
          title: <FormattedMessage id='rentaFijaSGT' />,
          type: 'item',
          url: '/instrumentos/rentaFijaSGT'
        }*/
      ],
      icon: FolderOpenOutlined,
      id: 'instrumentos',
      title: <FormattedMessage id='instrumentos' />,
      type: 'collapse'
    }
  ],
  icon: FolderOpenOutlined,
  id: 'group-instrumentos',
  title: <FormattedMessage id='instrumentos' />,
  type: 'group'
}
