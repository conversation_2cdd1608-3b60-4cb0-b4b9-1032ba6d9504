import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import {
  derivadosTrading,
  derivadosTradingMetadata
} from '@d/common/models/trading'
import { TradingRepository } from '@d/common/repository/trading-repository'
import { Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getTradingSwap } = container.get(TradingRepository)

  const {
    data: derivadosData,
    isFetching: isDerivadosPending,
    refetch: refetchDerivados
  } = useQuery({
    queryFn: getTradingSwap,
    queryKey: ['getTradingDerivados']
  })

  useEffect(() => {
    void refetchDerivados()
  }, [day])

  return isDerivadosPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchDerivados()} />
      <TitleSubtitle title='Cartera de Negociación' />
      <Paper elevation={10} sx={paperStyle}>
        <GenericTable
          data={derivadosData ?? []}
          entries={derivadosTrading}
          metadata={derivadosTradingMetadata}
          noTotal={false}
        />
      </Paper>
    </>
  )
}
