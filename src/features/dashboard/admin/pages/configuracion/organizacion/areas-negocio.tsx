import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type AreasNegocio,
  areasNegocio,
  areasNegocioDefault,
  areasNegocioMetadata
} from '@d/admin/models/areas-negocio'
import { AreasNegocioRepository } from '@d/admin/repositories/org-areas-negocio-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    delete<PERSON>reasNegocio,
    getAreasNegocio,
    insertAreasNegocio,
    updateAreasNegocio
  } = container.get(AreasNegocioRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(areasNegocioDefault)

  const {
    data: allAreasNegocioData,
    isFetching: isGetAreasNegocioPending,
    refetch: refetchAreasNegocio
  } = useQuery({
    queryFn: getAreasNegocio,
    queryKey: ['getAreasNegocio']
  })

  const submitAction = async (
    incoming: AreasNegocio,
    _: FormikHelpers<AreasNegocio>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertAreasNegocio(incoming)
      : updateAreasNegocio(selectedData.current.id.toString(), incoming))
    setLoadingDialog(false)
    void refetchAreasNegocio()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteAreasNegocio(selectedData.current.id.toString())
      setLoadingDialog(false)
      void refetchAreasNegocio()
    }
  }

  return isGetAreasNegocioPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchAreasNegocio()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = areasNegocioDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allAreasNegocioData ?? []}
                entries={areasNegocio}
                isDeleteAction
                isEditAction
                metadata={areasNegocioMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allAreasNegocioData ?? []) ?? areasNegocioDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<AreasNegocio>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          nombre: Yup.string().max(5).required('Nombre is required'),
          orden: Yup.number().required('Orden is required'),
          titulo: Yup.string().max(25).required('Titulo is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<AreasNegocio>
              field='titulo'
              label='Título: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.titulo.toString()}
            />
            <TextFormControl<AreasNegocio>
              field='orden'
              label='Orden: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.orden.toString()}
            />
            <TextFormControl<AreasNegocio>
              field='nombreInformeGlobal'
              label='Nombre informe global: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.orden.toString()}
            />
            <TextFormControl<AreasNegocio>
              field='accountingAreaId'
              label='Accounting Area Id: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.orden.toString()}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
