import type { RatioLiquidez } from '@d/admin/models/ratio-liquidez'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class RatioLiquidezRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/ratioliquidez'

  readonly getRatioLiquidez = (fecha: string) =>
    this.api.client
      .get<RatioLiquidez[]>(`${this.route}/tab/${fecha}`)
      .then(apires => apires.data)
      .then(data => data ?? [])
}
