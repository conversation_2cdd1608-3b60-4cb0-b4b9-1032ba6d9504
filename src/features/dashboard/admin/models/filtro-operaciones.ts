/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const filtroOperaciones = type({
  accionId: 'number?',
  comentarios: 'string?',
  fecha: 'string?',
  filtroOpId: 'number?',
  instrumentId: 'string?'
})

export type FiltroOperaciones = typeof filtroOperaciones.infer

export const filtroOperacionesDefault: FiltroOperaciones = {
  accionId: 0,
  comentarios: '',
  fecha: '',
  filtroOpId: 0,
  instrumentId: ''
}

export const filtroOperacionesMetadata = new Set([
  {
    header: 'Filtro Op Id',
    key: 'filtroOpId'
  },
  {
    header: 'Instrument Id',
    key: 'instrumentId'
  },
  {
    header: 'Comentarios',
    key: 'comentarios'
  },
  {
    header: 'Filtro Estado Estado',
    key: 'estado'
  },
  {
    header: 'Filtro Estado Código',
    key: 'codigo'
  }
])
