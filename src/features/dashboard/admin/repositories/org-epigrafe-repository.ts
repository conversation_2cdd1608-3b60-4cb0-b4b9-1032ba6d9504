/* eslint-disable sonarjs/use-type-alias */
import type { Epigrafes } from '@d/admin/models/epigrafe-informe'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class EpigrafesRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/epigrafes'

  readonly deleteEpigrafes = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getEpigrafes = () =>
    this.api.client
      .get<Epigrafes[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertEpigrafes = (epigrafes: Epigrafes) =>
    this.api.client.post<{ message: string }>(this.route, epigrafes)

  readonly updateEpigrafes = (id: string, epigrafes: Epigrafes) =>
    this.api.client.put<{ message: string }>(`${this.route}/${id}`, epigrafes)
}
