/* eslint-disable sonarjs/use-type-alias */
import type { Limites } from '@d/admin/models/limites'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class LimitesRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/limitesdefinicion'

  readonly deleteLimites = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getLimites = () =>
    this.api.client
      .get<Limites[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertLimites = (limites: Limites) =>
    this.api.client.post<{ message: string }>(this.route, limites)

  readonly updateLimites = (id: string, limites: Limites) =>
    this.api.client.put<{ message: string }>(`${this.route}/${id}`, limites)
}
