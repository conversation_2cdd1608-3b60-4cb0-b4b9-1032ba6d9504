/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const rentafijaSgt = type({
  cartera: 'string',
  cuponCobradoDia: 'number',
  cuponCobradoDia_Last: 'number',
  cuponCorrido: 'number',
  cuponCorrido_Last: 'number',
  cuponPeriodificado: 'number',
  cuponPeriodificado_Last: 'number',
  cuponPrepagado: 'number',
  cuponPrepPeriodific: 'number',
  duracion: 'number',
  durCorr: 'number',
  efectivoCritico: 'number',
  efectivoCritico_Last: 'number',
  efectivoHistor: 'number',
  efectivoHistor_Last: 'number',
  fecha: 'string',
  fecha_Last: 'string',
  fechaVto: 'string',
  iD: 'number',
  ingresosCup: 'number',
  ingresosCupPrepag: 'number',
  ingresosIntereses: 'number',
  ingresosTotal: 'number',
  intAcc: 'number',
  interesesPeriodif: 'number',
  nominal: 'number',
  nominal_Last: 'number',
  precioCritico: 'number',
  referencia: 'string',
  resultadosTotal: 'number',
  resultadosVentasDia: 'number',
  resultadoVentasTotal: 'number',
  retencion: 'number',
  sensi: 'number',
  tipo: 'number',
  tIR: 'number',
  vacio1: 'number',
  vacio2: 'number',
  vacio3: 'number',
  valoracionRepercutida: 'number',
  valoracionRepercutida_Last: 'number',
  valoracionTotal: 'number',
  valoracionTotal_Last: 'number',
  vidaRes: 'number'
})

export const rentafijaSgtMetadata = new Set([
  {
    header: 'Cartera',
    key: 'cartera'
  },
  {
    header: 'Referencia',
    key: 'referencia'
  },
  {
    header: 'Fecha Vto',
    key: 'fechaVto'
  },
  {
    header: 'Nominal',
    key: 'nominal'
  },
  {
    header: 'Efectivo Histor',
    key: 'efectivoHistor'
  },
  {
    header: 'Precio Crítico',
    key: 'precioCritico'
  },
  {
    header: 'Efectivo Crítico',
    key: 'efectivoCritico'
  },
  {
    header: 'Tir',
    key: 'tIR'
  },
  {
    header: 'Cupon Cobrado Dia',
    key: 'cuponCobradoDia'
  }
])

export type RentafijaSgt = typeof rentafijaSgt.infer

export const rentaFijaSgtDefault: RentafijaSgt = {
  cartera: '',
  cuponCobradoDia: 0,
  cuponCobradoDia_Last: 0,
  cuponCorrido: 0,
  cuponCorrido_Last: 0,
  cuponPeriodificado: 0,
  cuponPeriodificado_Last: 0,
  cuponPrepagado: 0,
  cuponPrepPeriodific: 0,
  duracion: 0,
  durCorr: 0,
  efectivoCritico: 0,
  efectivoCritico_Last: 0,
  efectivoHistor: 0,
  efectivoHistor_Last: 0,
  fecha: '',
  fecha_Last: '',
  fechaVto: '',
  iD: 0,
  ingresosCup: 0,
  ingresosCupPrepag: 0,
  ingresosIntereses: 0,
  ingresosTotal: 0,
  intAcc: 0,
  interesesPeriodif: 0,
  nominal: 0,
  nominal_Last: 0,
  precioCritico: 0,
  referencia: '',
  resultadosTotal: 0,
  resultadosVentasDia: 0,
  resultadoVentasTotal: 0,
  retencion: 0,
  sensi: 0,
  tipo: 0,
  tIR: 0,
  vacio1: 0,
  vacio2: 0,

  vacio3: 0,
  valoracionRepercutida: 0,
  valoracionRepercutida_Last: 0,
  valoracionTotal: 0,
  valoracionTotal_Last: 0,
  vidaRes: 0
}

export const rentafijaSgtValidate = (attempt: RentafijaSgt) =>
  rentafijaSgt(attempt) instanceof type.errors ? resetObject(attempt) : attempt
