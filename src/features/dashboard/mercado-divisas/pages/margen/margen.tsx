import { DialogTable } from '@d/common/components/DialogTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  activoPasivo,
  activoPasivoDetailMetadata
} from '@d/common/models/activo-pasivo'
import { MargenFinanciero } from '@d/common/templates/margen-financiero/margen-financiero'
import { DialogDolEur } from '@d/mercado-divisas/dialogs/dialog-doleur'

import { Fetching } from '@/dashboard/common/components/Fetching'

import { margenController } from './margen-controller'

export default () => {
  const {
    activoData,
    activoError,
    dialog1Data,
    isActivoError,
    isActivoPending,
    isPasivoError,
    isPasivoPending,
    loadingDialog,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    pasivoError,
    refetch,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  } = margenController()

  return (
    <Fetching
      errorMessage={activoError?.message ?? pasivoError?.message}
      isError={isActivoError || isPasivoError}
      isFetching={isActivoPending || isPasivoPending}
    >
      <Reload onClick={refetch} />
      <MargenFinanciero
        activo={activoData ?? []}
        onActivoClick={index =>
          onRowActivoPasivo1(activoData ?? [], index, true)
        }
        onPasivoClick={index =>
          onRowActivoPasivo1(pasivoData ?? [], index, false)
        }
        pasivo={pasivoData ?? []}
      />
      <DialogTable
        data={dialog1Data}
        entries={activoPasivo}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, true)}
        open={openDialogActivo1}
        setOpen={setOpenDialogActivo1}
        title='Activo'
      />
      <DialogTable
        data={dialog1Data}
        entries={activoPasivo}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, false)}
        open={openDialogPasivo1}
        setOpen={setOpenDialogPasivo1}
        title='Pasivo'
      />
      {openDialogActivo2.open && (
        <DialogDolEur
          dataDialog={openDialogActivo2}
          setOpen={close =>
            setOpenDialogActivo2(data => ({
              ...data,
              open: close
            }))
          }
          title='Operaciones Depósitos en Divisa Activo'
        />
      )}
      {openDialogPasivo2.open && (
        <DialogDolEur
          dataDialog={openDialogPasivo2}
          setOpen={close =>
            setOpenDialogPasivo2(data => ({
              ...data,
              open: close
            }))
          }
          title='Operaciones Depósitos en Divisa Pasivo'
        />
      )}
      <LoadingDialog open={loadingDialog} />
    </Fetching>
  )
}
