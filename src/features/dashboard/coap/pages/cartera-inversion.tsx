import { DialogTable } from '@d/common/components/DialogTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  type DetallesDerivadosCobertura,
  detallesDerivadosCobertura,
  detallesDerivadosCoberturaMetadata
} from '@d/common/models/detalles-derivados-cobertura'
import { CarteraRepository } from '@d/common/repository/cartera-repository'
import { DetallesDerivdosCoberturaRepository } from '@d/common/repository/detalles-derivados-cobertura-repository'
import { CarteraInversion } from '@d/common/templates/cartera-inversion/cartera-inversion'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export default () => {
  const day = calendarStore.actualDay.use()

  const [openDialog, setOpenDialog] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [dialogData, setDialogData] = useState<DetallesDerivadosCobertura[]>([])

  const { getInvDesglose, getInvResumen } = container.get(CarteraRepository)

  const { getDetallesDerivadosCobertura } = container.get(
    DetallesDerivdosCoberturaRepository
  )

  const {
    data: invSummaryData,
    isFetching: isInvSummaryPending,
    refetch: refetchInvSummary
  } = useQuery({
    queryFn: () => getInvResumen('4'),
    queryKey: ['getINVSummaryRentaFija']
  })

  const {
    data: plrfinvDesgloseData,
    isFetching: isPLRFINVDesglosePending,
    refetch: refetchPLRFINVDesglose
  } = useQuery({
    queryFn: () => getInvDesglose('4'),
    queryKey: ['getPLRFINVDesglose']
  })

  const fetchDetalles = async (rowIndex: number) => {
    setLoadingDialog(true)
    const isin = nth(rowIndex, plrfinvDesgloseData ?? [])?.referencia ?? ''
    const tradeGroup =
      nth(rowIndex, plrfinvDesgloseData ?? [])?.tradeGroup ?? ''
    setDialogData(await getDetallesDerivadosCobertura(isin, tradeGroup))
    setLoadingDialog(false)
    setOpenDialog(true)
  }

  const refetch = () => {
    void refetchInvSummary()
    void refetchPLRFINVDesglose()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return isInvSummaryPending || isPLRFINVDesglosePending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={refetch} />
      <CarteraInversion
        desglose={plrfinvDesgloseData ?? []}
        onClick={fetchDetalles}
        resumen={invSummaryData ?? []}
      />
      <LoadingDialog open={loadingDialog} />
      <DialogTable
        data={dialogData.map(el => ({
          ...el,
          archiveDate: el.archiveDate.split('T')[0] ?? ''
        }))}
        entries={detallesDerivadosCobertura}
        metadata={detallesDerivadosCoberturaMetadata}
        open={openDialog}
        rowsHeaders={[]}
        setOpen={setOpenDialog}
        title='Detalles por operación'
      />
    </>
  )
}
