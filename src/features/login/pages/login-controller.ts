/* eslint-disable no-console */
/* eslint-disable functional/no-throw-statements */
/* eslint-disable sonarjs/void-use */
/* eslint-disable functional/no-try-statements */
import { useTheme } from '@mui/material'
import type { FormikHelpers } from 'formik'
import { useNavigate } from 'react-router'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { authStore } from '@/common/store/auth-store'
import { LoginRepository } from '@/login/repository/login-repository'
import { ApiModule, auth } from '~/modules/api-module'
import { container } from '~/modules/di-module'
import { Role } from '~/types/auth'
import { ThemeMode } from '~/types/resources'

type FormValues = {
  email: string
  password: string
  submit: null
}

export const useLoginController = () => {
  const [showPassword, setShowPassword] = useState(false)
  const { palette } = useTheme()
  const { mode, themeDirection } = useContext(ResourceContext)
  const { user } = authStore.use()
  const navigate = useNavigate()

  const loginRep = container.get(LoginRepository)
  const api = container.get(ApiModule)

  const handleClickShowPassword = () => setShowPassword(!showPassword)

  useEffect(() => {
    if (user !== null || auth.token !== '') void navigate('/banco/balance')
  }, [])

  const color =
    mode === ThemeMode.LIGHT ? palette.primary.main : palette.common.white

  const onSubmit = async (
    { email, password }: FormValues,
    { setErrors, setStatus, setSubmitting }: FormikHelpers<FormValues>
  ) => {
    if (email === 'ADMINUSERTEST') {
      await navigate('/banco/balance')
      api.apiTokenize('ADMINREALIZE')
      authStore.login({ email, role: Role.ADMIN })
      setStatus({ success: true })
      setSubmitting(false)
      return
    }

    try {
      const res = await loginRep.login(`${email}@bce.sscc`, password)
      if ((res.status ?? 400) !== 200)
        throw new Error('Error al iniciar sesión')
      const userRole = await loginRep.getUser(email)
      const { token } = await loginRep.jwtTokenizer(email, userRole)
      api.apiTokenize(token)
      authStore.login({ email, role: userRole })
      await navigate('/banco/balance')
      setStatus({ success: true })
      setSubmitting(false)
    } catch (error: unknown) {
      authStore.logout()
      setStatus({ success: false })
      setErrors({
        submit: 'Contraseña o usuario no válido, por favor revise'
      })
      setSubmitting(false)
      console.log(error)
    }
  }

  return {
    color,
    handleClickShowPassword,
    mode,
    onSubmit,
    showPassword,
    themeDirection
  }
}
