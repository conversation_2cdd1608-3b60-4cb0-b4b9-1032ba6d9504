import {
  type RentafijaSgt,
  rentaFijaSgtDefault
} from '@d/admin/models/rentafija-sgt'
import { RentafijaSGTRepository } from '@d/admin/repositories/rentafija-sgt-repository'
import { useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, prop } from 'rambdax'
import type { ChangeEvent } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const rentaFijaSgtController = () => {
  const day = calendarStore.formattedDay.use()

  const {
    deleteRentaFijaSGT,
    getRentaFijaSGT,
    getRentaFijaSGTGroups,
    insertRentaFijaSGT,
    updateRentaFijaSGT
  } = container.get(RentafijaSGTRepository)

  const selectedData = useRef(rentaFijaSgtDefault)
  const selectedIndex = useRef<number | null>(0)

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const [filterRef, setFilterRef] = useState('')
  const [date, setDate] = useState<Dayjs | null>(dayjs(day))

  const {
    data: rentaFijaSGTData,
    isFetching: isRentaFijaSGTPending,
    refetch: refetchRentaFijaSGT
  } = useQuery({
    queryFn: () => getRentaFijaSGT(date?.format('YYYY-MM-DD') ?? day),
    queryKey: ['getRentaFijaSGT']
  })

  const {
    data: rentaFijaSGTGroupsData,
    isFetching: isRentaFijaSGTGroupsPending,
    refetch: refetchRentaFijaSGTGroups
  } = useQuery({
    queryFn: () =>
      getRentaFijaSGTGroups(date?.format('YYYY-MM-DD') ?? day, filterRef),
    queryKey: ['getRentaFijaSGTGroups']
  })

  const handleRefTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFilterRef(event.currentTarget.value),
    []
  )

  const filterRentaFijaSGTData = useMemo(() => {
    if (filterRef.trim() === '') return rentaFijaSGTData

    return (rentaFijaSGTData ?? []).filter(item => {
      const value = prop('referencia', item)
      return value.toLowerCase().includes(filterRef.toLowerCase())
    })
  }, [filterRef, rentaFijaSGTData, date])

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteRentaFijaSGT(selectedData.current.iD.toString())
      setLoadingDialog(false)
      void refetchRentaFijaSGT()
    }
  }

  const submitAction = async (
    rt: RentafijaSgt,
    _: FormikHelpers<RentafijaSgt>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)

    await (selectedIndex.current === null
      ? insertRentaFijaSGT(rt)
      : updateRentaFijaSGT(rt))

    setLoadingDialog(false)
    void refetchRentaFijaSGT()
  }

  const refetch = () => {
    void refetchRentaFijaSGT()
    void refetchRentaFijaSGTGroups()
  }

  useEffect(() => {
    if (filterRef.length === 12 || filterRef.length === 0)
      void refetchRentaFijaSGTGroups()
  }, [filterRef])

  useEffect(() => {
    refetch()
  }, [date])

  return {
    date,
    deleteAction,
    filterRentaFijaSGTData,
    handleRefTextChange,
    isRentaFijaSGTGroupsPending,
    isRentaFijaSGTPending,
    loadingDialog,
    openCreateEditDialog,
    openDeleteDialog,
    refetch,
    rentaFijaSGTData,
    rentaFijaSGTGroupsData,
    selectedData,
    selectedIndex,
    setDate,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  }
}
