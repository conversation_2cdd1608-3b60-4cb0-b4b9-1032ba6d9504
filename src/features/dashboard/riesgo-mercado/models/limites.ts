/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const limites = type({
  cobertura: 'number',
  consumo: 'number',
  fecha: 'string',
  limiteConsumoID: 'number',
  limiteDisponible: 'number',
  limiteDispuesto: 'number',
  limiteID: 'number',
  nombreCorto: 'string',
  nombreLargo: 'string'
})

export const limitesMetadata = new Set([
  {
    header: 'Límites',
    key: 'nombreLargo'
  },
  {
    header: 'Límite',
    key: 'limiteDispuesto'
  },
  {
    header: 'Consumo',
    key: 'consumo'
  },
  {
    header: 'Disponible',
    key: 'limiteDisponible'
  },
  {
    header: '% Utilizado',
    key: 'cobertura'
  }
])

export type Limites = typeof limites.infer

export const limitesDefault: Limites = {
  cobertura: 0,
  consumo: 0,
  fecha: '',
  limiteConsumoID: 0,
  limiteDisponible: 0,
  limiteDispuesto: 0,
  limiteID: 0,
  nombreCorto: '',
  nombreLargo: ''
}

export const limitesValidate = (attempt?: Limites) =>
  limites(attempt) instanceof type.errors ? resetObject(attempt) : attempt
