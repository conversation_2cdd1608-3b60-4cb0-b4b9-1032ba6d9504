import type { ApexOptions } from 'apexcharts'

import { numberFormat } from '~/utils/format-number'

export const limitesChartOpts: ApexOptions = {
  chart: { id: 'apex-limites-risk' },
  dataLabels: { enabled: false },
  plotOptions: { bar: { borderRadius: 4 } },
  xaxis: {
    categories: [''],
    labels: { rotate: -45, style: { fontSize: '12px' } }
  },
  yaxis: {
    labels: {
      formatter: (value: number) => `${numberFormat(value * 100, 1)}%`
    }
  }
}
