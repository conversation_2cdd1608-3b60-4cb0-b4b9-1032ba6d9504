import {
  index,
  layout,
  route,
  type RouteConfig
} from '@react-router/dev/routes'

import { adminRouter } from './features/dashboard/admin/routes'
import { bancaCorporativaRouter } from './features/dashboard/banca-corporativa/routes'
import { bancoRouter } from './features/dashboard/banco/routes'
import { coapRouter } from './features/dashboard/coap/routes'
import { derivadosRouter } from './features/dashboard/derivados/routes'
import { mercadoDivisasRouter } from './features/dashboard/mercado-divisas/routes'
import { mercadoMonetarioRouter } from './features/dashboard/mercado-monetario/routes'
import { rentaFijaRouter } from './features/dashboard/renta-fija/routes'
import { riesgoMercadoRouter } from './features/dashboard/riesgo-mercado/routes'
import { saldosTesorerosRouter } from './features/dashboard/saldos-tesoreros/routes'

export default [
  index('features/login/pages/login.tsx'),
  route('*', 'redirect.tsx'),
  layout('features/dashboard/common/layout/index.tsx', [
    route('/docs', 'features/dashboard/common/pages/docs.tsx'),
    ...bancoRouter,
    ...bancaCorporativaRouter,
    ...coapRouter,
    ...adminRouter,
    ...saldosTesorerosRouter,
    ...derivadosRouter,
    ...rentaFijaRouter,
    ...mercadoDivisasRouter,
    ...riesgoMercadoRouter,
    ...mercadoMonetarioRouter
  ])
] satisfies RouteConfig
