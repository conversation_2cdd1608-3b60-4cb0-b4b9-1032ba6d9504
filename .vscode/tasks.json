{"version": "2.0.0", "tasks": [{"group": {"kind": "build", "isDefault": true}, "type": "shell", "command": "bun", "args": ["tsc", "-p", "tsconfig.json", "--watch"], "label": "TypeScript", "problemMatcher": ["$tsc-watch"], "runOptions": {"runOn": "folderOpen"}, "presentation": {"reveal": "always", "panel": "shared"}}, {"label": "<PERSON>", "type": "shell", "command": "./node_modules/.bin/react-router dev", "isBackground": true}]}