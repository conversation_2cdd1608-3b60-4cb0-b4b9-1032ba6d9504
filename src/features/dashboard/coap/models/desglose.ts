/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const desglose = type({
  equity: 'string',
  valorMercado: 'number'
})

export const desgloseMetadata = new Set([
  {
    header: 'Equity',
    key: 'equity'
  },
  {
    header: '<PERSON><PERSON> de Mercado',
    key: 'valorMercado'
  }
])

export type Desglose = typeof desglose.infer

export const desgloseValidate = (attempt?: Desglose) =>
  desglose(attempt) instanceof Error ? resetObject(desglose.infer) : attempt
