/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const margenFinancieroInforme = type({
  accountingAreaID: 'string',
  ajusteDia: 'number?',
  ajusteIntDia: 'number?',
  ajusteMargen: 'number?',
  ajuste<PERSON>aldo: 'number?',
  ajustesMes: 'number?',
  balance: 'string',
  costeDia: 'number',
  currencyID: 'string',
  epigrafeID: 'number',
  fecha: 'string',
  intAnual: 'number?',
  intDia: 'number',
  intMes: 'number?',
  mfgID: 'number',
  nombre: 'string',
  nombreInformeGlobal: 'string',
  orden: 'number',
  peso: 'number',
  producto: 'string',
  rentabilidadAnual: 'number?',
  rentabilidadDia: 'number?',
  rentabilidadMes: 'number?',
  saldo: 'number',
  saldoMedioAnual: 'number?',
  saldoMedioMes: 'number?',
  tradeGroup: 'string'
})

export const margenFinancieroInformeMetadata = new Set([
  {
    header: 'Nombre',
    key: 'nombre'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Saldo mes',
    key: 'saldoMedioMes'
  },
  {
    header: 'Saldo año',
    key: 'saldoMedioAnual'
  },
  {
    header: 'Int dia',
    key: 'intDia'
  },
  {
    header: 'Int mes',
    key: 'intMes'
  },
  {
    header: 'Int anual',
    key: 'intAnual'
  },
  {
    header: 'Margen dia',
    key: 'rentabilidadDia'
  },
  {
    header: 'M. Mes',
    key: 'rentabilidadMes'
  },
  {
    header: 'M. Anual',
    key: 'rentabilidadAnual'
  }
])

export const margenFinancieroEdicionMetadata = new Set([
  {
    header: 'Nombre',
    key: 'nombre'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Ajuste saldo',
    key: 'ajusteSaldo'
  },
  {
    header: 'Ajuste Intereses',
    key: 'ajusteIntDia'
  },
  {
    header: 'Ajuste día',
    key: 'ajusteDia'
  },
  {
    header: 'Ajuste Margen',
    key: 'ajusteMargen'
  },
  {
    header: 'Int Mes',
    key: 'intMes'
  },
  {
    header: 'Int Anual',
    key: 'intAnual'
  },
  {
    header: 'M. Anual',
    key: 'rentabilidadAnual'
  }
])

export const margenFinancieroAjusteMetadata = new Set([
  {
    header: 'Nombre',
    key: 'nombre'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Balance',
    key: 'balance'
  },
  {
    header: 'Ajuste Saldo',
    key: 'ajusteSaldo'
  },
  {
    header: 'Ajuste Intereses',
    key: 'ajusteIntDia'
  },
  {
    header: 'Ajuste día',
    key: 'ajusteDia'
  },
  {
    header: 'Ajuste Mes',
    key: 'ajusteMes'
  },
  {
    header: 'Ajuste Margen',
    key: 'ajusteMargen'
  }
])

export type MargenFinancieroInforme = typeof margenFinancieroInforme.infer

export const margenFinancieroInformeDefault: MargenFinancieroInforme = {
  accountingAreaID: '',
  ajusteDia: 0,
  ajusteIntDia: 0,
  ajusteMargen: 0,
  ajusteSaldo: 0,
  ajustesMes: 0,
  balance: '',
  costeDia: 0,
  currencyID: '',
  epigrafeID: 0,
  fecha: '',
  intAnual: 0,
  intDia: 0,
  intMes: 0,
  mfgID: 0,
  nombre: '',
  nombreInformeGlobal: '',
  orden: 0,
  peso: 0,
  producto: '',
  rentabilidadAnual: 0,
  rentabilidadDia: 0,
  rentabilidadMes: 0,
  saldo: 0,
  saldoMedioAnual: 0,
  saldoMedioMes: 0,
  tradeGroup: ''
}

export const margenFinancieroInformeValidate = (
  attempt?: MargenFinancieroInforme
) =>
  margenFinancieroInforme(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
