/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const carteraResumen = type({
  plusvalia: 'number = 0',
  tradeGroup: 'string?',
  valorContable: 'number?',
  valordeMercado: 'number?'
})

export const carteraResumenMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Valor Contable',
    key: 'valorContable'
  },
  {
    header: 'Valor de Mercado',
    key: 'valordeMercado'
  },
  {
    header: 'Plusvalía',
    key: 'plusvalia'
  }
])

export type CarteraResumen = typeof carteraResumen.infer

export const carteraResumenValidate = (attempt?: CarteraResumen) =>
  carteraResumen(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt

export const carteraDesglose = type({
  ajusteInflacion: 'number = 0',
  areaID: 'number = 0',
  fechaVto: 'string = ""',
  nominal: 'number = 0',
  plusvalia: 'number = 0',
  plusvaliaDerivados: 'number = 0',
  plusvaliaTotal: 'number = 0',
  precioCierre: 'number = 0',
  referencia: 'string = ""',
  tIR: 'number = 0',
  tradeGroup: 'string = ""',
  valorContable: 'number = 0',
  valordeMercado: 'number = 0'
})

export const carteraDesgloseMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Referencia',
    key: 'referencia'
  },
  {
    header: 'Fecha Vto',
    key: 'fechaVto'
  },
  {
    header: 'Nominal',
    key: 'nominal'
  },
  {
    header: 'Valor Contable',
    key: 'valorContable'
  },
  {
    header: 'TIR',
    key: 'tIR'
  },
  {
    header: 'Valor de Mercado',
    key: 'valordeMercado'
  },
  {
    header: 'Precio Cierre',
    key: 'precioCierre'
  },
  {
    header: 'Ajuste Inflación',
    key: 'ajusteInflacion'
  },
  {
    header: 'Plusvalía (inc. Op. Cobertura)',
    key: 'plusvalia'
  },
  {
    header: 'Plusvalía Derivados',
    key: 'plusvaliaDerivados'
  },
  {
    header: 'Plusvalía Total',
    key: 'plusvaliaTotal'
  }
])

export type CarteraDesglose = typeof carteraDesglose.infer

export const carteraDesgloseValidate = (attempt?: CarteraDesglose) =>
  carteraDesglose(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
