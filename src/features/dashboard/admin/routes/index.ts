import { layout, route } from '@react-router/dev/routes'

export const adminRouter = [
  route(
    'admin/ajustes-derivados',
    'features/dashboard/admin/pages/ajustes-derivados.tsx'
  ),
  route(
    'admin/carga-ficheros',
    'features/dashboard/admin/pages/carga-ficheros.tsx'
  ),
  route('admin/ajustes', 'features/dashboard/admin/pages/ajustes/ajustes.tsx'),
  layout('features/dashboard/admin/layouts/calcular-margen.tsx', [
    route(
      'admin/calcular-margen/calcular-margen',
      'features/dashboard/admin/pages/calcular-margen/calcular-margen/calcular-margen.tsx'
    ),
    route(
      'admin/calcular-margen/ficheros-sgt',
      'features/dashboard/admin/pages/calcular-margen/ficheros-sgt.tsx'
    ),
    route(
      'admin/calcular-margen/importar-plvar',
      'features/dashboard/admin/pages/calcular-margen/importar-plvar.tsx'
    ),
    route(
      'admin/calcular-margen/historico-benchmarks',
      'features/dashboard/admin/pages/calcular-margen/historico-benchmarks.tsx'
    ),
    route(
      'admin/calcular-margen/ratio-liquidez',
      'features/dashboard/admin/pages/calcular-margen/ratio-liquidez.tsx'
    ),
    route(
      'admin/calcular-margen/jobs-desatendidos',
      'features/dashboard/admin/pages/calcular-margen/jobs-desatendidos.tsx'
    )
  ]),

  layout('features/dashboard/admin/layouts/configuracion/index.tsx', [
    layout('features/dashboard/admin/layouts/configuracion/filtros.tsx', [
      route(
        'admin/configuracion/filtros/trade-group',
        'features/dashboard/admin/pages/configuracion/filtros/trade-group.tsx'
      ),
      route(
        'admin/configuracion/filtros/contrapartida',
        'features/dashboard/admin/pages/configuracion/filtros/contrapartida.tsx'
      ),
      route(
        'admin/configuracion/filtros/renta-fija',
        'features/dashboard/admin/pages/configuracion/filtros/renta-fija.tsx'
      ),
      route(
        'admin/configuracion/filtros/operaciones',
        'features/dashboard/admin/pages/configuracion/filtros/operaciones.tsx'
      ),
      route(
        'admin/configuracion/filtros/cuentas-orden',
        'features/dashboard/admin/pages/configuracion/filtros/cuentas-orden.tsx'
      ),
      route(
        'admin/configuracion/filtros/estados',
        'features/dashboard/admin/pages/configuracion/filtros/estados.tsx'
      )
    ]),
    layout('features/dashboard/admin/layouts/configuracion/organizacion.tsx', [
      layout(
        'features/dashboard/admin/layouts/configuracion/organizacion-tradegroups.tsx',
        [
          route(
            'admin/configuracion/organizacion/tradegroups/trade',
            'features/dashboard/admin/pages/configuracion/organizacion/trade-group/trade.tsx'
          ),
          route(
            'admin/configuracion/organizacion/tradegroups/equivalencia',
            'features/dashboard/admin/pages/configuracion/organizacion/trade-group/equivalencia.tsx'
          ),
          route(
            'admin/configuracion/organizacion/tradegroups/subinforme',
            'features/dashboard/admin/pages/configuracion/organizacion/trade-group/subinforme.tsx'
          )
        ]
      ),
      route(
        'admin/configuracion/organizacion/areas-negocio',
        'features/dashboard/admin/pages/configuracion/organizacion/areas-negocio.tsx'
      ),
      route(
        'admin/configuracion/organizacion/portafolios',
        'features/dashboard/admin/pages/configuracion/organizacion/portafolios.tsx'
      ),
      route(
        'admin/configuracion/organizacion/producto',
        'features/dashboard/admin/pages/configuracion/organizacion/producto.tsx'
      ),
      route(
        'admin/configuracion/organizacion/limites',
        'features/dashboard/admin/pages/configuracion/organizacion/limites.tsx'
      ),
      route(
        'admin/configuracion/organizacion/epigrafes-informe',
        'features/dashboard/admin/pages/configuracion/organizacion/epigrafes-informe.tsx'
      ),
      route(
        'admin/configuracion/organizacion/parametros-generales',
        'features/dashboard/admin/pages/configuracion/organizacion/parametros-generales.tsx'
      ),
      route(
        'admin/configuracion/organizacion/perfiles-usuarios',
        'features/dashboard/admin/pages/configuracion/organizacion/perfiles-usuarios.tsx'
      )
    ])
  ]),

  layout('features/dashboard/admin/layouts/input-manual/index.tsx', [
    layout('features/dashboard/admin/layouts/input-manual/ajustes.tsx', [
      route(
        'admin/input-manual/ajustes/margen-financiero',
        'features/dashboard/admin/pages/input-manual/margen-financiero.tsx'
      ),
      route(
        'admin/input-manual/ajustes/ajustes-aplicados',
        'features/dashboard/admin/pages/input-manual/ajustes-aplicados.tsx'
      ),
      route(
        'admin/input-manual/ajustes/renta-fija-sgt',
        'features/dashboard/admin/pages/input-manual/renta-fija-sgt/renta-fija-sgt.tsx'
      )
    ]),
    layout('features/dashboard/admin/layouts/input-manual/otros-saldos.tsx', [
      route(
        'admin/input-manual/otros-saldos/banco-espana',
        'features/dashboard/admin/pages/input-manual/banco-espana.tsx'
      ),
      route(
        'admin/input-manual/otros-saldos/exceso',
        'features/dashboard/admin/pages/input-manual/exceso.tsx'
      ),
      route(
        'admin/input-manual/otros-saldos/otros',
        'features/dashboard/admin/pages/input-manual/otros.tsx'
      )
    ]),
    route(
      'admin/input-manual/fondo-arcano',
      'features/dashboard/admin/pages/input-manual/fondo-arcano.tsx'
    )
  ]),
  layout('features/dashboard/admin/layouts/config-manual.tsx', [
    route(
      'admin/config-manual/ajustes-trading',
      'features/dashboard/admin/pages/config-manual/trading.tsx'
    ),
    route(
      'admin/config-manual/derivados',
      'features/dashboard/admin/pages/config-manual/trading-derivados.tsx'
    ),
    route(
      'admin/config-manual/pool',
      'features/dashboard/admin/pages/config-manual/pool/pool.tsx'
    )
  ])
]
