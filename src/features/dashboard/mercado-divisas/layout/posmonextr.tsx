/* eslint-disable sonarjs/void-use */
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

const route = '/mercado-divisas/posmonextr'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/mesa-contado`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/mesa-plazo`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/total`)
        setMenuId(2)
        break
      }
      case 3: {
        if (val !== menuId) void navigate(`${route}/pylhistorico`)
        setMenuId(3)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('mesa-contado')) setMenuId(0)
    if (path.includes('mesa-plazo')) setMenuId(1)
    if (path.includes('total')) setMenuId(2)
    if (path.includes('pylhistorico')) setMenuId(3)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <InputLabel id='demo-simple-select-label'>Menu</InputLabel>
                <Select
                  id='demo-simple-select'
                  label='Menu'
                  labelId='demo-simple-select-label'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Mesa Contado</MenuItem>
                  <MenuItem value={1}>Mesa Plazo</MenuItem>
                  <MenuItem value={2}>Total</MenuItem>
                  <MenuItem value={3}>P&L Histórico</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Outlet />
        </Grid>
      </Grid>
    </Container>
  )
}
