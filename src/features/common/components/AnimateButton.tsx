import { motion, useCycle } from 'framer-motion'
import type { FC, ReactNode } from 'react'

type Direction = 'down' | 'left' | 'right' | 'up'

const getOffset = (direction: Direction, offset: number) => {
  if (direction === 'up' || direction === 'left') {
    return [offset, 0]
  } else if (direction === 'down') {
    return [0, offset]
  } else {
    return [0, offset]
  }
}

export const AnimateButton: FC<{
  children?: ReactNode
  direction?: Direction
  offset?: number
  scale?: {
    hover: number | string | undefined
    tap: number | string | undefined
  }
  type?: 'rotate' | 'scale' | 'slide'
}> = ({
  children,
  direction = 'right',
  offset = 10,
  scale = { hover: 1.05, tap: 0.954 },
  type = 'scale'
}) => {
  const [offset1, offset2] = getOffset(direction, offset)

  const [x, cycleX] = useCycle(offset1, offset2)
  const [y, cycleY] = useCycle(offset1, offset2)

  const directionSense = direction === 'up' || direction === 'left'

  switch (type) {
    case 'rotate': {
      return (
        <motion.div
          animate={{ rotate: 360 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatDelay: 0,
            repeatType: 'loop'
          }}
        >
          {children}
        </motion.div>
      )
    }
    case 'slide': {
      return (
        <motion.div
          animate={directionSense ? { y: y ?? '' } : { x: x ?? '' }}
          onHoverEnd={() => (directionSense ? cycleY() : cycleX())}
          onHoverStart={() => (directionSense ? cycleY() : cycleX())}
        >
          {children}
        </motion.div>
      )
    }

    // eslint-disable-next-line unicorn/no-useless-switch-case
    case 'scale':
    default: {
      return (
        <motion.div
          whileHover={{ scale: scale.hover }}
          whileTap={{ scale: scale.tap }}
        >
          {children}
        </motion.div>
      )
    }
  }
}
