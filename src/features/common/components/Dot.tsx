import { Box, type CSSObject, useTheme } from '@mui/material'
import type { FC } from 'react'

import { getColors } from '~/resources/helpers/get-colors'
import type { ColorProps } from '~/types/extended'

export const Dot: FC<{
  color?: ColorProps
  size?: number
  sx?: CSSObject
  variant?: string
}> = ({ color = 'primary', size = 8, sx, variant }) => {
  const theme = useTheme()
  const colors = getColors(theme, color)
  const { main } = colors

  return (
    <Box
      component='span'
      sx={{
        bgcolor: variant === 'outlined' ? '' : main,
        borderRadius: '50%',
        height: size,
        width: size,
        ...(variant === 'outlined' && {
          border: `1px solid ${main}`
        }),
        ...sx
      }}
    />
  )
}
