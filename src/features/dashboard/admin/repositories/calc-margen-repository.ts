/* eslint-disable sonarjs/use-type-alias */
import { CalcMargen } from '@d/admin/models/calc-margen'
import { LogsCalcMargen } from '@d/admin/models/logs-calcmargen'
import { Referencia } from '@d/admin/models/referencia'
import { ResumenImportacion } from '@d/admin/models/resumen-importacion'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class CalcMargenRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/calcmargenfinanciero'

  readonly generarMargenFinanciero = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/${this.attachDate()}/1`
    )

  readonly getLogs = () =>
    this.api.client
      .get<LogsCalcMargen[]>(`${this.route}/mf2logs`)
      .then(apires => apires.data)
      .then(data => data ?? [])
  readonly getReferencia = () =>
    this.api.client
      .get<Referencia[]>(`${this.route}/tasareferenciaaplicada`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getResumenImportacion = () =>
    this.api.client
      .get<ResumenImportacion[]>(`${this.route}/resumenimportacion`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getUltimaImportacion = () =>
    this.api.client
      .get<CalcMargen[]>(`${this.route}/ultimaimportacion`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly setFundingDate = (fecha: string, tasa: string, spread: string) =>
    this.api.client.post<{ message: string }>(
      `fundingRate/${fecha}/M/${tasa}/${spread}/BENCHMARK`
    )

  readonly setFundingDateBase = (fecha: string, rate: number) =>
    this.api.client.post<{ message: string }>(
      `fundingRate/${fecha}/A/${(100 * (rate - 0.00085)).toString()}/8.5/ESTR`
    )

  readonly setImportbnd = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/importbnd/${this.attachDate()}/3`
    )

  readonly setImportdep = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/importdep/${this.attachDate()}/3`
    )

  readonly setImportDeriv = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/importswp/${this.attachDate()}/3`
    )

  readonly setImportotros = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/importotrossaldos/${this.attachDate()}/3`
    )

  readonly setImportPrestamos = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/importlnd/${this.attachDate()}/3`
    )

  readonly setImportrep = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/importrep/${this.attachDate()}/3`
    )

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
