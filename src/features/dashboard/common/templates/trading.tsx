import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import {
  type Trading,
  trading,
  tradingMetadata
} from '@d/common/models/trading'
import { Container, Grid, Paper } from '@mui/material'
import type { FC } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { paperStyle } from '~/resources/config/paper'

export const TradingTemplate: FC<{
  resumen: Trading[]
}> = ({ resumen }) => (
  <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
    <Grid container spacing={3}>
      <Grid item xs={8}>
        <TitleSubtitle title='Resumen Deuda y Letras' />
        <Paper elevation={10} sx={paperStyle}>
          <GenericTable
            data={resumen}
            entries={trading}
            metadata={tradingMetadata}
            noTotal={false}
          />
        </Paper>
        {/* coommented while checking, according to mf2 new struct */}
        {/* <Grid item xs={4}>
          <div style={{width: '100%', display: 'flex', alignItems: 'start'}}>
            <h3> Pos Equivalente (Nacional)</h3>
          </div>
          <Paper elevation={10} sx={ paperStyle }>
            <img src="https://www.w3schools.com/images/lamp.jpg" width="32" height="32" alt='pos equivalente'></img>
          </Paper>
        </Grid>

        <Grid item xs={8}>
          <div style={{width: '100%', display: 'flex', alignItems: 'start'}}>
            <Button variant="contained" startIcon={<PlusCircleOutlined />}>
              Nuevo
            </Button>
          </div>
          <br></br>
          <Paper elevation={10} sx={ paperStyle }>
          </Paper>
        </Grid>

        <Grid item xs={4}>
          <div style={{width: '100%', display: 'flex', alignItems: 'start'}}>
            <h3> Derivados</h3>
          </div>
          <Paper elevation={10} sx={ paperStyle }>
            <TableGenerator tittle={''} headers={headersDerivados} rows={[]} rowsHeaders={rowHeadDerivados} />
          </Paper>
        </Grid> */}
      </Grid>
    </Grid>
  </Container>
)
