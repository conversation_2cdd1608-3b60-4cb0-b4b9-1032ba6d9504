/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const incumplimientos = type({
  amberTranche: 'string',
  contrapartida: 'number',
  greenTranche: 'number',
  incumplimientos: 'string',
  numObs: 'number',
  redTranche: 'string'
})

export const incumplimientosMetadata = new Set([
  {
    header: 'Contrapartida',
    key: 'contrapartida'
  },
  {
    header: 'Num Obs',
    key: 'numObs'
  },
  {
    header: 'Incumplimientos',
    key: 'incumplimientos'
  },
  {
    header: 'Green Tranche',
    key: 'greenTranche'
  },
  {
    header: 'Amber Tranche',
    key: 'amberTranche'
  },
  {
    header: 'Red Tranche',
    key: 'redTranche'
  }
])

export type Incumplimientos = typeof incumplimientos.infer
export const incumplimientosValidate = (attempt?: Incumplimientos) =>
  incumplimientos(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
