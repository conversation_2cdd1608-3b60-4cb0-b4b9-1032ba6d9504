import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import { IconButton } from '@c/components/IconButton'
import { Customization } from '@d/common/components/customization'
import { Box, Toolbar, useMediaQuery } from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { lazy, useContext } from 'react'

import { AuthContext } from '@/common/contexts/AuthContext'
import { ResourceContext } from '@/common/contexts/ResourceContext'
import { menuStore } from '@/common/store/menu-store'
import { Role } from '~/types/auth'
import { MenuOrientation, ThemeMode } from '~/types/resources'

import { Calendar } from './Calendar'
import { Notification } from './Notification'
import { Profile } from './Profile'

const Available = lazy(() =>
  import('./Available').then(comp => ({ default: comp.Available }))
)

const Localization = lazy(() =>
  import('./Localization').then(comp => ({ default: comp.Localization }))
)

const MegaMenuSection = lazy(() =>
  import('./MegaMenuSection').then(comp => ({ default: comp.MegaMenuSection }))
)

const Message = lazy(() =>
  import('./Message').then(comp => ({ default: comp.Message }))
)

export const HeaderContent = () => {
  const { user } = useContext(AuthContext)
  const { breakpoints, palette } = useTheme()
  const downLG = useMediaQuery(breakpoints.down('lg'))
  const { menuOrientation } = useContext(ResourceContext)
  const { drawerOpen } = menuStore.use()

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG

  /*menuOrientation === MenuOrientation.HORIZONTAL && !downLG && (
        <DrawerHeader open={true} />
      )*/

  const iconBackColorOpen =
    palette.mode === ThemeMode.DARK ? 'grey.200' : 'grey.300'
  const iconBackColor =
    palette.mode === ThemeMode.DARK ? 'background.default' : 'grey.100'

  return (
    <Toolbar>
      {!isHorizontal && (
        <IconButton
          aria-label='open drawer'
          color='secondary'
          edge='start'
          onClick={() => menuStore.openDrawer(!drawerOpen)}
          sx={{
            bgcolor: drawerOpen ? iconBackColorOpen : iconBackColor,
            color: 'text.primary',
            ml: { lg: -2, xs: 0 }
          }}
          variant='light'
        >
          {drawerOpen ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
        </IconButton>
      )}
      <Box sx={{ ml: { md: 1, xs: 0 }, width: '100%' }}></Box>
      {!downLG && user?.role === Role.ADMIN && <MegaMenuSection />}
      {!downLG && user?.role === Role.ADMIN && <Localization />}
      {downLG && <Box sx={{ ml: 1, width: '100%' }} />}
      {user?.role === Role.ADMIN && <Notification />}
      <Available />
      <Calendar />
      {user?.role === Role.ADMIN && <Message />}
      <Customization />
      {!downLG && <Profile />}
    </Toolbar>
  )
}
