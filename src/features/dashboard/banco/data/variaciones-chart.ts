import type { ApexOptions } from 'apexcharts'

import { myLocal } from '~/lang'
import { apexPaletteColors } from '~/resources/config/colors'
import { numberFormat } from '~/utils/format-number'

export const variacionesChartOpt: ApexOptions = {
  chart: {
    defaultLocale: 'es',
    id: 'apex-margen-day-lag',
    locales: myLocal
  },
  colors: apexPaletteColors,
  dataLabels: {
    enabled: false
  },
  xaxis: {
    type: 'datetime' as const
  },
  yaxis: {
    labels: {
      formatter: value => `${numberFormat(value / 1000, 2)}k`
    }
  }
}
