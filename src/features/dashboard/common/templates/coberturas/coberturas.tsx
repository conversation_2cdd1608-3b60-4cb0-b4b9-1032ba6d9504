import { FileExcelOutlined, SearchOutlined } from '@ant-design/icons'
import { TitleSubtitle } from '@c/components/TitleSubtitle'
import { ErrorMessage } from '@d/common/components/ErrorMessage'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  Typography
} from '@mui/material'
import type { FC } from 'react'

import { paperStyle } from '~/resources/config/paper'

import {
  type Bonos,
  bonos,
  bonosMetadata,
  type Derivados,
  derivados,
  derivadosMetadata
} from '../../models/coberturas'
import { coberturasController } from './coberturas-controller'

export const Coberturas: FC<{
  bonosData: Bonos[]
  derivadosData: Derivados[]
  error?: string
  isError: boolean
  onBonosClick: (isin: string, book: string) => void
}> = ({ bonosData, derivadosData, error, isError, onBonosClick }) => {
  const {
    exportBonosCSV,
    exportDerivadosCSV,
    filterBonosData,
    filterDerivadosData,
    handleBonosChange,
    handleBonosTextChange,
    handleDerivadosChange,
    handleDerivadosTextChange,
    menuIdBonos,
    menuIdDerivados,
    onBonosFilteredClick
  } = coberturasController(bonosData, derivadosData, onBonosClick)

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TitleSubtitle title='Activos Cubiertos' />
          <Paper elevation={10} sx={paperStyle}>
            {bonosData.length > 0 && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  height: '70px',
                  justifyContent: 'space-between',
                  pl: '30px',
                  pr: '40px',
                  width: '100%'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    mt: 2
                  }}
                >
                  <Typography sx={{ mt: 1 }} variant='subtitle1'>
                    Buscar en:
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      minWidth: 200,
                      ml: 2
                    }}
                  >
                    <FormControl fullWidth>
                      <Select
                        id='demo-simple-select'
                        onChange={handleBonosChange}
                        value={menuIdBonos.toString()}
                      >
                        {[...bonosMetadata].map((data, index) => (
                          <MenuItem key={index} value={index}>
                            {data.header}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        ml: { md: 1, xs: 0 }
                      }}
                    >
                      <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                        <OutlinedInput
                          aria-describedby='header-search-text'
                          endAdornment={
                            <InputAdornment position='end' sx={{ mr: -0.5 }}>
                              <SearchOutlined />
                            </InputAdornment>
                          }
                          id='header-search'
                          inputProps={{ 'aria-label': 'weight' }}
                          onChange={handleBonosTextChange}
                          placeholder='Buscar'
                        />
                      </FormControl>
                    </Box>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '40px',
                    mt: 2
                  }}
                >
                  <Button
                    onClick={exportBonosCSV}
                    startIcon={<FileExcelOutlined />}
                    variant='contained'
                  >
                    Descargar excel
                  </Button>
                </Box>
              </Box>
            )}
            {isError ? (
              <ErrorMessage message={error} />
            ) : (
              <GenericTable
                data={filterBonosData}
                entries={bonos}
                metadata={bonosMetadata}
                onRowClick={onBonosFilteredClick}
              />
            )}
          </Paper>
        </Grid>
        <Grid item xs={12}>
          <TitleSubtitle title='Desglose por activos' />
          <Paper elevation={10} sx={paperStyle}>
            {derivadosData.length > 0 && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  height: '70px',
                  justifyContent: 'space-between',
                  pl: '30px',
                  pr: '40px',
                  width: '100%'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    mt: 2
                  }}
                >
                  <Typography sx={{ mt: 1 }} variant='subtitle1'>
                    Buscar en:
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      minWidth: 200,
                      ml: 2
                    }}
                  >
                    <FormControl fullWidth>
                      <Select
                        id='demo-simple-select'
                        onChange={handleDerivadosChange}
                        value={menuIdDerivados.toString()}
                      >
                        {[...derivadosMetadata].map((data, index) => (
                          <MenuItem key={index} value={index}>
                            {data.header}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        ml: { md: 1, xs: 0 }
                      }}
                    >
                      <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                        <OutlinedInput
                          aria-describedby='header-search-text'
                          endAdornment={
                            <InputAdornment position='end' sx={{ mr: -0.5 }}>
                              <SearchOutlined />
                            </InputAdornment>
                          }
                          id='header-search'
                          inputProps={{ 'aria-label': 'weight' }}
                          onChange={handleDerivadosTextChange}
                          placeholder='Buscar'
                        />
                      </FormControl>
                    </Box>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '40px',
                    mt: 2
                  }}
                >
                  <Button
                    onClick={exportDerivadosCSV}
                    startIcon={<FileExcelOutlined />}
                    variant='contained'
                  >
                    Descargar excel
                  </Button>
                </Box>
              </Box>
            )}
            <GenericTable
              data={filterDerivadosData}
              entries={derivados}
              metadata={derivadosMetadata}
            />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}
