/* eslint-disable functional/no-try-statements */
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons'
import {
  Box,
  FormHelperText,
  Grid,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  Paper,
  Stack,
  Typography
} from '@mui/material'
import type {
  ChangeEventHandler,
  FC,
  FocusEventHandler,
  MouseEventHandler
} from 'react'

import { IconButton } from '@/common/components/IconButton'
import { LogoIndex } from '@/common/components/LogoIndex'
import { ThemeSwitcher } from '@/common/components/ThemeSwitcher'
import lightBackground from '~/assets/loginbackground.png'
import darkBackground from '~/assets/loginDarkBackground.png'
import { ThemeDirection, ThemeMode } from '~/types/resources'

export const LoginTitle: FC<{
  color: string
  title: string
  variant?: 'h3' | 'h5'
}> = ({ color, title, variant = 'h3' }) => (
  <Stack
    alignItems='baseline'
    direction='row'
    justifyContent='center'
    sx={{ mb: { sm: 0.5, xs: -0.5 } }}
  >
    <Typography sx={{ color }} variant={variant}>
      {title}
    </Typography>
  </Stack>
)

export const LoginWrapper: FC<{
  children: ReactNode
  direction: ThemeDirection
  mode: ThemeMode
}> = ({ children, direction, mode }) => (
  <Box sx={{ minHeight: '100vh' }}>
    <Box
      sx={{
        bottom: 0,
        position: 'absolute',
        transform:
          direction === ThemeDirection.RTL ? 'rotate(180deg)' : 'inherit',
        width: '100%',
        zIndex: -1
      }}
    >
      <img
        src={mode === ThemeMode.LIGHT ? lightBackground : darkBackground}
        style={{ height: '100%', width: '100%' }}
      />
    </Box>
    <Grid
      container
      direction='column'
      justifyContent='flex-end'
      sx={{ minHeight: '100vh' }}
    >
      <Grid
        alignItems='center'
        container
        item
        justifyContent='center'
        sx={{
          minHeight: {
            md: 'calc(100vh - 112px)',
            sm: 'calc(100vh - 134px)',
            xs: 'calc(100vh - 210px)'
          }
        }}
        xs={12}
      >
        <Grid item>
          <Paper
            elevation={3}
            sx={{
              background: mode === ThemeMode.DARK ? '#1E1E1E' : '',
              maxWidth: '400px',
              padding: '25px'
            }}
          >
            <Grid sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
              <LogoIndex />
            </Grid>
            <Grid container spacing={3}>
              {children}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
      <Grid container item justifyContent='end' xs={12}>
        <ThemeSwitcher />
      </Grid>
    </Grid>
  </Box>
)

export const TextBox: FC<{
  error?: boolean
  errorPlaceholder?: string
  id: string
  label: string
  name: string
  onBlur: FocusEventHandler<HTMLInputElement>
  onChange: ChangeEventHandler<HTMLInputElement>
  onError: boolean
  onMouseDown?: MouseEventHandler<HTMLButtonElement>
  onShowClick?: MouseEventHandler<HTMLButtonElement>
  placeholder: string
  showPassword?: boolean
  type?: 'email' | 'password' | 'text'
  value: string
}> = ({
  error,
  errorPlaceholder,
  id,
  label,
  name,
  onBlur,
  onChange,
  onError,
  onMouseDown,
  onShowClick,
  placeholder,
  showPassword,
  type = 'email',
  value
}) => (
  <Grid item xs={12}>
    <Stack spacing={1}>
      <InputLabel htmlFor={id}>{label}</InputLabel>
      <OutlinedInput
        endAdornment={
          showPassword !== undefined && (
            <InputAdornment position='end'>
              <IconButton
                aria-label='toggle password visibility'
                color='secondary'
                edge='end'
                onClick={onShowClick}
                onMouseDown={onMouseDown}
              >
                {showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              </IconButton>
            </InputAdornment>
          )
        }
        error={error}
        fullWidth
        id={id}
        name={name}
        onBlur={onBlur}
        onChange={onChange}
        placeholder={placeholder}
        type={type}
        value={value}
      />
      {onError && (
        <FormHelperText
          error
          id={`standard-weight-helper-text-${id}`}
          sx={{ color: '#ff6363' }}
        >
          {errorPlaceholder}
        </FormHelperText>
      )}
    </Stack>
  </Grid>
)
