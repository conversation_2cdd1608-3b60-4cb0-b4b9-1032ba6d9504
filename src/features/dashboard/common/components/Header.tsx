import { AppBar, type AppBarProps, useMediaQuery } from '@mui/material'
import { useTheme } from '@mui/material/styles'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { menuStore } from '@/common/store/menu-store'
import { MenuOrientation } from '~/types/resources'

import { HeaderContent } from './header-content'
import { AppBarStyled } from './header-content/AppBarStyled'

export const Header = () => {
  const { breakpoints, palette } = useTheme()
  const downLG = useMediaQuery(breakpoints.down('lg'))
  const { menuOrientation } = useContext(ResourceContext)
  const { drawerOpen } = menuStore.use()

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG

  const appBar: AppBarProps = {
    color: 'inherit',
    elevation: 0,
    position: 'fixed',
    sx: {
      borderBottom: `1px solid ${palette.divider}`,
      width: isHorizontal
        ? '100%'
        : // eslint-disable-next-line unicorn/no-nested-ternary, sonarjs/no-nested-conditional
          drawerOpen
          ? 'calc(100% - 260px)'
          : { lg: 'calc(100% - 60px)', xs: '100%' },
      zIndex: 1200
    }
  }

  return downLG ? (
    <AppBar {...appBar}>
      <HeaderContent />
    </AppBar>
  ) : (
    <AppBarStyled open={drawerOpen} {...appBar}>
      <HeaderContent />
    </AppBarStyled>
  )
}
