import { DialogTable } from '@d/common/components/DialogTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  activoPasivo,
  activoPasivoDetailMetadata
} from '@d/common/models/activo-pasivo'
import {
  desgloseDerivadoCobertura,
  desgloseDerivadoCoberturaMetadata
} from '@d/common/models/desglose-derivado-cobertura'
import {
  desgloseProductoActivoPasivo,
  desgloseProductoActivoPasivoActMetadata,
  desgloseProductoActivoPasivoPasMetadata
} from '@d/common/models/desglose-producto-activo-pasivo'
import { MargenFinanciero } from '@d/common/templates/margen-financiero/margen-financiero'

import { Fetching } from '@/dashboard/common/components/Fetching'

import { margenController } from './margen-controller'

export default () => {
  const {
    activoData,
    activoError,
    dialog1Data,
    dialog1Error,
    dialog2Data,
    dialog2Error,
    dialog3Data,
    dialog3Error,
    isActivoError,
    isActivoPending,
    isDialog1Error,
    isDialog1Pending,
    isDialog2Error,
    isDialog2Pending,
    isDialog3Error,
    isDialog3Pending,
    isPasivoError,
    isPasivoPending,
    onRowActivo3,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogActivo3,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    pasivoError,
    refetch,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogActivo3,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  } = margenController()

  return (
    <Fetching
      errorMessage={activoError?.message ?? pasivoError?.message}
      fullHeight
      isError={isActivoError || isPasivoError}
      isFetching={isActivoPending || isPasivoPending}
    >
      <Reload onClick={refetch} />
      <MargenFinanciero
        activo={activoData ?? []}
        onActivoClick={index =>
          onRowActivoPasivo1(activoData ?? [], index, true)
        }
        onPasivoClick={index =>
          onRowActivoPasivo1(pasivoData ?? [], index, false)
        }
        pasivo={pasivoData ?? []}
      />
      <DialogTable
        data={dialog1Data ?? []}
        entries={activoPasivo}
        error={dialog1Error?.message}
        isError={isDialog1Error}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, true)}
        open={openDialogActivo1}
        setOpen={setOpenDialogActivo1}
        title='Activo'
      />
      <DialogTable
        data={dialog1Data ?? []}
        entries={activoPasivo}
        error={dialog1Error?.message}
        isError={isDialog1Error}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, false)}
        open={openDialogPasivo1}
        setOpen={setOpenDialogPasivo1}
        title='Pasivo'
      />
      <DialogTable
        data={dialog2Data ?? []}
        entries={desgloseProductoActivoPasivo}
        error={dialog2Error?.message}
        excel
        isError={isDialog2Error}
        metadata={desgloseProductoActivoPasivoActMetadata}
        onRowClick={onRowActivo3}
        open={openDialogActivo2}
        setOpen={setOpenDialogActivo2}
        title='Activo'
      />
      <DialogTable
        data={dialog2Data ?? []}
        entries={desgloseProductoActivoPasivo}
        error={dialog2Error?.message}
        excel
        isError={isDialog2Error}
        metadata={desgloseProductoActivoPasivoPasMetadata}
        open={openDialogPasivo2}
        setOpen={setOpenDialogPasivo2}
        title='Pasivo'
      />
      <DialogTable
        data={dialog3Data ?? []}
        entries={desgloseDerivadoCobertura}
        error={dialog3Error?.message}
        isError={isDialog3Error}
        metadata={desgloseDerivadoCoberturaMetadata}
        open={openDialogActivo3}
        setOpen={setOpenDialogActivo3}
        title='Derivados de cobertura'
      />
      <LoadingDialog
        open={isDialog1Pending || isDialog2Pending || isDialog3Pending}
      />
    </Fetching>
  )
}
