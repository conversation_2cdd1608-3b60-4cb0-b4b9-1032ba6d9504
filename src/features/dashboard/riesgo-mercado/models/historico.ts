/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const historico = type({
  cleanPnL: 'number',
  cpty: 'string',
  cumple: 'string',
  fecha: 'string',
  simm1d: 'number'
})

export const historicoMetadata = new Set([
  {
    header: 'Contrapartida',
    key: 'cpty'
  },
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Simm1d Entregado',
    key: 'simm1d'
  },
  {
    header: 'Simm1d Recibido',
    key: 'simm1d'
  },
  {
    header: 'Clean P&L',
    key: 'cleanPnL'
  }
])

export type Historico = typeof historico.infer
export const historicoValidate = (attempt?: Historico) =>
  historico(attempt) instanceof type.errors ? resetObject(attempt) : attempt
