/* eslint-disable unicorn/no-useless-switch-case */
import type { Theme } from '@mui/material'

import type { ColorProps } from '~/types/extended'

export const getColors = (theme: Theme, color: ColorProps) => {
  switch (color ?? 'default') {
    case 'error': {
      return theme.palette.error
    }
    case 'info': {
      return theme.palette.info
    }
    case 'secondary': {
      return theme.palette.secondary
    }
    case 'success': {
      return theme.palette.success
    }
    case 'warning': {
      return theme.palette.warning
    }
    case 'default':
    case 'inherit':
    case 'primary':
    default: {
      return theme.palette.primary
    }
  }
}
