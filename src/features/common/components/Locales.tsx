import { type FC, type ReactNode, useContext, useEffect, useState } from 'react'
import { IntlProvider, type MessageFormatElement } from 'react-intl'

import { ResourceContext } from '@/common/contexts/ResourceContext'

const loadLocaleData = (locale: 'en' | 'es') =>
  import(`../../../core/lang/${locale}.json`) as Promise<{
    default: Record<string, MessageFormatElement[]> | Record<string, string>
  }>

export const Locales: FC<{ children: ReactNode }> = ({ children }) => {
  const { i18n } = useContext(ResourceContext)

  const [messages, setMessages] = useState<
    Record<string, MessageFormatElement[]> | Record<string, string>
  >()

  useEffect(() => {
    void loadLocaleData(i18n).then(d => setMessages(d.default))
  }, [i18n])

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {messages && (
        <IntlProvider defaultLocale='en' locale={i18n} messages={messages}>
          {children as string}
        </IntlProvider>
      )}
    </>
  )
}
