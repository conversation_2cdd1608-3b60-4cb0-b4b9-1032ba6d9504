/* eslint-disable import/default */
import { variacionesChartOpt } from '@d/banco/data/variaciones-chart'
import { variacionesSeries } from '@d/banco/models/variaciones'
import { GraficasRepository } from '@d/banco/repository/graficas-repository'
import { AppChart } from '@d/common/components/Chart'
import { Box, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useRef } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'

const paperStyle = {
  alignItems: 'left',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

export const VariacionesChart = () => {
  const day = calendarStore.actualDay.use()
  const { getVariaciones } = container.get(GraficasRepository)

  const series = useRef<ApexAxisChartSeries>([])

  const {
    error: variacionesError,
    isError: isVariacionesError,
    isFetching: isVariacionesPending,
    refetch: refetchVariaciones
  } = useQuery({
    queryFn: () =>
      getVariaciones().then(data => {
        series.current = variacionesSeries(data)
        return data
      }),
    queryKey: ['getVariaciones']
  })

  useEffect(() => {
    void refetchVariaciones()
  }, [day])

  return (
    <Grid item xs={6}>
      <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
        <TitleSubtitle title='Variaciones' />
      </div>
      <Fetching
        errorMessage={variacionesError?.message}
        isError={isVariacionesError}
        isFetching={isVariacionesPending}
      >
        <Paper elevation={10} sx={paperStyle}>
          <Box id='chart' sx={{ bgcolor: 'transparent' }}>
            <AppChart
              height={350}
              options={variacionesChartOpt}
              series={series.current}
              type='line'
            />
          )}
        </Box>
      </Paper>
    </Grid>
  )
}
