import { Button } from '@mui/material'
import { yellow } from '@mui/material/colors'
import { styled } from '@mui/material/styles'
import { type FC, useContext } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { ThemeMode } from '~/types/resources'

type Area = 'COAP' | 'MCAP' | 'MEMO' | 'TESO'

const AjusteButtonStyled = styled(Button)(() => ({
  '&:hover': { backgroundColor: yellow[300] }
}))

const AjusteButton: FC<{
  activeArea: Area
  selectedActiveArea: Area
  setActiveArea: (area: Area) => void
}> = ({ activeArea, selectedActiveArea, setActiveArea }) => {
  const { mode } = useContext(ResourceContext)

  const selectedStyle = {
    backgroundColor: mode === ThemeMode.LIGHT ? 'primary.lighter' : 'todo'
  }

  return (
    <AjusteButtonStyled
      onClick={() => setActiveArea(activeArea)}
      sx={activeArea === selectedActiveArea ? selectedStyle : {}}
    >
      {activeArea}
    </AjusteButtonStyled>
  )
}

export const AjusteButtonArrays = (
  selectedActiveArea: Area,
  setActiveArea: (area: Area) => void
) =>
  Array.of<Area>('COAP', 'MCAP', 'MEMO', 'TESO').map((area, index) => (
    <AjusteButton
      activeArea={area}
      key={index}
      selectedActiveArea={selectedActiveArea}
      setActiveArea={setActiveArea}
    />
  ))
