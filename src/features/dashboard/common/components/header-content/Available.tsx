/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable sonarjs/no-nested-conditional */
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import { Box, Button } from '@mui/material'
import { useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import { useQuery } from '@tanstack/react-query'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { ThemeMode } from '~/types/resources'

import { CommonRepository } from '../../repository/common-repository'

export const Available = () => {
  const theme = useTheme()

  const { getAvailable, getFundingRate } = container.get(CommonRepository)

  const day = calendarStore.formattedDay.use()
  const { mode } = useContext(ResourceContext)

  const {
    data: fundingRate,
    isPending: isFundingRatePending,
    refetch: fundingRateRefetch
  } = useQuery({
    queryFn: getFundingRate,
    queryKey: ['todos']
  })

  const {
    data: available,
    isPending: isAvailablePending,
    refetch: availablePendingRefetch
  } = useQuery({
    queryFn: async () => {
      calendarStore.setLoading(true)
      const aval = await getAvailable()
      calendarStore.setLoading(false)
      calendarStore.setAvailable(aval)
      return aval
    },
    queryKey: ['available']
  })

  const BgColorAvailable =
    theme.palette.mode === ThemeMode.LIGHT ? '#E6F2E2' : 'grey.300'
  const BgColorUnavailable =
    theme.palette.mode === ThemeMode.LIGHT ? '#BB6161' : '#AF3C3C'

  useEffect(() => {
    void fundingRateRefetch()
    void availablePendingRefetch()
  }, [day])

  return (
    <Box sx={{ display: 'flex', flexDirection: 'row', ml: 0.75 }}>
      <Typography
        component='h5'
        sx={{ alignSelf: 'center', marginRight: '15px', whiteSpace: 'nowrap' }}
        variant='h5'
      >
        ESTR +8.5pb:
      </Typography>

      <Button
        disabled
        sx={{
          '&.Mui-disabled': {
            color: mode === ThemeMode.DARK ? '#FAFAFA' : '#004B36'
          },
          marginRight: '15px'
        }}
        variant='contained'
      >
        {isFundingRatePending
          ? '0'
          : (Number(fundingRate) * 100).toString().slice(0, 6)}
        %
      </Button>
      <Button
        startIcon={
          (available ?? false) || !isAvailablePending ? (
            <CheckCircleOutlined />
          ) : (
            <CloseCircleOutlined />
          )
        }
        sx={{
          bgcolor: (available ?? false) ? BgColorAvailable : BgColorUnavailable,
          color:
            (available ?? false)
              ? mode === ThemeMode.DARK
                ? '#FAFAFA'
                : '#237804'
              : mode === ThemeMode.DARK
                ? '#FAFAFA'
                : 'black',
          mr: '15px'
        }}
        variant='outlined'
      >
        Disponible
      </Button>
    </Box>
  )
}
