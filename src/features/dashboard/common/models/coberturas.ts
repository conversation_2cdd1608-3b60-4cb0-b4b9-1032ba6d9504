/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const bonos = type({
  book: 'string',
  currencyID: 'string',
  fechaValoracion: 'string',
  intDiaBono: 'number',
  intDiaCobertura: 'number',
  isin: 'string',
  nominalBono: 'number'
})

export const bonosMetadata = new Set([
  {
    header: 'Book',
    key: 'book'
  },
  {
    header: 'ISIN',
    key: 'isin'
  },
  {
    header: 'NominalBono',
    key: 'nominalBono'
  },
  {
    header: 'IntDiaBono',
    key: 'intDiaBono'
  },
  {
    header: 'IntDiaCobertura',
    key: 'intDiaCobertura'
  },
  {
    header: 'CurrencyID',
    key: 'currencyID'
  },
  {
    header: 'FechaValoracion',
    key: 'fechaValoracion'
  }
])

export type Bonos = typeof bonos.infer

export const bonosValidate = (attempt?: Bonos) =>
  bonos(attempt) instanceof type.errors ? resetObject(attempt) : attempt

export const derivados = type({
  book: 'string',
  currencyID: 'string',
  fechaValoracion: 'string',
  instrumentID: 'number',
  intDiaCobertura: 'number',
  isin: 'string',
  saldo: 'number'
})

export const derivadosMetadata = new Set([
  {
    header: 'Book',
    key: 'book'
  },
  {
    header: 'Isin',
    key: 'isin'
  },
  {
    header: 'InstrumentID',
    key: 'instrumentID'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'IntDiaCobertura',
    key: 'intDiaCobertura'
  },
  {
    header: 'CurrencyID',
    key: 'currencyID'
  },
  {
    header: 'Fecha Valoración',
    key: 'fechaValoracion'
  }
])

export type Derivados = typeof derivados.infer

export const derivadosValidate = (attempt?: Derivados) =>
  derivados(attempt) instanceof type.errors ? resetObject(attempt) : attempt
