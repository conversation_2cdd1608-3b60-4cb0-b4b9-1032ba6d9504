/* eslint-disable sonarjs/no-hardcoded-passwords */
/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const parametros = type({
  bdEProductID: 'number?',
  callMoney: 'string?',
  costOfCarryIndex: 'string?',
  cptyInterna: 'string?',
  ddbb: 'string?',
  formatoSaldos: 'number?',
  paramID: 'number?',
  password: 'string?',
  poolProductID: 'number?',
  rrcc: 'number?',
  servidor: 'string?',
  tradeGroup: 'string',
  tradeGroupPool: 'string?',
  usuario: 'string?'
})

export type Parametros = typeof parametros.infer

export const parametrosDefault: Parametros = {
  bdEProductID: 0,
  callMoney: '',
  costOfCarryIndex: '',
  cptyInterna: '',
  ddbb: '',
  formatoSaldos: 0,
  paramID: 0,
  password: '',
  poolProductID: 0,
  rrcc: 0,
  servidor: '',
  tradeGroup: '',
  tradeGroupPool: '',
  usuario: ''
}

export const parametrosMetadata = new Set([
  {
    header: 'Param Id',
    key: 'paramID'
  },
  {
    header: 'Formato Saldos',
    key: 'formatoSaldos'
  },
  {
    header: 'DDBB',
    key: 'ddbb'
  },
  {
    header: 'Servidor',
    key: 'servidor'
  },
  {
    header: 'Usuario',
    key: 'usuario'
  },
  {
    header: 'Password',
    key: 'password'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Trade Group Pool',
    key: 'tradeGroupPool'
  },
  {
    header: 'Cost of Carry Index',
    key: 'costOfCarryIndex'
  },
  {
    header: 'Call Money',
    key: 'callMoney'
  }
])
