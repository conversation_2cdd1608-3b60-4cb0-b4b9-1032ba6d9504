import { FileExcelOutlined, SearchOutlined } from '@ant-design/icons'
import { TitleSubtitle } from '@c/components/TitleSubtitle'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import {
  type CarteraDesglose,
  carteraDesglose,
  carteraDesgloseMetadata,
  type CarteraResumen,
  carteraResumen,
  carteraResumenMetadata
} from '@d/common/models/cartera'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  Typography
} from '@mui/material'
import type { FC } from 'react'

import { paperStyle } from '~/resources/config/paper'

import { carteraInversionController } from './cartera-inversion-controller'

export const CarteraInversion: FC<{
  desglose: CarteraDesglose[]
  onClick?: (index: number) => void
  resumen: CarteraResumen[]
}> = ({ desglose, onClick, resumen }) => {
  const {
    exportDesgloseCsv,
    filterData,
    handleChange,
    handleTextChange,
    menuId,
    onRowClick
  } = carteraInversionController(desglose, onClick)

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TitleSubtitle title='Resumen de resultados' />
          <Paper elevation={10} sx={paperStyle}>
            <GenericTable
              data={resumen}
              entries={carteraResumen}
              metadata={carteraResumenMetadata}
              noTotal={false}
            />
          </Paper>
        </Grid>
        <Grid item xs={12}>
          <TitleSubtitle title='Desglose por activos' />
          <Paper elevation={10} sx={paperStyle}>
            {desglose.length > 0 && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  height: '70px',
                  justifyContent: 'space-between',
                  pl: '30px',
                  pr: '40px',
                  width: '100%'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    mt: 2
                  }}
                >
                  <Typography sx={{ mt: 1 }} variant='subtitle1'>
                    Buscar en:
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      minWidth: 200,
                      ml: 2
                    }}
                  >
                    <FormControl fullWidth>
                      <Select
                        id='demo-simple-select'
                        onChange={handleChange}
                        value={menuId.toString()}
                      >
                        {[...carteraDesgloseMetadata].map((data, index) => (
                          <MenuItem key={index} value={index}>
                            {data.header}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        ml: { md: 1, xs: 0 }
                      }}
                    >
                      <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                        <OutlinedInput
                          aria-describedby='header-search-text'
                          endAdornment={
                            <InputAdornment position='end' sx={{ mr: -0.5 }}>
                              <SearchOutlined />
                            </InputAdornment>
                          }
                          id='header-search'
                          inputProps={{ 'aria-label': 'weight' }}
                          onChange={handleTextChange}
                          placeholder='Buscar'
                        />
                      </FormControl>
                    </Box>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '40px',
                    mt: 2
                  }}
                >
                  <Button
                    onClick={exportDesgloseCsv}
                    startIcon={<FileExcelOutlined />}
                    variant='contained'
                  >
                    Descargar excel
                  </Button>
                </Box>
              </Box>
            )}
            <GenericTable
              data={filterData}
              entries={carteraDesglose}
              metadata={carteraDesgloseMetadata}
              noTotal={false}
              onRowClick={onRowClick}
            />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}
