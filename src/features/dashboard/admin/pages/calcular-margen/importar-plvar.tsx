/* eslint-disable unicorn/no-keyword-prefix */
import { DeleteOutlined, UploadOutlined } from '@ant-design/icons'
import { OverlayAlert } from '@d/admin/components/alert'
import {
  logAjusteVar,
  logAjusteVarMetadata
} from '@d/admin/models/log-ajustevar'
import { CalcMargenRepository } from '@d/admin/repositories/calc-margen-repository'
import { ImportarPLyVarRepository } from '@d/admin/repositories/import-plvar-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  type SelectChangeEvent,
  Typography
} from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { nth } from 'rambdax'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { getUltimaImportacion } = container.get(CalcMargenRepository)
  const {
    conversionDatosVaR,
    getLogAjusteVaR,
    statAjusteVarSPSH,
    statEliminarPlEq,
    statImportVaR,
    statPLRFBatchDiario,
    statPLRFEliminarResultados
  } = container.get(ImportarPLyVarRepository)

  const [menuId, setMenuId] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [showAlert, setShowAlert] = useState(false)
  const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(dayjs())
  const [endDate, setEndDate] = useState<dayjs.Dayjs | null>(dayjs())

  const importDate = useRef('')

  const {
    isFetching: isUltimaImportacionPending,
    refetch: refetchUltimaImportacion
  } = useQuery({
    queryFn: async () =>
      getUltimaImportacion().then(data => {
        importDate.current =
          nth(0, data)?.importPLRF ??
          new Date(Date.now()).toISOString().slice(0, 10)
        return data
      }),
    queryKey: ['getUltimaImportacion']
  })

  const {
    data: logAjuste,
    isFetching: isLogAjustePending,
    refetch: refetchLogAjuste
  } = useQuery({
    queryFn: () =>
      getLogAjusteVaR().then(data =>
        data.map(el => ({
          ...el,
          hora_Ajuste: el.hora_Ajuste.split('T')[0] ?? el.hora_Ajuste
        }))
      ),
    queryKey: ['getLogAjusteVaR']
  })

  const {
    data: conversionData,
    isFetching: isConversionPending,
    refetch: refetchConversion
  } = useQuery({
    queryFn: conversionDatosVaR,
    queryKey: ['conversionDatosVaR']
  })

  const handleChange = (event: SelectChangeEvent) =>
    setMenuId(Number.parseInt(event.target.value))

  const refetch = () => {
    void refetchUltimaImportacion()
    void refetchLogAjuste()
    void refetchConversion()
  }

  return isUltimaImportacionPending ||
    isLogAjustePending ||
    isConversionPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={refetch} />
      <OverlayAlert open={showAlert} />
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        {isLoading && <LinearProgress sx={{ mb: '15px' }} />}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TitleSubtitle title='Última Importación' />
          </Grid>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker
                disabled
                slotProps={{ textField: { size: 'small' } }}
                value={dayjs(importDate.current)}
              />
            </LocalizationProvider>
            <Button
              onClick={async () => {
                setIsLoading(true)
                await statPLRFBatchDiario(importDate.current)
                setIsLoading(false)
                setShowAlert(true)
                setTimeout(() => setShowAlert(false), 2000)
              }}
              startIcon={<UploadOutlined />}
              sx={{
                background: '#E5F1EE',
                color: 'green',
                marginLeft: '20px',
                marginRight: '10px'
              }}
              variant='contained'
            >
              Importar PL
            </Button>
            <Button
              onClick={async () => {
                setIsLoading(true)
                await statPLRFEliminarResultados(importDate.current)
                setIsLoading(false)
                setShowAlert(true)
                setTimeout(() => setShowAlert(false), 2000)
              }}
              sx={{ background: '#E5F1EE', color: 'red', marginX: '10px' }}
              variant='contained'
            >
              Eliminar PL
            </Button>
            <Button
              onClick={async () => {
                setIsLoading(true)
                await statEliminarPlEq(importDate.current)
                setIsLoading(false)
                setShowAlert(true)
                setTimeout(() => setShowAlert(false), 2000)
              }}
              startIcon={<DeleteOutlined />}
              sx={{ background: '#E5F1EE', color: 'red' }}
              variant='contained'
            >
              Eliminar PL EQ
            </Button>
          </Grid>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker
                disabled
                slotProps={{ textField: { size: 'small' } }}
                value={dayjs(importDate.current)}
              />
            </LocalizationProvider>
            <Button
              onClick={async () => {
                setIsLoading(true)
                await statImportVaR(importDate.current)
                setIsLoading(false)
                setShowAlert(true)
                setTimeout(() => setShowAlert(false), 2000)
              }}
              startIcon={<UploadOutlined />}
              sx={{ background: '#E5F1EE', color: 'green', marginLeft: '10px' }}
              variant='contained'
            >
              Importar VAR
            </Button>
          </Grid>
          <Grid item xs={12}>
            <TitleSubtitle title='Ajuste VAR' />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <InputLabel id='simple-select-label'>Seleccione</InputLabel>
                <Select
                  id='simple-select'
                  label='Seleccione'
                  labelId='simple-select-label'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  {(conversionData ?? []).map((item, index) => (
                    <MenuItem key={index} value={index}>
                      {item.nombresVaR}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Typography component='h5' sx={{ mb: '5px' }} variant='h5'>
              Fecha origen de los datos
            </Typography>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker<dayjs.Dayjs>
                defaultValue={dayjs(
                  new Date(Date.now()).toISOString().slice(0, 10)
                )}
                onChange={setStartDate}
                slotProps={{ textField: { size: 'small' } }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={6}>
            <Typography component='h5' sx={{ mb: '5px' }} variant='h5'>
              Fecha destino de los datos
            </Typography>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker<dayjs.Dayjs>
                defaultValue={dayjs(
                  new Date(Date.now()).toISOString().slice(0, 10)
                )}
                onChange={setEndDate}
                slotProps={{ textField: { size: 'small' } }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12}>
            <Button
              onClick={async () => {
                if (startDate === null || endDate === null) return
                setIsLoading(true)
                await statAjusteVarSPSH(
                  startDate.format('YYYY-MM-DD'),
                  endDate.format('YYYY-MM-DD'),
                  '???'
                )
                setIsLoading(false)
                setShowAlert(true)
                setTimeout(() => setShowAlert(false), 2000)
              }}
              sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
              variant='contained'
            >
              Ajustar
            </Button>
          </Grid>
          <Grid item xs={12}>
            <TitleSubtitle title='Logs Ajustes' />
            <Paper elevation={1} sx={paperStyle}>
              <GenericTable
                data={logAjuste ?? []}
                entries={logAjusteVar}
                metadata={logAjusteVarMetadata}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
