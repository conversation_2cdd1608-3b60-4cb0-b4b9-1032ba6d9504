/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const sensitivityArea = type({
  parDelta: 'number?',
  producto: 'string?',
  titulo: 'string?'
})

export const sensitivityAreaIntMetadata = new Set([
  { header: 'T<PERSON>tu<PERSON>', key: 'titulo' },
  { header: 'Par Delta', key: 'parDelta' }
])

export const sensitivityAreaProdMetadata = new Set([
  { header: 'Producto', key: 'producto' },
  { header: 'Par Delta', key: 'parDelta' }
])

export type SensitivityArea = typeof sensitivityArea.infer
export const sensitivityAreaValidate = (attempt?: SensitivityArea) =>
  sensitivityArea(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
