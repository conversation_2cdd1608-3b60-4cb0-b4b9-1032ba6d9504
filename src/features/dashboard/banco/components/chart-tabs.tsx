import '~/resources/styles/react-tabs.css'

import { Grid, Paper } from '@mui/material'
import type { FC } from 'react'
import { Tab, TabList, TabPanel, Tabs } from 'react-tabs'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'

import { NegoRentabChart } from './nego-rentab-chart'

const paperStyle = {
  alignItems: 'left',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

export const ChartTabs: FC<{
  isNego?: boolean
}> = ({ isNego = false }) => (
  <Grid item xs={6}>
    <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
      <TitleSubtitle
        title={
          isNego ? 'Resultado de Negociación' : 'Rentabilidad Margen Financiero'
        }
      />
    </div>
    <Paper elevation={10} sx={paperStyle}>
      <Tabs>
        <TabList>
          {isNego ? (
            <>
              <Tab>Trading diario</Tab>
              <Tab>Acum<PERSON>do <PERSON></Tab>
              <Tab>Acumulado Año</Tab>
            </>
          ) : (
            <>
              <Tab>Diario</Tab>
              <Tab>Mes</Tab>
              <Tab>Acumulado Año</Tab>
            </>
          )}
        </TabList>
        <TabPanel>
          <NegoRentabChart isNego={isNego} temp='day' />
        </TabPanel>
        <TabPanel>
          <NegoRentabChart isNego={isNego} temp='month' />
        </TabPanel>
        <TabPanel>
          <NegoRentabChart isNego={isNego} temp='year' />
        </TabPanel>
      </Tabs>
    </Paper>
  </Grid>
)
