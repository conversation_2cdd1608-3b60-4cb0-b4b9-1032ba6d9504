/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable sonarjs/cognitive-complexity */
import { Dot } from '@c/components/Dot'
import {
  Avatar,
  Chip,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import {
  type FC,
  forwardRef,
  type ForwardRefExoticComponent,
  type RefAttributes,
  useEffect
} from 'react'
import { Link, useLocation } from 'react-router'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { menuStore } from '@/common/store/menu-store'
import type { LinkTarget, NavItemType } from '~/types/menu'
import { MenuOrientation, ThemeMode } from '~/types/resources'

const listItemProps = (
  target: LinkTarget,
  url: string,
  isExternal: boolean
): {
  component:
    | ForwardRefExoticComponent<RefAttributes<HTMLAnchorElement>>
    | string
  href?: string
  target?: LinkTarget
} =>
  isExternal
    ? { component: 'a', href: url, target }
    : {
        component: forwardRef((props, ref) => (
          <Link {...props} ref={ref} target={target} to={url} />
        ))
      }

export const NavItem: FC<{ item: NavItemType; level: number }> = ({
  item,
  level
}) => {
  const theme = useTheme()

  const matchDownLg = useMediaQuery(theme.breakpoints.down('lg'))
  const { drawerOpen, openItem } = menuStore.use()

  const downLG = useMediaQuery(theme.breakpoints.down('lg'))

  const { menuOrientation } = useContext(ResourceContext)

  let itemTarget: LinkTarget = '_self'
  if (item.target ?? false) itemTarget = '_blank'

  const Icon = item.icon ?? ''
  const itemIcon =
    item.icon === undefined ? (
      false
    ) : (
      <Icon style={{ fontSize: drawerOpen ? '1rem' : '1.25rem' }} />
    )

  const isSelected = openItem.includes(item.id ?? '')

  const { pathname } = useLocation()

  useEffect(() => {
    if (
      pathname &&
      pathname.includes('product-details') &&
      (item.url?.includes('product-details') ?? false)
    ) {
      menuStore.activeItem([item.id ?? ''])
    }

    if (
      pathname &&
      pathname.includes('kanban') &&
      (item.url?.includes('kanban') ?? false)
    ) {
      menuStore.activeItem([item.id ?? ''])
    }

    if (pathname === item.url) menuStore.activeItem([item.id ?? ''])
    // eslint-disable-next-line
  }, [pathname])

  const textColor =
    theme.palette.mode === ThemeMode.DARK ? 'grey.400' : 'text.primary'
  const iconSelectedColor =
    theme.palette.mode === ThemeMode.DARK && drawerOpen
      ? 'text.primary'
      : 'primary.main'

  return menuOrientation === MenuOrientation.VERTICAL || downLG ? (
    <ListItemButton
      {...listItemProps(itemTarget, item.url ?? '', item.external ?? false)}
      disabled={item.disabled}
      selected={isSelected}
      sx={{
        pl: drawerOpen ? `${(level * 28).toString()}px` : 1.5,
        py: !drawerOpen && level === 1 ? 1.25 : 1,
        zIndex: 1201,
        ...(drawerOpen && {
          '&.Mui-selected': {
            '&:hover': {
              bgcolor:
                theme.palette.mode === ThemeMode.DARK
                  ? 'divider'
                  : 'primary.lighter',
              color: iconSelectedColor
            },
            bgcolor:
              theme.palette.mode === ThemeMode.DARK
                ? 'divider'
                : 'primary.lighter',
            borderRight: `2px solid ${theme.palette.primary.main}`,
            color: iconSelectedColor
          },
          '&:hover': {
            bgcolor:
              theme.palette.mode === ThemeMode.DARK
                ? 'divider'
                : 'primary.lighter'
          }
        }),
        ...(!drawerOpen && {
          '&.Mui-selected': {
            '&:hover': {
              bgcolor: 'primary.lighter'
            },
            bgcolor: isSelected ? 'primary.lighter' : textColor
          },
          '&:hover': {
            bgcolor: 'primary.lighter'
          }
        })
      }}
      {...(matchDownLg && {
        onClick: () => menuStore.openDrawer(false)
      })}
    >
      {itemIcon !== false && (
        <ListItemIcon
          sx={{
            color: isSelected ? iconSelectedColor : textColor,
            minWidth: 28,
            ...(!drawerOpen && {
              '&:hover': {
                bgcolor:
                  theme.palette.mode === ThemeMode.DARK
                    ? 'secondary.light'
                    : 'secondary.lighter'
              },
              alignItems: 'center',
              borderRadius: 1.5,
              height: 36,
              justifyContent: 'center',
              width: 36
            }),
            ...(!drawerOpen &&
              isSelected && {
                '&:hover': {
                  bgcolor:
                    theme.palette.mode === ThemeMode.DARK
                      ? 'primary.darker'
                      : 'primary.lighter'
                },
                bgcolor:
                  theme.palette.mode === ThemeMode.DARK
                    ? 'primary.900'
                    : 'primary.lighter'
              })
          }}
        >
          {itemIcon}
        </ListItemIcon>
      )}
      {(drawerOpen || (!drawerOpen && level !== 1)) && (
        <ListItemText
          primary={
            <Typography
              sx={{ color: isSelected ? iconSelectedColor : textColor }}
              variant='h6'
            >
              {item.title}
            </Typography>
          }
        />
      )}
      {(drawerOpen || (!drawerOpen && level !== 1)) && item.chip && (
        <Chip
          avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}
          color={item.chip.color}
          label={item.chip.label}
          size={item.chip.size}
          variant={item.chip.variant}
        />
      )}
    </ListItemButton>
  ) : (
    <ListItemButton
      {...listItemProps(itemTarget, item.url ?? '', item.external ?? false)}
      disabled={item.disabled}
      selected={isSelected}
      sx={{
        zIndex: 1201,
        ...(drawerOpen && {
          '&.Mui-selected': {
            '&:hover': {
              bgcolor: 'transparent',
              color: iconSelectedColor
            },
            bgcolor: 'transparent',
            color: iconSelectedColor
          },
          '&:hover': {
            bgcolor: 'transparent'
          }
        }),
        ...(!drawerOpen && {
          '&.Mui-selected': {
            '&:hover': {
              bgcolor: 'primary.lighter'
            },
            bgcolor: 'primary.lighter'
          },
          '&:hover': {
            bgcolor: 'primary.lighter'
          }
        })
      }}
    >
      {itemIcon !== false && (
        <ListItemIcon
          sx={{
            minWidth: 36,
            ...(!drawerOpen && {
              '&:hover': {
                bgcolor: 'transparent'
              },
              alignItems: 'center',
              borderRadius: 1.5,
              height: 36,
              justifyContent: 'flex-start',
              width: 36
            }),
            ...(!drawerOpen &&
              isSelected && {
                '&:hover': {
                  bgcolor: 'transparent'
                },
                bgcolor: 'transparent'
              })
          }}
        >
          {itemIcon}
        </ListItemIcon>
      )}

      {itemIcon === false && (
        <ListItemIcon
          sx={{
            color: isSelected ? 'primary.main' : 'secondary.main',
            ...(!drawerOpen && {
              '&:hover': {
                bgcolor: 'transparent'
              },
              alignItems: 'center',
              borderRadius: 1.5,
              justifyContent: 'flex-start'
            }),
            ...(!drawerOpen &&
              isSelected && {
                '&:hover': {
                  bgcolor: 'transparent'
                },
                bgcolor: 'transparent'
              })
          }}
        >
          <Dot color={isSelected ? 'primary' : 'secondary'} size={4} />
        </ListItemIcon>
      )}
      <ListItemText
        primary={
          <Typography color='inherit' variant='h6'>
            {item.title}
          </Typography>
        }
      />
      {!drawerOpen && level !== 1 && item.chip && (
        <Chip
          avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}
          color={item.chip.color}
          label={item.chip.label}
          size={item.chip.size}
          variant={item.chip.variant}
        />
      )}
    </ListItemButton>
  )
}
