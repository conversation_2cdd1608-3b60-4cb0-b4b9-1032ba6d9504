/* eslint-disable security/detect-object-injection */
/* eslint-disable sonarjs/function-return-type */
/* eslint-disable functional/no-throw-statements */

type CellValue = number | string
type DataObject = Record<string, CellValue>

const prepareData = (
  headers: string[],
  rows: Record<string, unknown>[],
  rowHeaders: string[]
) => {
  if (rows.length === 0) return []

  let data: DataObject[] = []

  data = rows.map(row => {
    const objToRet: DataObject = {}
    for (const [index, key] of headers.entries()) {
      const rawIndex = rowHeaders[index] ?? ''
      const rawValue = row[rawIndex]
      const value = Number.isNaN(Number(rawValue))
        ? String(rawValue)
        : Number(rawValue).toFixed(2)
      objToRet[key] = value
    }
    return objToRet
  })

  return data
}

export const exportToCSV = (
  data: CellValue[][] | DataObject[],
  filename = 'data.csv',
  separator = ';'
) => {
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error(
      'Invalid data format. Expected an array of objects or a 2D array.'
    )
  }

  let headers: string[] = []
  let csvContent = ''

  if (typeof data[0] === 'object' && !Array.isArray(data[0])) {
    headers = Object.keys(data[0])

    csvContent += `${headers.join(separator)}\r\n`

    for (const item of data as DataObject[]) {
      const row = headers.map(header => {
        let cell = item[header]

        if (typeof cell === 'string' && cell.includes(separator))
          cell = `"${cell}"`

        if (
          typeof cell === 'string' &&
          !Number.isNaN(Number(cell.slice(1, -1)))
        )
          cell = cell.split('.').join(',')

        return cell
      })

      csvContent += `${row.join(separator)}\r\n`
    }
  } else if (Array.isArray(data[0])) {
    for (const row of data as CellValue[][]) {
      csvContent += `${row
        .map((cell: CellValue) => {
          if (typeof cell === 'string' && cell.includes(separator))
            // eslint-disable-next-line no-param-reassign
            cell = `"${cell}"`

          return cell
        })
        .join(separator)}\r\n`
    }
  } else {
    throw new TypeError(
      'Invalid data format. Expected an array of objects or a 2D array.'
    )
  }

  const blob = new Blob(['\uFEFF', csvContent], {
    type: 'text/csv;charset=utf-16le;'
  })

  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  document.body.append(link)
  link.click()
  link.remove()
  URL.revokeObjectURL(url)
}

export const customExportToCSV = (
  headers: string[],
  rows: Record<string, unknown>[],
  rowHeaders: string[],
  fileName: string,
  separator?: string
) => exportToCSV(prepareData(headers, rows, rowHeaders), fileName, separator)
