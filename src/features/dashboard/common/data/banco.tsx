import { HomeOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const banco: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'banco-balance',
          title: <FormattedMessage id='balance' />,
          type: 'item',
          url: '/banco/balance'
        },
        {
          id: 'banco-calendarioIntereses',
          title: <FormattedMessage id='calendarioIntereses' />,
          type: 'item',
          url: '/banco/calendario-intereses'
        },
        {
          id: 'banco-graficas',
          title: <FormattedMessage id='graficas' />,
          type: 'item',
          url: '/banco/graficas'
        }
      ],
      icon: HomeOutlined,
      id: 'banco',
      title: <FormattedMessage id='banco' />,
      type: 'collapse'
    }
  ],
  icon: HomeOutlined,
  id: 'group-banco',
  title: <FormattedMessage id='banco' />,
  type: 'group'
}
