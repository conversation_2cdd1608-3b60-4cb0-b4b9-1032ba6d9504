/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const derivadosTrading = type({
  areaID: 'number',
  pLAno: 'number',
  pLDia: 'number',
  pLMes: 'number',
  tradeGroup: 'string'
})

export const derivadosTradingMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Día',
    key: 'pLDia'
  },
  {
    header: 'Mes',
    key: 'pLMes'
  },
  {
    header: 'Año',
    key: 'pLAno'
  }
])

export type DerivadosTrading = typeof derivadosTrading.infer

export const derivadosTradingValidate = (attempt: DerivadosTrading) =>
  derivadosTrading(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt

export const trading = type({
  areaID: 'number',
  day_Unrealised: 'number',
  dayTotal: 'number',
  month_Unrealised: 'number',
  monthTotal: 'number',
  titulo: 'string',
  tradeGroup: 'string',
  year_Realised: 'number',
  year_Unrealised: 'number',
  yearTotal: 'number'
})

export const tradingMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Día',
    key: 'dayTotal'
  },
  {
    header: 'Mes',
    key: 'monthTotal'
  },
  {
    header: 'Año',
    key: 'yearTotal'
  }
])

export type Trading = typeof trading.infer

export const tradingValidate = (attempt: Trading) =>
  trading(attempt) instanceof type.errors ? resetObject(attempt) : attempt
