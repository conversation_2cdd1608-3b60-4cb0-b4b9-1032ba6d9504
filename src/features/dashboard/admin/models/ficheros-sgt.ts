/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const ficherosSgt = type({
  carterasSGT: 'number',
  fecha: 'string',
  nominal: 'number',
  titulos: 'number'
})

export const ficherosSgtMetadata = new Set([
  { header: 'Fecha', key: 'fecha' },
  { header: 'Carteras SGT', key: 'carterasSGT' },
  { header: 'Titulos', key: 'titulos' },
  { header: 'Nominal', key: 'nominal' }
])

export type FicherosSGT = typeof ficherosSgt.infer
