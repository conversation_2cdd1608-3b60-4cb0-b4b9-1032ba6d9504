/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const backtesting = type({
  areaID: 'string',
  cartera: 'string',
  cleanPL: 'number',
  excedido: 'number',
  fecha: 'string',
  portfolio: 'string',
  producto: 'string',
  vaR: 'number'
})

export const backtestingMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Área',
    key: 'areaID'
  },
  {
    header: 'VaR',
    key: 'vaR'
  },
  {
    header: 'Clean PL',
    key: 'cleanPL'
  },
  {
    header: 'Excedido',
    key: 'excedido'
  }
])

export type Backtesting = typeof backtesting.infer

export const backtestingValidate = (attempt?: Backtesting) =>
  backtesting(attempt) instanceof type.errors ? resetObject(attempt) : attempt
