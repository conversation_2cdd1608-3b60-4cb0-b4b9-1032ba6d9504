import type { Theme } from '@mui/material/styles'

import { getColors } from '~/resources/helpers/get-colors'
import type { ExtendedStyleProps } from '~/types/extended'

// ==============================|| OVERRIDES - TAB ||============================== //

export const getColorStyle = ({ color, theme }: ExtendedStyleProps) => {
  const colors = getColors(theme, color)
  const { main } = colors

  return {
    border: `2px solid ${main}`
  }
}

export const Slider = (theme: Theme) => ({
  MuiSlider: {
    styleOverrides: {
      mark: {
        '&.MuiSlider-markActive': {
          borderColor: 'inherit',
          borderWidth: 2,
          opacity: 1
        },
        backgroundColor: theme.palette.background.paper,
        border: `1px solid ${theme.palette.secondary.light}`,
        borderRadius: '50%',
        height: 4,
        width: 4
      },
      rail: {
        color: theme.palette.secondary.light
      },
      root: {
        '&.Mui-disabled': {
          '.MuiSlider-rail': {
            opacity: 0.25
          },
          '.MuiSlider-thumb': {
            border: `2px solid ${theme.palette.secondary.lighter}`
          },
          '.MuiSlider-track': {
            color: theme.palette.secondary.lighter
          }
        }
      },
      thumb: {
        '&.MuiSlider-thumbColorError': getColorStyle({
          color: 'error',
          theme
        }),
        '&.MuiSlider-thumbColorInfo': getColorStyle({ color: 'info', theme }),
        '&.MuiSlider-thumbColorPrimary': getColorStyle({
          color: 'primary',
          theme
        }),
        '&.MuiSlider-thumbColorSecondary': getColorStyle({
          color: 'secondary',
          theme
        }),
        '&.MuiSlider-thumbColorSuccess': getColorStyle({
          color: 'success',
          theme
        }),
        '&.MuiSlider-thumbColorWarning': getColorStyle({
          color: 'warning',
          theme
        }),
        backgroundColor: theme.palette.background.paper,
        border: `2px solid ${theme.palette.primary.main}`,
        height: 14,
        width: 14
      },
      track: {
        height: '1px'
      },
      valueLabel: {
        backgroundColor: theme.palette.grey[600],
        color: theme.palette.grey[0]
      }
    }
  }
})
