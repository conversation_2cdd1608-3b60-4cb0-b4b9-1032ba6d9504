import { AppBar, Box, Container, useScrollTrigger } from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { cloneElement, type ReactElement } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'

import { Navigation } from './drawer-content/Navigation'

const ElevationScroll = ({
  children,
  window
}: {
  children: ReactElement
  window: Node | Window
}) => {
  const theme = useTheme()
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    target: window,
    threshold: 0
  })

  theme.shadows[4] = theme.customShadows.z1

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-expect-error
  return cloneElement(children, { elevation: trigger ? 4 : 0 })
}

export const HorizontalBar = () => {
  const theme = useTheme()
  const { container } = useContext(ResourceContext)

  return (
    <ElevationScroll window={globalThis.window}>
      <AppBar
        sx={{
          bgcolor: theme.palette.background.paper,
          borderBottom: `1px solid ${theme.palette.divider}`,
          borderTop: `1px solid ${theme.palette.divider}`,
          color: theme.palette.grey[500],
          height: 62,
          justifyContent: 'center',
          overflowX: 'auto',
          overflowY: 'hidden',
          top: 60,
          width: '100%',
          zIndex: 1098
        }}
      >
        <Container maxWidth={container ? 'xl' : false}>
          <Box sx={{ alignItems: 'center', display: 'flex' }}>
            <Navigation />
          </Box>
        </Container>
      </AppBar>
    </ElevationScroll>
  )
}
