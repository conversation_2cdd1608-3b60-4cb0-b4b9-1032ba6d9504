/* eslint-disable import/default */
import { variacionesChartOpt } from '@d/banco/data/variaciones-chart'
import { variacionesSeries } from '@d/banco/models/variaciones'
import { GraficasRepository } from '@d/banco/repository/graficas-repository'
import { AppChart } from '@d/common/components/Chart'
import { Loading } from '@d/common/components/Loading'
import { Box, Grid, Paper } from '@mui/material'
import { useEffect, useRef, useState } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

const paperStyle = {
  alignItems: 'left',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

export const VariacionesChart = () => {
  const day = calendarStore.actualDay.use()
  const { getVariaciones } = container.get(GraficasRepository)

  const series = useRef<ApexAxisChartSeries>([])

  const [isLoading, setIsLoading] = useState(false)

  const fetchChart = async () => {
    setIsLoading(true)
    series.current = variacionesSeries(await getVariaciones())
    setIsLoading(false)
  }

  useEffect(() => {
    void fetchChart()
  }, [day])

  return (
    <Grid item xs={6}>
      <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
        <TitleSubtitle title='Variaciones' />
      </div>
      <Paper elevation={10} sx={paperStyle}>
        <Box id='chart' sx={{ bgcolor: 'transparent' }}>
          {isLoading ? (
            <Loading />
          ) : (
            <AppChart
              height={350}
              options={variacionesChartOpt}
              series={series.current}
              type='line'
            />
          )}
        </Box>
      </Paper>
    </Grid>
  )
}
