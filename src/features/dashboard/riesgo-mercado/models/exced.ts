/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const exced = type({
  cOAP: 'number?',
  derivados: 'number?',
  divisa: 'number',
  mercadoMonetario: 'number?',
  producto: 'string',
  rentaFija: 'number?',
  tOTAL: 'number'
})

export const excedMetadata = new Set([
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Mercado Monetario',
    key: 'mercadoMonetario'
  },
  {
    header: 'Renta Fija',
    key: 'rentaFija'
  },
  {
    header: 'COAP',
    key: 'cOAP'
  },
  {
    header: 'TOTAL',
    key: 'tOTAL'
  }
])

export type Exced = typeof exced.infer

export const excedValidate = (attempt?: Exced) =>
  exced(attempt) instanceof type.errors ? resetObject(attempt) : attempt
