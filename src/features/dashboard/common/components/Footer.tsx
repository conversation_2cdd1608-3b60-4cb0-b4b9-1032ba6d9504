import { Link, Stack, Typography } from '@mui/material'
import { Link as RouterLink } from 'react-router'

export const Footer = () => (
  <Stack
    alignItems='center'
    direction='row'
    justifyContent='space-between'
    sx={{ mt: 'auto', p: '24px 16px 0px' }}
  >
    <Typography variant='caption'>&copy; All rights reserved</Typography>
    <Stack
      alignItems='center'
      direction='row'
      justifyContent='space-between'
      spacing={1.5}
    >
      <Link
        color='textPrimary'
        component={RouterLink}
        target='_blank'
        to='#'
        variant='caption'
      >
        About us
      </Link>
      <Link
        color='textPrimary'
        component={RouterLink}
        target='_blank'
        to='#'
        variant='caption'
      >
        Privacy
      </Link>
      <Link
        color='textPrimary'
        component={RouterLink}
        target='_blank'
        to='#'
        variant='caption'
      >
        Terms
      </Link>
    </Stack>
  </Stack>
)
