/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const epigrafes = type({
  accountingAreaID: 'number',
  categoria: 'string',
  epigrafeID: 'number',
  nombre: 'string',
  orden: 'number',
  titulo: 'string'
})

export type Epigrafes = typeof epigrafes.infer

export const epigrafesDefault: Epigrafes = {
  accountingAreaID: 0,
  categoria: '',
  epigrafeID: 0,
  nombre: '',
  orden: 0,
  titulo: ''
}

export const epigrafesMetadata = new Set([
  {
    header: 'Epigrafe ID',
    key: 'epigrafeID'
  },
  {
    header: 'Nombre',
    key: 'nombre'
  },
  {
    header: 'Orden',
    key: 'orden'
  },
  {
    header: 'Categoria',
    key: 'categoria'
  },
  {
    header: 'Título',
    key: 'titulo'
  }
])
