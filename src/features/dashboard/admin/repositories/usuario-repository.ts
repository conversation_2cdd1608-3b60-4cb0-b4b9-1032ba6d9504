/* eslint-disable sonarjs/use-type-alias */
import type { Usuario } from '@d/admin/models/usuario'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class UsuarioRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/usuarios'

  readonly deleteUsuario = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getUsuario = () =>
    this.api.client
      .get<Usuario[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertUsuario = (usuario: Usuario) =>
    this.api.client.post<{ message: string }>(this.route, usuario)

  readonly updateUsuario = (id: string, usuario: Usuario) =>
    this.api.client.put<{ message: string }>(`${this.route}/${id}`, usuario)
}
