/* eslint-disable unicorn/no-keyword-prefix */
import { type Ajustes, ajustesDefault } from '@d/admin/models/ajustes'
import { AjustesRepository } from '@d/admin/repositories/ajustes-repository'
import { useMutation, useQuery } from '@tanstack/react-query'

import { alertStore } from '@/common/store/alert-store'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

type Area = 'COAP' | 'MCAP' | 'MEMO' | 'TESO'

export const validate = (ajustes: Ajustes) => {
  const errors: Record<string, string> = {}
  if (!ajustes.fecha) errors['fecha'] = 'Required'
  if (!ajustes.accountingAreaID || !ajustes.concepto)
    errors['accountingAreaID'] = 'Area y concepto obligatorios'
  return errors
}

export const ajustesController = () => {
  const day = calendarStore.formattedDay.use()

  const [activeArea, setActiveArea] = useState<Area>('MEMO')

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const [ajustesDate, setAjustesDate] = useState(day)

  const { deleteAjustes, getAjustes, insertAjustes, updateAjustes } =
    container.get(AjustesRepository)

  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(ajustesDefault)

  const {
    data: ajustesData,
    error: ajustesError,
    isError: isAjustesError,
    isFetching: isAjustesPending,
    refetch: refetchAjustes
  } = useQuery({
    queryFn: () => getAjustes(ajustesDate, activeArea),
    queryKey: ['getAjustes']
  })

  const {
    isError: isDeleteAjustesError,
    isPending: isDeleteAjustesPending,
    isSuccess: isDeleteAjustesSuccess,
    mutate: deleteAction
  } = useMutation({
    mutationFn: async (isOk: boolean) => {
      setOpenDeleteDialog(false)
      if (!isOk) return
      await deleteAjustes(
        ajustesDate,
        activeArea,
        ajustesData?.[selectedIndex.current ?? 0]?.concepto ?? 'FUTUROS'
      )
      void refetchAjustes()
    }
  })

  const {
    isError: isSubmitAjustesError,
    isPending: isSubmitAjustesPending,
    isSuccess: isSubmitAjustesSuccess,
    mutate: submitAction
  } = useMutation({
    mutationFn: async (ajustes: Ajustes) => {
      setOpenCreateEditDialog(false)
      await (selectedIndex.current === null
        ? insertAjustes(ajustes)
        : updateAjustes(
            ajustes.fecha,
            ajustes.importe,
            ajustes.accountingAreaID as Area,
            ajustes.concepto
          ))

      void refetchAjustes()
    }
  })

  useEffect(() => {
    setAjustesDate(day)
    void refetchAjustes()
  }, [day])

  useEffect(() => {
    void refetchAjustes()
  }, [activeArea, ajustesDate])

  useEffect(() => {
    if (isDeleteAjustesSuccess || isSubmitAjustesSuccess)
      alertStore.displayInfo()
  }, [isDeleteAjustesSuccess, isSubmitAjustesSuccess])

  useEffect(() => {
    if (isSubmitAjustesError || isDeleteAjustesError) alertStore.displayError()
  }, [isSubmitAjustesError, isDeleteAjustesError])

  return {
    activeArea,
    ajustesData,
    ajustesDate,
    ajustesError,
    day,
    deleteAction,
    isAjustesError,
    isAjustesPending,
    isDeleteAjustesPending,
    isSubmitAjustesPending,
    openCreateEditDialog,
    openDeleteDialog,
    refetchAjustes,
    selectedData,
    selectedIndex,
    setActiveArea,
    setAjustesDate,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  }
}
