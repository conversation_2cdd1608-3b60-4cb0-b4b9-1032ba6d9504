import { DialogTable } from '@d/common/components/DialogTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  activoPasivo,
  activoPasivoDetailMetadata
} from '@d/common/models/activo-pasivo'
import {
  desgloseDerivadoCobertura,
  desgloseDerivadoCoberturaMetadata
} from '@d/common/models/desglose-derivado-cobertura'
import {
  desgloseProductoActivoPasivo,
  desgloseProductoActivoPasivoActMetadata,
  desgloseProductoActivoPasivoPasMetadata
} from '@d/common/models/desglose-producto-activo-pasivo'
import { MargenFinanciero } from '@d/common/templates/margen-financiero/margen-financiero'

import { margenController } from './margen-controller'

export default () => {
  const {
    activoData,
    dialog1Data,
    dialog2Data,
    dialog3Data,
    isActivoPending,
    isPasivoPending,
    loadingDialog,
    onRowActivo3,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogActivo3,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    refetch,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogActivo3,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  } = margenController()

  return isActivoPending || isPasivoPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={refetch} />
      <MargenFinanciero
        activo={activoData ?? []}
        onActivoClick={index =>
          onRowActivoPasivo1(activoData ?? [], index, true)
        }
        onPasivoClick={index =>
          onRowActivoPasivo1(pasivoData ?? [], index, false)
        }
        pasivo={pasivoData ?? []}
      />
      <DialogTable
        data={dialog1Data}
        entries={activoPasivo}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, true)}
        open={openDialogActivo1}
        setOpen={setOpenDialogActivo1}
        title='Activo'
      />
      <DialogTable
        data={dialog1Data}
        entries={activoPasivo}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, false)}
        open={openDialogPasivo1}
        setOpen={setOpenDialogPasivo1}
        title='Pasivo'
      />
      <DialogTable
        data={dialog2Data}
        entries={desgloseProductoActivoPasivo}
        excel
        metadata={desgloseProductoActivoPasivoActMetadata}
        onRowClick={onRowActivo3}
        open={openDialogActivo2}
        setOpen={setOpenDialogActivo2}
        title='Activo'
      />
      <DialogTable
        data={dialog2Data}
        entries={desgloseProductoActivoPasivo}
        excel
        metadata={desgloseProductoActivoPasivoPasMetadata}
        open={openDialogPasivo2}
        setOpen={setOpenDialogPasivo2}
        title='Pasivo'
      />
      <DialogTable
        data={dialog3Data}
        entries={desgloseDerivadoCobertura}
        metadata={desgloseDerivadoCoberturaMetadata}
        open={openDialogActivo3}
        setOpen={setOpenDialogActivo3}
        title='Derivados de cobertura'
      />
      <LoadingDialog open={loadingDialog} />
    </>
  )
}
