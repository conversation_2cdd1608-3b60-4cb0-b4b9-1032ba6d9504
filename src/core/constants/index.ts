import {
  MenuOrientation,
  type ResourcesConfig,
  ThemeDirection,
  ThemeMode
} from '~/types/resources'

export const resConfig: ResourcesConfig = {
  container: false,
  fontFamily: `'Public Sans', sans-serif`,
  i18n: 'en',
  menuOrientation: MenuOrientation.VERTICAL,
  miniDrawer: false,
  mode: ThemeMode.LIGHT,
  presetColor: 'default',
  themeDirection: ThemeDirection.LTR
}

export const APP_DEFAULT_PATH = '/banco/balance'
export const HORIZONTAL_MAX_ITEM = 10
export const DRAWER_WIDTH = 260
