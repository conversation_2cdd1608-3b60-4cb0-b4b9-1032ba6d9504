import type { PalettesProps } from '@ant-design/colors'
import type {
  PaletteColorOptions,
  SimplePaletteColorOptions
} from '@mui/material/styles'

import type { ThemeMode } from '~/types/resources'

export const Default = (
  { blue, cyan, gold, green, grey, red }: PalettesProps,
  _?: ThemeMode
): {
  error: SimplePaletteColorOptions
  grey: PaletteColorOptions
  info: SimplePaletteColorOptions
  primary: SimplePaletteColorOptions
  secondary: SimplePaletteColorOptions
  success: SimplePaletteColorOptions
  warning: SimplePaletteColorOptions
} => {
  const greyColors: PaletteColorOptions = {
    0: grey?.[0],
    50: grey?.[1],
    100: grey?.[2],
    200: grey?.[3],
    300: grey?.[4],
    400: grey?.[5],
    500: grey?.[6],
    600: grey?.[7],
    700: grey?.[8],
    800: grey?.[9],
    900: grey?.[10],
    A50: grey?.[15],
    A100: grey?.[11],
    A200: grey?.[12],
    A400: grey?.[13],
    A700: grey?.[14],
    A800: grey?.[16]
  }

  const contrastText = '#fff'
  const primaryColors = ['#004B36', '#C7EBAD', '#7EA49A']

  return {
    error: {
      contrastText,
      dark: red?.[7],
      darker: red?.[9] ?? '#d32f2f',
      light: red?.[2],
      lighter: red?.[0] ?? '#ff6659',
      main: red?.[4] ?? '#f44336'
    },
    grey: greyColors,
    info: {
      contrastText,
      dark: cyan?.[7],
      darker: cyan?.[9] ?? '#0097A7',
      light: cyan?.[3],
      lighter: cyan?.[0] ?? '#B2EBF2',
      main: cyan?.[5] ?? '#00BCD4'
    },
    primary: {
      100: blue?.[1],
      200: blue?.[2],
      300: primaryColors[2],
      400: blue?.[4],
      700: blue?.[7],
      900: blue?.[9],
      contrastText,
      dark: blue?.[6],
      darker: blue?.[8] ?? '#0D47A1',
      light: blue?.[3],
      lighter: primaryColors[1] ?? '#B3E5FC',
      main: primaryColors[0] ?? '#2196F3'
    },
    secondary: {
      100: greyColors[100],
      200: greyColors[200],
      400: greyColors[400],
      600: greyColors[600],
      800: greyColors[800],
      A100: greyColors[0],
      A200: greyColors.A400,
      A300: greyColors.A700,
      contrastText: greyColors[0],
      dark: greyColors[700],
      darker: greyColors[900] ?? '#212121',
      light: greyColors[300],
      lighter: greyColors[100] ?? '#cfd8dc',
      main: greyColors[500] ?? '#607D8B'
    },
    success: {
      contrastText,
      dark: green?.[7],
      darker: green?.[9] ?? '#388E3C',
      light: green?.[3],
      lighter: green?.[0] ?? '#81C784',
      main: green?.[5] ?? '#4CAF50'
    },
    warning: {
      contrastText: greyColors[100],
      dark: gold?.[7],
      darker: gold?.[9] ?? '#F57F17',
      light: gold?.[3],
      lighter: gold?.[0] ?? '#FFD54F',
      main: gold?.[5] ?? '#FFC107'
    }
  }
}
