/* eslint-disable sonarjs/no-nested-functions */
import { FileExcelOutlined } from '@ant-design/icons'
import { Reload } from '@d/common/components/Reload'
import { varMarginalChartOpts } from '@d/riesgo-mercado/data/var-marginal-chart'
import {
  varMarginal,
  varMarginalMetadata
} from '@d/riesgo-mercado/models/var-marginal'
import { VarMarginalRepository } from '@d/riesgo-mercado/repositories/var-marginal-repository'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  MenuItem,
  Paper,
  Select,
  type SelectChangeEvent,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'

import { calendarStore } from '@/common/store/calendar-store'
import { AppChart } from '@/dashboard/common/components/Chart'
import { GenericTable } from '@/dashboard/common/components/generic-table/GenericTable'
import { Loading } from '@/dashboard/common/components/Loading'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { customExportToCSV, exportToCSV } from '~/utils/csv'

type Areas =
  | 'COAP'
  | 'Derivados'
  | 'Divisa'
  | 'Mercado Monetario'
  | 'Renta Fija'
  | 'TOTAL'

const areas: Areas[] = [
  'TOTAL',
  'COAP',
  'Derivados',
  'Divisa',
  'Mercado Monetario',
  'Renta Fija'
]

export default () => {
  const day = calendarStore.actualDay.use()

  const { getBce, getBceNeg, getCarteras, getVarMarginal } = container.get(
    VarMarginalRepository
  )

  const [menuIdInverVarProd, setMenuIdInverVarProd] = useState(0)
  const menuIdInverTitle = () => nth(menuIdInverVarProd, areas) ?? 'TOTAL'
  const [menuIdInverVarProdChart, setMenuIdInverVarProdChart] = useState(0)
  const menuIdInverTitleChart = () =>
    nth(menuIdInverVarProdChart, areas) ?? 'TOTAL'
  const [menuIdInverTradeGroupProdChart, setMenuIdInverTradeGroupProdChart] =
    useState(0)

  const [menuIdInverVarMarg, setMenuIdInverVarMarg] = useState(0)
  const menuIdInverVarMargTitle = () =>
    nth(menuIdInverVarMarg, areas) ?? 'TOTAL'
  const [menuIdInverVarMargChart, setMenuIdInverVarMargChart] = useState(0)
  const menuIdInverVarMargTitleChart = () =>
    nth(menuIdInverVarMargChart, areas) ?? 'TOTAL'
  const [menuIdInverTradeGroupMargChart, setMenuIdInverTradeGroupMargChart] =
    useState(0)

  const [menuIdNegoVarProd, setMenuIdNegoVarProd] = useState(0)
  const menuIdNegoTitle = () => nth(menuIdNegoVarProd, areas) ?? 'TOTAL'
  const [menuIdNegoVarProdChart, setMenuIdNegoVarProdChart] = useState(0)
  const menuIdNegoTitleChart = () =>
    nth(menuIdNegoVarProdChart, areas) ?? 'TOTAL'
  const [menuIdNegoTradeGroupProdChart, setMenuIdNegoTradeGroupProdChart] =
    useState(0)

  const [menuIdNegoVarMarg, setMenuIdNegoVarMarg] = useState(0)
  const menuIdNegoVarMargTitle = () => nth(menuIdNegoVarMarg, areas) ?? 'TOTAL'
  const [menuIdNegoVarMargChart, setMenuIdNegoVarMargChart] = useState(0)
  const menuIdNegoVarMargTitleChart = () =>
    nth(menuIdNegoVarMargChart, areas) ?? 'TOTAL'
  const [menuIdNegoTradeGroupMargChart, setMenuIdNegoTradeGroupMargChart] =
    useState(0)

  const bceInvSeries = useRef<ApexAxisChartSeries>([])
  const bceInvOptions = useRef(varMarginalChartOpts)
  const bceInvMargSeries = useRef<ApexAxisChartSeries>([])
  const bceInvMargOptions = useRef(varMarginalChartOpts)
  const bceNegSeries = useRef<ApexAxisChartSeries>([])
  const bceNegOptions = useRef(varMarginalChartOpts)
  const bceNegMargSeries = useRef<ApexAxisChartSeries>([])
  const bceNegMargOptions = useRef(varMarginalChartOpts)

  const {
    data: getInverCarteraData,
    isFetching: isgetInverCarteraPending,
    refetch: refetchGetInverCartera
  } = useQuery({
    queryFn: () => getCarteras('Inversion'),
    queryKey: ['getInverCartera']
  })

  const {
    data: getNegoCarteraData,
    isFetching: isgetNegoCarteraPending,
    refetch: refetchGetNegoCartera
  } = useQuery({
    queryFn: () => getCarteras('Negociacion'),
    queryKey: ['getNegoCartera']
  })

  const menuIdInverTitleTradeGroupChart = () =>
    nth(menuIdInverTradeGroupProdChart, getInverCarteraData ?? [])?.cartera ??
    'TOTAL'

  const menuIdInverTitleTradeGroupMargChart = () =>
    nth(menuIdInverTradeGroupMargChart, getInverCarteraData ?? [])?.cartera ??
    'TOTAL'

  const menuIdNegoTitleTradeGroupChart = () =>
    nth(menuIdNegoTradeGroupProdChart, getNegoCarteraData ?? [])?.cartera ??
    'TOTAL'

  const menuIdNegoTitleTradeGroupMargChart = () =>
    nth(menuIdNegoTradeGroupMargChart, getNegoCarteraData ?? [])?.cartera ??
    'TOTAL'

  const {
    data: getInverVarProdData,
    isFetching: isgetInverVarProdPending,
    refetch: refetchGetInverVarProd
  } = useQuery({
    queryFn: () => getVarMarginal('Inversión', menuIdInverTitle(), 'Producto'),
    queryKey: ['getInverVarProd']
  })

  const {
    data: getInverVarMargData,
    isFetching: isgetInverVarMargPending,
    refetch: refetchGetInverVarMarg
  } = useQuery({
    queryFn: () =>
      getVarMarginal('Inversión', menuIdInverVarMargTitle(), 'Marginal'),
    queryKey: ['getInverVarMarg']
  })

  const {
    data: getNegoVarProdData,
    isFetching: isgetNegoVarProdPending,
    refetch: refetchGetNegoVarProd
  } = useQuery({
    queryFn: () => getVarMarginal('Negociación', menuIdNegoTitle(), 'Producto'),
    queryKey: ['getNegoVarProd']
  })

  const {
    data: getNegoVarMargData,
    isFetching: isgetNegoVarMargPending,
    refetch: refetchGetNegoVarMarg
  } = useQuery({
    queryFn: () =>
      getVarMarginal('Negociación', menuIdNegoVarMargTitle(), 'Marginal'),
    queryKey: ['getNegoVarMarg']
  })

  const {
    data: getInverBceData,
    isFetching: isgetInverBcePending,
    refetch: refetchGetInverBce
  } = useQuery({
    queryFn: () =>
      getBce(menuIdInverTitleTradeGroupChart(), menuIdInverTitleChart()).then(
        data => {
          bceInvOptions.current = {
            ...bceInvOptions.current,
            xaxis: { categories: data.map(el => el.producto) }
          }
          bceInvSeries.current = [{ data: data.map(el => el.vaR) }]
          return data
        }
      ),
    queryKey: ['getInverBce']
  })

  const {
    data: getInverBceMargData,
    isFetching: isgetInverBceMargPending,
    refetch: refetchGetInverBceMarg
  } = useQuery({
    queryFn: () =>
      getBce(
        menuIdInverTitleTradeGroupMargChart(),
        menuIdInverVarMargTitleChart()
      ).then(data => {
        bceInvMargOptions.current = {
          ...bceInvOptions.current,
          xaxis: { categories: data.map(el => el.producto) }
        }
        bceInvMargSeries.current = [{ data: data.map(el => el.vaRMarginal) }]
        return data
      }),
    queryKey: ['getInverBceMarg']
  })

  const {
    data: getNegoBceData,
    isFetching: isgetNegoBcePending,
    refetch: refetchGetNegoBce
  } = useQuery({
    queryFn: () =>
      getBceNeg(menuIdNegoTitleTradeGroupChart(), menuIdNegoTitleChart()).then(
        data => {
          bceNegOptions.current = {
            ...bceNegOptions.current,
            xaxis: { categories: data.map(el => el.producto) }
          }
          bceNegSeries.current = [{ data: data.map(el => el.vaR) }]
          return data
        }
      ),
    queryKey: ['getNegoBce']
  })

  const {
    data: getNegoBceMargData,
    isFetching: isgetNegoBceMargPending,
    refetch: refetchGetNegoBceMarg
  } = useQuery({
    queryFn: () =>
      getBceNeg(
        menuIdNegoTitleTradeGroupMargChart(),
        menuIdNegoVarMargTitleChart()
      ).then(data => {
        bceNegMargOptions.current = {
          ...bceNegOptions.current,
          xaxis: { categories: data.map(el => el.producto) }
        }
        bceNegMargSeries.current = [{ data: data.map(el => el.vaRMarginal) }]
        return data
      }),
    queryKey: ['getNegoBceMarg']
  })

  const refetch = () => {
    void refetchGetInverVarProd()
    void refetchGetInverVarMarg()
    void refetchGetNegoVarProd()
    void refetchGetNegoVarMarg()
    void refetchGetInverCartera()
    void refetchGetNegoCartera()
    void refetchGetInverBce()
    void refetchGetInverBceMarg()
    void refetchGetNegoBce()
    void refetchGetNegoBceMarg()
  }

  useEffect(() => {
    void refetchGetInverVarProd()
  }, [menuIdInverVarProd])

  useEffect(() => {
    void refetchGetInverBce()
  }, [menuIdInverVarProdChart, menuIdInverTradeGroupProdChart])

  useEffect(() => {
    void refetchGetInverVarMarg()
  }, [menuIdInverVarMarg])

  useEffect(() => {
    void refetchGetInverBceMarg()
  }, [menuIdInverVarMargChart, menuIdInverTradeGroupMargChart])

  useEffect(() => {
    void refetchGetNegoVarProd()
  }, [menuIdNegoVarProd])

  useEffect(() => {
    void refetchGetNegoBce()
  }, [menuIdNegoVarProdChart, menuIdNegoTradeGroupProdChart])

  useEffect(() => {
    void refetchGetNegoVarMarg()
  }, [menuIdNegoVarMarg])

  useEffect(() => {
    void refetchGetNegoBceMarg()
  }, [menuIdNegoVarMargChart, menuIdNegoTradeGroupMargChart])

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Inversión</h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              {isgetInverVarProdPending || getInverVarProdData === undefined ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 220,
                        mt: 2
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) =>
                            setMenuIdInverVarProd(Number(event.target.value))
                          }
                          value={menuIdInverVarProd.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={index} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Button
                      onClick={() =>
                        customExportToCSV(
                          [...varMarginalMetadata].map(el => el.header),
                          getInverVarProdData,
                          [...varMarginalMetadata].map(el => el.key),
                          'VarPorProducto.csv',
                          ';'
                        )
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <GenericTable
                    data={getInverVarProdData}
                    entries={varMarginal}
                    metadata={varMarginalMetadata}
                    title='Var por Producto'
                  />
                </>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              {isgetInverBcePending || getInverBceData === undefined ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 450,
                        mt: 2,
                        whiteSpace: 'nowrap'
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) =>
                            setMenuIdInverVarProdChart(
                              Number(event.target.value)
                            )
                          }
                          value={menuIdInverVarProdChart.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={index} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Typography
                        component='h5'
                        sx={{ mx: '20px' }}
                        variant='h5'
                      >
                        Trade Group
                      </Typography>
                      {!isgetInverCarteraPending &&
                        getInverCarteraData !== undefined && (
                          <FormControl fullWidth>
                            <Select
                              onChange={(event: SelectChangeEvent) =>
                                setMenuIdInverTradeGroupProdChart(
                                  Number(event.target.value)
                                )
                              }
                              value={menuIdInverTradeGroupProdChart.toString()}
                            >
                              {getInverCarteraData.map((key, index) => (
                                <MenuItem key={key.cartera} value={index}>
                                  {key.cartera}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                    </Box>
                    <Button
                      onClick={() =>
                        exportToCSV(getInverBceData, 'fxVarInvProd.csv', ';')
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <Box
                    id='chart'
                    sx={{ bgcolor: 'transparent', width: '100%' }}
                  >
                    <AppChart
                      height={350}
                      options={bceInvOptions.current}
                      series={bceInvSeries.current}
                      type='bar'
                    />
                  </Box>
                </>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              {isgetInverVarMargPending || getInverVarMargData === undefined ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 220,
                        mt: 2
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) =>
                            setMenuIdInverVarMarg(Number(event.target.value))
                          }
                          value={menuIdInverVarMarg.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={index} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Button
                      onClick={() =>
                        customExportToCSV(
                          [...varMarginalMetadata].map(el => el.header),
                          getInverVarMargData,
                          [...varMarginalMetadata].map(el => el.key),
                          'VarMarginalPorProducto.csv',
                          ';'
                        )
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <GenericTable
                    data={getInverVarMargData}
                    entries={varMarginal}
                    metadata={varMarginalMetadata}
                    title='Var Marginal por Producto'
                  />
                </>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              {isgetInverBceMargPending || getInverBceMargData === undefined ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 450,
                        mt: 2,
                        whiteSpace: 'nowrap'
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) =>
                            setMenuIdInverVarMargChart(
                              Number(event.target.value)
                            )
                          }
                          value={menuIdInverVarMargChart.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={index} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Typography
                        component='h5'
                        sx={{ mx: '20px' }}
                        variant='h5'
                      >
                        Trade Group
                      </Typography>
                      {!isgetInverCarteraPending &&
                        getInverCarteraData !== undefined && (
                          <FormControl fullWidth>
                            <Select
                              onChange={(event: SelectChangeEvent) =>
                                setMenuIdInverTradeGroupMargChart(
                                  Number(event.target.value)
                                )
                              }
                              value={menuIdInverTradeGroupMargChart.toString()}
                            >
                              {getInverCarteraData.map((key, index) => (
                                <MenuItem key={key.cartera} value={index}>
                                  {key.cartera}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                    </Box>
                    <Button
                      onClick={() =>
                        exportToCSV(
                          getInverBceMargData,
                          'fxVarInvMargProd.csv',
                          ';'
                        )
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <Box
                    id='chart'
                    sx={{ bgcolor: 'transparent', width: '100%' }}
                  >
                    <AppChart
                      height={350}
                      options={bceInvMargOptions.current}
                      series={bceInvMargSeries.current}
                      type='bar'
                    />
                  </Box>
                </>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Negociacion</h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              {isgetNegoVarProdPending || getNegoVarProdData === undefined ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 220,
                        mt: 2
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) => {
                            setMenuIdNegoVarProd(Number(event.target.value))
                          }}
                          value={menuIdNegoVarProd.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={key} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Button
                      onClick={() =>
                        customExportToCSV(
                          [...varMarginalMetadata].map(el => el.header),
                          getNegoVarProdData,
                          [...varMarginalMetadata].map(el => el.key),
                          'VarPorProducto.csv',
                          ';'
                        )
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <GenericTable
                    data={getNegoVarProdData}
                    entries={varMarginal}
                    metadata={varMarginalMetadata}
                    title='Var por Producto'
                  />
                </>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              {isgetNegoBcePending ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 480,
                        mt: 2,
                        whiteSpace: 'nowrap'
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) =>
                            setMenuIdNegoVarProdChart(
                              Number(event.target.value)
                            )
                          }
                          value={menuIdNegoVarProdChart.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={index} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Typography
                        component='h5'
                        sx={{ mx: '20px' }}
                        variant='h5'
                      >
                        Trade Group
                      </Typography>
                      {!isgetNegoCarteraPending &&
                        getNegoCarteraData !== undefined && (
                          <FormControl fullWidth>
                            <Select
                              onChange={(event: SelectChangeEvent) =>
                                setMenuIdNegoTradeGroupProdChart(
                                  Number(event.target.value)
                                )
                              }
                              value={menuIdNegoTradeGroupProdChart.toString()}
                            >
                              {getNegoCarteraData.map((key, index) => (
                                <MenuItem key={key.cartera} value={index}>
                                  {key.cartera}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                    </Box>
                    <Button
                      onClick={() =>
                        exportToCSV(
                          getNegoBceData ?? [],
                          'fxVarNegoProd.csv',
                          ';'
                        )
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <Box
                    id='chart'
                    sx={{ bgcolor: 'transparent', width: '100%' }}
                  >
                    <AppChart
                      height={350}
                      options={bceNegOptions.current}
                      series={bceNegSeries.current}
                      type='bar'
                    />
                  </Box>
                </>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              {isgetNegoVarMargPending || getNegoVarMargData === undefined ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 220,
                        mt: 2
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) => {
                            setMenuIdNegoVarMarg(Number(event.target.value))
                          }}
                          value={menuIdNegoVarMarg.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={index} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Button
                      onClick={() =>
                        customExportToCSV(
                          [...varMarginalMetadata].map(el => el.header),
                          getNegoVarMargData,
                          [...varMarginalMetadata].map(el => el.key),
                          'VarMarginalPorProducto.csv',
                          ';'
                        )
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <GenericTable
                    data={getNegoVarMargData}
                    entries={varMarginal}
                    metadata={varMarginalMetadata}
                    title='Var Marginal por Producto'
                  />
                </>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              {isgetNegoBceMargPending || getNegoBceMargData === undefined ? (
                <Loading />
              ) : (
                <>
                  <Container
                    sx={{
                      alignItems: 'baseline',
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box
                      sx={{
                        alignItems: 'baseline',
                        display: 'flex',
                        minWidth: 480,
                        mt: 2,
                        whiteSpace: 'nowrap'
                      }}
                    >
                      <Typography
                        component='h5'
                        sx={{ mr: '20px' }}
                        variant='h5'
                      >
                        Área
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          onChange={(event: SelectChangeEvent) =>
                            setMenuIdNegoVarMargChart(
                              Number(event.target.value)
                            )
                          }
                          value={menuIdNegoVarMargChart.toString()}
                        >
                          {areas.map((key, index) => (
                            <MenuItem key={index} value={index}>
                              {key}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Typography
                        component='h5'
                        sx={{ mx: '20px' }}
                        variant='h5'
                      >
                        Trade Group
                      </Typography>
                      {!isgetNegoCarteraPending &&
                        getNegoCarteraData !== undefined && (
                          <FormControl fullWidth>
                            <Select
                              onChange={(event: SelectChangeEvent) =>
                                setMenuIdNegoTradeGroupMargChart(
                                  Number(event.target.value)
                                )
                              }
                              value={menuIdNegoTradeGroupMargChart.toString()}
                            >
                              {getNegoCarteraData.map((key, index) => (
                                <MenuItem key={index} value={index}>
                                  {key.cartera}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                    </Box>
                    <Button
                      onClick={() =>
                        exportToCSV(
                          getNegoBceMargData,
                          'fxVarNegoMarg.csv',
                          ';'
                        )
                      }
                      startIcon={<FileExcelOutlined />}
                      variant='contained'
                    >
                      Descargar excel
                    </Button>
                  </Container>
                  <Box
                    id='chart'
                    sx={{ bgcolor: 'transparent', width: '100%' }}
                  >
                    <AppChart
                      height={350}
                      options={bceNegMargOptions.current}
                      series={bceNegMargSeries.current}
                      type='bar'
                    />
                  </Box>
                </>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
