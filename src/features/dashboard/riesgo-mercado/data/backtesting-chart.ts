import type { ApexOptions } from 'apexcharts'

import { apexAlterPaletteColors } from '~/resources/config/colors'
import { numberFormat } from '~/utils/format-number'

export const backtestingChartOpts: ApexOptions = {
  chart: { id: 'apex-backtesting-neg' },
  colors: apexAlterPaletteColors,
  dataLabels: {
    enabled: false
  },
  xaxis: {
    categories: [''],
    type: 'datetime' as const
  },
  yaxis: {
    labels: {
      formatter: (value: number) => `${numberFormat(value / 1000000, 2)}M`
    }
  }
}
