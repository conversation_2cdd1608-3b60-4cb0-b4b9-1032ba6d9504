/* eslint-disable react/hook-use-state */
import { type Dispatch, type SetStateAction, useState } from 'react'

export const useDebounce = (timeout = 750) => {
  const [debVal, ssDebVal] = useState('')

  let timeoutId: NodeJS.Timeout | undefined

  const setDebVal: Dispatch<SetStateAction<string>> = (
    value: SetStateAction<string>
  ) => {
    if (typeof timeoutId === 'number') clearTimeout(timeoutId)
    timeoutId = setTimeout(ssDebVal, timeout, value)
  }

  return { debVal, setDebVal }
}
