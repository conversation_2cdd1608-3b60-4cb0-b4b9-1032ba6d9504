/* eslint-disable sonarjs/use-type-alias */
import type { FiltroOperaciones } from '@d/admin/models/filtro-operaciones'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class FiltroOperacionesRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/filtroOperaciones'

  readonly deleteFiltroOperaciones = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getFiltroOperaciones = () =>
    this.api.client
      .get<FiltroOperaciones[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertFiltroOperaciones = (filtroOperaciones: FiltroOperaciones) =>
    this.api.client.post<{ message: string }>(this.route, filtroOperaciones)

  readonly updateFiltroOperaciones = (
    id: string,
    filtroOperaciones: FiltroOperaciones
  ) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      filtroOperaciones
    )
}
