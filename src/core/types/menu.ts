import type { AntdIconProps } from '@ant-design/icons/lib/components/AntdIcon'
import type { ChipProps } from '@mui/material'
import type { ForwardRefExoticComponent, ReactNode } from 'react'

import type { GenericCardProps } from './root'

export type LinkTarget = '_blank' | '_parent' | '_self' | '_top'

export type MenuProps = {
  componentDrawerOpen: boolean
  drawerOpen: boolean
  error: null
  menu: NavItemType
  openComponent: string
  openItem: string[]
  selectedID: string | null
}

export type NavItemType = {
  breadcrumbs?: boolean
  caption?: ReactNode | string
  children?: NavItemType[]
  chip?: ChipProps
  color?: 'default' | 'primary' | 'secondary'
  disabled?: boolean
  elements?: NavItemType[]
  external?: boolean
  icon?:
    | ForwardRefExoticComponent<
        Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>
      >
    | GenericCardProps['iconPrimary']
    | string
  id?: string
  search?: string
  target?: boolean
  title?: ReactNode | string
  type?: string
  url?: string
}
