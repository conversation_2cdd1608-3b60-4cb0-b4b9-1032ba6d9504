/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const equity = type({
  day_PL_Total: 'number',
  day_Realised: 'number',
  day_Unrealised: 'number',
  equity: 'string',
  incep_PL_Total: 'number',
  incep_Realised: 'number',
  incep_Unrealised: 'number',
  month_PL_Total: 'number',
  month_Realised: 'number',
  month_Unrealised: 'number',
  pV_ANO: 'number',
  pV_DIA: 'number',
  pV_ICP: 'number',
  pV_MES: 'number',
  pV_NOW: 'number',
  year_PL_Total: 'number',
  year_Realised: 'number',
  year_Unrealised: 'number'
})

export const equityMetadata = new Set([
  {
    header: 'Equity',
    key: 'equity'
  },
  {
    header: 'PV',
    key: 'pV_NOW'
  },
  {
    header: 'PV Día Ant.',
    key: 'pV_DIA'
  },
  {
    header: 'P&L Día',
    key: 'day_PL_Total'
  },
  {
    header: 'PV Inic. Año',
    key: 'pV_ANO'
  },
  {
    header: 'P&L Año',
    key: 'year_PL_Total'
  },
  {
    header: 'PV Orig',
    key: 'pV_ICP'
  },
  {
    header: 'P&L Origen',
    key: 'incep_PL_Total'
  }
])

export type Equity = typeof equity.infer

export const equityValidate = (attempt?: Equity) =>
  equity(attempt) instanceof Error ? resetObject(equity.infer) : attempt
