/* eslint-disable sonarjs/use-type-alias */
import type { Areas } from '@d/admin/models/areas'
import type { FiltroTradeGroup } from '@d/admin/models/filtro-tradegroup'
import type { Productos } from '@d/admin/models/productos'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class TradeGroupRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/tradegroup'

  readonly deleteFiltroTradeGroup = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getAreas = () =>
    this.api.client
      .get<Areas[]>(`${this.route}/areas`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getAreasTitulo = () =>
    this.api.client
      .get<Areas[]>(`${this.route}/areas`)

      .then(apires => apires.data)
      .then(data => data ?? [])
      .then(areas => areas.map(area => area.titulo))

  readonly getFecha = () =>
    this.api.client
      .get<{ fecha: string }[]>(`${this.route}/fechafiltro`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getProductos = () =>
    this.api.client
      .get<Productos[]>(`${this.route}/productos`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getProductosDesc = () =>
    this.api.client
      .get<Productos[]>(`${this.route}/productos`)

      .then(apires => apires.data)
      .then(data => data ?? [])
      .then(productos => productos.map(producto => producto.descripcionCorta))

  readonly getTradeGroups = (tradeProps: {
    areaId?: string
    producto?: string
    tradeGroup?: string
  }) =>
    this.api.client
      .get<{ tradeGroup: string }[]>(
        `${this.route}/${this.attachDate()}`,
        tradeProps
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertFiltroTradeGroup = (filtroTradeGroup: FiltroTradeGroup) =>
    this.api.client.post<{ message: string }>(this.route, filtroTradeGroup)

  readonly updateFiltroTradeGroup = (
    id: string,
    filtroTradeGroup: FiltroTradeGroup
  ) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      filtroTradeGroup
    )

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
