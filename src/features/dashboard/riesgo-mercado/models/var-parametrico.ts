/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const varParametrico = type({
  areaID: 'number',
  spreadGroup: 'string',
  total: 'number',
  yieldCurve: 'number'
})

export const varParametricoMetadata = new Set([
  {
    header: 'Area',
    key: 'areaID'
  },
  {
    header: 'Spread Group',
    key: 'spreadGroup'
  },
  {
    header: 'Yield Curve',
    key: 'yieldCurve'
  },
  {
    header: 'TOTAL',
    key: 'total'
  }
])

export type VarParametrico = typeof varParametrico.infer

export const varParametricoValidate = (attempt?: VarParametrico) =>
  varParametrico(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
