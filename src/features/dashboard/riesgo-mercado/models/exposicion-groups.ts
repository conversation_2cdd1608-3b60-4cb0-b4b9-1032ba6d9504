/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const exposicionGroups = type({
  delta: 'number',
  position: 'number',
  pV: 'number',
  titulo: 'string'
})

export const exposicionGroupsMetadata = new Set([
  {
    header: 'Títu<PERSON>',
    key: 'titulo'
  },
  {
    header: 'Posición',
    key: 'position'
  },
  {
    header: 'PV',
    key: 'pV'
  },
  {
    header: 'Delta',
    key: 'delta'
  }
])

export type ExposicionGroups = typeof exposicionGroups.infer

export const exposicionGroupsValidate = (attempt?: ExposicionGroups) =>
  exposicionGroups(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
