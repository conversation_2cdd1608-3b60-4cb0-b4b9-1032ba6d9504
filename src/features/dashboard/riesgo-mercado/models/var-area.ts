/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const varArea = type({
  areaID: 'string',
  sH: 'number',
  sP: 'number'
})

export const varAreaMetadata = new Set([
  {
    header: 'Area',
    key: 'areaID'
  },
  {
    header: 'Paramétrico',
    key: 'sP'
  },
  {
    header: 'Histórico',
    key: 'sH'
  }
])

export type VarArea = typeof varArea.infer

export const varAreaValidate = (attempt?: VarArea) =>
  varArea(attempt) instanceof type.errors ? resetObject(attempt) : attempt
