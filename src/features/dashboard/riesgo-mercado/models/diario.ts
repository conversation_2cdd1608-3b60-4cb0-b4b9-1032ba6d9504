/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const diario = type({
  cpty: 'string',
  diffAnual: 'number',
  diffDia: 'number',
  diffMes: 'number',
  disponible: 'number',
  disponibleContratacion: 'number?',
  exposicionIM: 'number',
  umbralContratacion: 'number',
  umbralSeguridad: 'number'
})

export const diarioMetadata = new Set([
  {
    header: 'Contrapartida',
    key: 'cpty'
  },
  {
    header: 'Exposición IM',
    key: 'exposicionIM'
  },
  {
    header: 'Diff Día',
    key: 'diffDia'
  },
  {
    header: 'Diff Mes',
    key: 'diffMes'
  },
  {
    header: 'Diff Anual',
    key: 'diffAnual'
  },
  {
    header: 'Umbral Seguridad',
    key: 'umbralSeguridad'
  },
  {
    header: 'Disponible',
    key: 'disponible'
  },
  {
    header: 'Umbral Contratación',
    key: 'umbralContratacion'
  },
  {
    header: 'Dsiponible Contratación',
    key: 'disponibleContratacion'
  }
])

export type Diario = typeof diario.infer

export const diarioValidate = (attempt?: Diario) =>
  diario(attempt) instanceof type.errors ? resetObject(attempt) : attempt
