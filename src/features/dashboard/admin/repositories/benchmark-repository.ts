/* eslint-disable sonarjs/use-type-alias */
import { FechasHistorico } from '@d/admin/models/fechas-historico'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class BenchmarkRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/histbenchmarks'

  readonly deleteBenchmark = (fecha: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${fecha}`)

  readonly getAllBenchmarkDates = () =>
    this.api.client
      .get<FechasHistorico[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getBenchmark = (fecha: string) =>
    this.api.client
      .get<FechasHistorico[]>(`${this.route}/${fecha}`)
      .then(apires => apires.data)
      .then(data => data ?? [])
}
