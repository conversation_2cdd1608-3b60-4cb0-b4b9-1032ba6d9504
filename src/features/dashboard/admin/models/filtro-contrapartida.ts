/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const filtroContrapartidas = type({
  areaId: 'number?',
  codigoAdaptive: 'string?',
  comentarios: 'string?',
  estado: 'number',
  fecha: 'string?',
  id: 'number?',
  nombreContrapartida: 'string?',
  product: 'string?',
  tradeGroup: 'string?'
})

export type FiltroContrapartidas = typeof filtroContrapartidas.infer

export const filtroContrapartidasDefault: FiltroContrapartidas = {
  areaId: 0,
  codigoAdaptive: '',
  comentarios: '',
  estado: 0,
  fecha: '',
  id: 0,
  nombreContrapartida: '',
  product: '',
  tradeGroup: ''
}

export const filtroContrapartidasMetadata = new Set([
  {
    header: 'Filtro Cpty Id',
    key: 'id'
  },
  {
    header: 'Cpty',
    key: 'codigoAdaptive'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Product',
    key: 'product'
  },
  {
    header: 'Nombre Cpty',
    key: 'nombreContrapartida'
  },
  {
    header: 'Comentarios',
    key: 'comentarios'
  },
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Filtro Estado',
    key: 'estado'
  }
])
