import { SearchOutlined } from '@ant-design/icons'
import {
  type AjusteManual,
  ajusteManual,
  ajusteManualMetadata
} from '@d/coap/models/ajuste-manual'
import { desglose, desgloseMetadata } from '@d/coap/models/desglose'
import { equity, equityMetadata } from '@d/coap/models/equity'
import { CoapRepository } from '@d/coap/repository/coap-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  type SelectChangeEvent,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { nth, piped, prop } from 'rambdax'
import type { ChangeEvent } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getDesglose, getDividendos, getEquity } =
    container.get(CoapRepository)

  const [menuIdCDVD, setMenuIdCDVD] = useState(0)
  const [menuIdCREA, setMenuIdCREA] = useState(0)
  const [filterCDVD, setFilterCDVD] = useState('')
  const [filterCREA, setFilterCREA] = useState('')

  const {
    data: getEquityData,
    isFetching: isGetEquityPending,
    refetch: refetchGetEquity
  } = useQuery({
    queryFn: getEquity,
    queryKey: ['getEquity']
  })

  const {
    data: getDesgloseData,
    isFetching: isGetDesglosePending,
    refetch: refetchGetDesglose
  } = useQuery({
    queryFn: getDesglose,
    queryKey: ['getDesglose']
  })

  const {
    data: getDividendosCDVDData,
    isFetching: isGetDividendosCDVDPending,
    refetch: refetchGetDividendosCDVD
  } = useQuery({
    queryFn: () => getDividendos('CDVD'),
    queryKey: ['getDividendosCDVD']
  })

  const {
    data: getDividendosCREAData,
    isFetching: isGetDividendosCREAPending,
    refetch: refetchGetDividendosCREA
  } = useQuery({
    queryFn: () => getDividendos('CREA'),
    queryKey: ['getDividendosCREA']
  })

  const refetch = () => {
    void refetchGetEquity()
    void refetchGetDesglose()
    void refetchGetDividendosCDVD()
    void refetchGetDividendosCREA()
  }

  const filterCDVDData = useMemo(() => {
    if (!filterCDVD.trim()) return getDividendosCDVDData ?? []

    return (getDividendosCDVDData ?? []).filter(item => {
      const value = prop(
        piped(
          [...ajusteManualMetadata].map(el => el.key),
          keys => nth(menuIdCDVD, keys) as keyof AjusteManual
        ),
        item
      )

      if (typeof value === 'string') {
        return value.toLowerCase().includes(filterCDVD.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(filterCDVD)
      }
      return false
    })
  }, [filterCDVD, menuIdCDVD, getDividendosCDVDData, day])

  const filterCREAData = useMemo(() => {
    if (!filterCREA.trim()) return getDividendosCREAData ?? []

    return (getDividendosCREAData ?? []).filter(item => {
      const value = prop(
        piped(
          [...ajusteManualMetadata].map(el => el.key),
          keys => nth(menuIdCREA, keys) as keyof AjusteManual
        ),
        item
      )

      if (typeof value === 'string') {
        return value.toLowerCase().includes(filterCREA.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(filterCREA)
      }
      return false
    })
  }, [filterCREA, menuIdCREA, getDividendosCREAData, day])

  const handleCDVDChange = (event: SelectChangeEvent) => {
    setMenuIdCDVD(Number(event.target.value))
    if (menuIdCDVD === 0) setFilterCDVD('')
  }

  const handleCDVDTextChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => setFilterCDVD(event.currentTarget.value)

  const handleCREAChange = (event: SelectChangeEvent) => {
    setMenuIdCREA(Number(event.target.value))
    if (menuIdCREA === 0) setFilterCREA('')
  }

  const handleCREATextChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => setFilterCREA(event.currentTarget.value)

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            {isGetEquityPending ? (
              <Loading />
            ) : (
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getEquityData ?? []}
                  entries={equity}
                  metadata={equityMetadata}
                  noTotal={false}
                />
              </Paper>
            )}
          </Grid>

          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Desglose</h3>
            </div>
            {isGetDesglosePending ? (
              <Loading />
            ) : (
              <Paper
                elevation={10}
                sx={{
                  ...paperStyle,
                  maxHeight: '325px',
                  overflowY: 'auto'
                }}
              >
                <GenericTable
                  data={getDesgloseData ?? []}
                  entries={desglose}
                  metadata={desgloseMetadata}
                  noTotal={false}
                />
              </Paper>
            )}
          </Grid>
          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Dividendos</h3>
            </div>
            {isGetDividendosCDVDPending ? (
              <Loading />
            ) : (
              <Paper elevation={10} sx={paperStyle}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    height: '70px',
                    justifyContent: 'space-between',
                    pl: '30px',
                    pr: '40px',
                    width: '100%'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      mt: 2
                    }}
                  >
                    <Typography sx={{ mt: 1 }} variant='subtitle1'>
                      Buscar en:
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        minWidth: 200,
                        ml: 2
                      }}
                    >
                      <FormControl fullWidth>
                        <Select
                          id='demo-simple-select'
                          onChange={handleCDVDChange}
                          value={menuIdCDVD.toString()}
                        >
                          {[...ajusteManualMetadata].map((data, index) => (
                            <MenuItem key={index} value={index}>
                              {data.header}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          ml: { md: 1, xs: 0 }
                        }}
                      >
                        <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                          <OutlinedInput
                            aria-describedby='header-search-text'
                            endAdornment={
                              <InputAdornment position='end' sx={{ mr: -0.5 }}>
                                <SearchOutlined />
                              </InputAdornment>
                            }
                            id='header-search'
                            inputProps={{ 'aria-label': 'weight' }}
                            onChange={handleCDVDTextChange}
                            placeholder='Buscar'
                          />
                        </FormControl>
                      </Box>
                    </Box>
                  </Box>
                </Box>
                <GenericTable
                  data={filterCDVDData}
                  entries={ajusteManual}
                  metadata={ajusteManualMetadata}
                  noTotal={false}
                  paginationSize={5}
                />
              </Paper>
            )}
          </Grid>

          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Ajuste sobre Realizado</h3>
            </div>
            {isGetDividendosCREAPending ? (
              <Loading />
            ) : (
              <Paper elevation={10} sx={paperStyle}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    height: '70px',
                    justifyContent: 'space-between',
                    pl: '30px',
                    pr: '40px',
                    width: '100%'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      mt: 2
                    }}
                  >
                    <Typography sx={{ mt: 1 }} variant='subtitle1'>
                      Buscar en:
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        minWidth: 200,
                        ml: 2
                      }}
                    >
                      <FormControl fullWidth>
                        <Select
                          id='demo-simple-select'
                          onChange={handleCREAChange}
                          value={menuIdCREA.toString()}
                        >
                          {[...ajusteManualMetadata].map((data, index) => (
                            <MenuItem key={index} value={index}>
                              {data.header}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          ml: { md: 1, xs: 0 }
                        }}
                      >
                        <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                          <OutlinedInput
                            aria-describedby='header-search-text'
                            endAdornment={
                              <InputAdornment position='end' sx={{ mr: -0.5 }}>
                                <SearchOutlined />
                              </InputAdornment>
                            }
                            id='header-search'
                            inputProps={{ 'aria-label': 'weight' }}
                            onChange={handleCREATextChange}
                            placeholder='Buscar'
                          />
                        </FormControl>
                      </Box>
                    </Box>
                  </Box>
                </Box>
                <GenericTable
                  data={filterCREAData}
                  entries={ajusteManual}
                  metadata={ajusteManualMetadata}
                  noTotal={false}
                  paginationSize={5}
                />
              </Paper>
            )}
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
