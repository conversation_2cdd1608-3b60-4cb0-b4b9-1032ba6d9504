import { FileExcelOutlined } from '@ant-design/icons'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import {
  dolEur,
  dolMetadata,
  eurMetadata
} from '@d/mercado-divisas/models/doleur'
import { MercadoDivisasRepository } from '@d/mercado-divisas/repository/mercado-divisas-repository'
import Button from '@mui/material/Button'
import Checkbox from '@mui/material/Checkbox'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControlLabel from '@mui/material/FormControlLabel'
import FormGroup from '@mui/material/FormGroup'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { type FC, useEffect, useState } from 'react'

import { container } from '~/modules/di-module'
import { customExportToCSV } from '~/utils/csv'

export const DialogDolEur: FC<{
  dataDialog: { balance: string; product: string; tradeGroup: string } | null
  open: boolean
  setOpen: (open: boolean) => void
  title?: string
}> = ({ dataDialog, open, setOpen, title }) => {
  const { getDolar, getEuro } = container.get(MercadoDivisasRepository)

  const queryClient = useQueryClient()

  const [dolarEur, setDolarEur] = useState(true)

  const handleClose = () => {
    void queryClient.invalidateQueries({
      queryKey: ['getDolar', 'getEuro']
    })
    setOpen(false)
  }

  const {
    data: getDolarData,
    error: getDolarError,
    isError: isGetDolarError,
    isFetching: isGetDolarPending,
    refetch: refetchGetDolar
  } = useQuery({
    queryFn: () =>
      getDolar(
        dataDialog?.product ?? '',
        dataDialog?.tradeGroup ?? '',
        dataDialog?.balance ?? ''
      ).then(res =>
        res.map(el => ({
          ...el,
          expiryDate: el.expiryDate.split('T')[0] ?? '',
          startDateAdj: el.startDateAdj.split('T')[0] ?? ''
        }))
      ),
    queryKey: ['getDolar']
  })

  const {
    data: getEuroData,
    error: getEuroError,
    isError: isGetEuroError,
    isFetching: isGetEuroPending,
    refetch: refetchGetEuro
  } = useQuery({
    queryFn: () =>
      getEuro(
        dataDialog?.product ?? '',
        dataDialog?.tradeGroup ?? '',
        dataDialog?.balance ?? ''
      ).then(res =>
        res.map(el => ({
          ...el,
          expiryDate: el.expiryDate.split('T')[0] ?? '',
          startDateAdj: el.startDateAdj.split('T')[0] ?? ''
        }))
      ),
    queryKey: ['getEuro']
  })

  const refetch = () => {
    void refetchGetDolar()
    void refetchGetEuro()
  }

  useEffect(() => {
    refetch()
  }, [dataDialog, open])

  return isGetDolarPending || isGetEuroPending ? (
    <Loading />
  ) : (
    <Dialog
      aria-describedby='alert-dialog-description'
      aria-labelledby='alert-dialog-title'
      maxWidth='xl'
      onClose={handleClose}
      open={open}
      sx={{ '& .MuiPaper-root': { resize: 'horizontal' } }}
    >
      <DialogTitle id='alert-dialog-title'>{title ?? ''}</DialogTitle>
      <DialogContent>
        <Stack
          spacing={2}
          sx={{
            alignItems: 'flex-end',
            display: 'flex',
            flexFlow: 'row nowrap',
            marginY: '5px'
          }}
        >
          <Typography
            sx={{ paddingBottom: '10px', paddingX: '18px' }}
            variant='subtitle1'
          >
            Divisa:
          </Typography>
          <FormGroup row>
            <FormControlLabel
              control={<Checkbox checked={dolarEur} />}
              label='Moneda Extranjera'
              onChange={() => setDolarEur(!dolarEur)}
            />
            <FormControlLabel
              control={<Checkbox checked={!dolarEur} />}
              label='Euros'
              onChange={() => setDolarEur(!dolarEur)}
            />
          </FormGroup>
        </Stack>
        <GenericTable
          data={dolarEur ? (getDolarData ?? []) : (getEuroData ?? [])}
          entries={dolEur}
          metadata={dolarEur ? dolMetadata : eurMetadata}
        />
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() =>
            customExportToCSV(
              (dolarEur ? [...dolMetadata] : [...eurMetadata]).map(
                el => el.header
              ),
              dolarEur ? (getDolarData ?? []) : (getEuroData ?? []),
              (dolarEur ? [...dolMetadata] : [...eurMetadata]).map(
                el => el.key
              ),
              `${title ?? 'data'}.csv`
            )
          }
          startIcon={<FileExcelOutlined />}
          variant='contained'
        >
          Descargar excel
        </Button>
        <Button onClick={handleClose}>Cerrar</Button>
      </DialogActions>
    </Dialog>
  )
}
