import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

import { Semaforo } from '../models/semaforo'

@injectable()
export class CommonRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  readonly getAvailable = () =>
    this.api.client
      .get<Semaforo>(`/semaforo/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data?.estado === '1')

  readonly getFundingRate = () =>
    this.api.client
      .get<{ rate: number }>(`/fundingRate/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data?.rate ?? 0)

  readonly putAvailable = (status: boolean) =>
    this.api.client.put<{ message: string }>(
      `/semaforo/${this.attachDate()}/${status ? '1' : '0'}`
    )

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
