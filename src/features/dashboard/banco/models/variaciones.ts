/* eslint-disable sonarjs/no-unused-collection */
/* eslint-disable unicorn/no-array-for-each */
/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'
import { prop } from 'rambdax'

export const variaciones = type({
  bancaCorporativa: 'number',
  cOAP: 'number',
  fecha: 'string',
  mercadodeDivisas: 'number',
  mercadoMonetario: 'number',
  rentaFija: 'number',
  saldosTesoreros: 'number',
  total: 'number'
})

export const variacionesMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Banca Corporativa',
    key: 'bancaCorporativa'
  },
  {
    header: 'COAP',
    key: 'cOAP'
  },
  {
    header: 'Mercado de Divisas',
    key: 'mercadodeDivisas'
  },
  {
    header: 'Mercado Monetario',
    key: 'mercadoMonetario'
  },
  {
    header: 'Renta Fija',
    key: 'rentaFija'
  },
  {
    header: 'Saldos Tesoreros',
    key: 'saldosTesoreros'
  },
  {
    header: 'Total',
    key: 'total'
  }
])

export type Variaciones = typeof variaciones.infer

export const variacionesSeries = (data: Variaciones[]) => {
  const series: ApexAxisChartSeries = []

  for (const metadata of variacionesMetadata) {
    const serie = Array.of<number[]>()

    for (const row of data) {
      const key = metadata.key as keyof Variaciones

      if (key !== 'fecha') {
        serie.push([new Date(row.fecha).getTime(), prop(key, row)])
      }
    }

    if (metadata.key !== 'fecha')
      series.push({ data: serie, name: metadata.header })
  }

  return series
}
