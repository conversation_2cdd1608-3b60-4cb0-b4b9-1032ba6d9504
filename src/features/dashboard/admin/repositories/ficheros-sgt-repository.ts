/* eslint-disable sonarjs/use-type-alias */
import { CarteraNoExistente } from '@d/admin/models/cartera-noexistente'
import { DatosCargadosSGT } from '@d/admin/models/datos-cargados-sgt'
import { FicherosSGT } from '@d/admin/models/ficheros-sgt'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class FicherosSGTRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/ficherossgt'

  readonly getCarterasNoExistentes = (fecha: string) =>
    this.api.client
      .get<CarteraNoExistente[]>(`${this.route}/carterasNoExistentes/${fecha}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getDatosCargadosSGT = (fecha: string) =>
    this.api.client
      .get<DatosCargadosSGT[]>(`${this.route}/datosCargadosSGT/${fecha}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getListadoSGT = () =>
    this.api.client
      .get<FicherosSGT[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])
}
