/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable sonarjs/void-use */
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

const route = '/admin/configuracion/organizacion/tradegroups'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/trade`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/equivalencia`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/subinforme`)
        setMenuId(2)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('trade')) setMenuId(0)
    if (path.includes('equivalencia')) setMenuId(1)
    if (path.includes('subinforme')) setMenuId(2)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Trade Groups</MenuItem>
                  <MenuItem value={1}>Equivalencia SGT</MenuItem>
                  <MenuItem value={2}>Sub Informes</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Outlet />
        </Grid>
      </Grid>
    </Container>
  )
}
