import { Box, CircularProgress, Typography } from '@mui/material'
import type { FC } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { ThemeMode } from '~/types/resources'

export const Loading: FC<{ notHeight?: boolean; title?: string }> = ({
  notHeight = true,
  title = 'Cargando...'
}) => {
  const { mode } = useContext(ResourceContext)

  return (
    <Box
      sx={{
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'column',
        height: notHeight ? null : '55vh',
        justifyContent: 'center',
        width: '100%'
      }}
    >
      <CircularProgress
        sx={{ color: mode === ThemeMode.DARK ? '#fff' : null }}
      />
      <Typography sx={{ fontStyle: 'italic', mt: 1 }} variant='subtitle1'>
        {title}
      </Typography>
    </Box>
  )
}
