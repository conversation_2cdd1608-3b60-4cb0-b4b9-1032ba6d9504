import { CloseOutlined, MailOutlined } from '@ant-design/icons'
import { IconButton } from '@c/components/IconButton'
import { MainCard } from '@c/components/MainCard'
import { Transitions } from '@c/components/Transitions'
import {
  Box,
  ClickAwayListener,
  Divider,
  List,
  ListItemAvatar,
  ListItemButton,
  ListItemSecondaryAction,
  ListItemText,
  Paper,
  Popper,
  Typography,
  useMediaQuery
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { useRef, useState } from 'react'

import { ThemeMode } from '~/types/resources'

const avatarSX = {
  height: 48,
  width: 48
}

const actionSX = {
  alignSelf: 'flex-start',
  ml: 1,
  mt: '6px',
  right: 'auto',
  top: 'auto',
  transform: 'none'
}

export const Message = () => {
  const theme = useTheme()
  const matchesXs = useMediaQuery(theme.breakpoints.down('md'))

  const anchorRef = useRef<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState(false)
  const handleToggle = () => setOpen(prevOpen => !prevOpen)

  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current?.contains(event.target as Node) ?? false) return

    setOpen(false)
  }

  const iconBackColorOpen =
    theme.palette.mode === ThemeMode.DARK ? 'grey.200' : 'grey.300'
  const iconBackColor =
    theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'grey.100'

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <IconButton
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup='true'
        aria-label='open profile'
        color='secondary'
        onClick={handleToggle}
        ref={anchorRef}
        sx={{
          bgcolor: open ? iconBackColorOpen : iconBackColor,
          color: 'text.primary'
        }}
        variant='light'
      >
        <MailOutlined />
      </IconButton>
      <Popper
        anchorEl={anchorRef.current}
        disablePortal
        open={open}
        placement={matchesXs ? 'bottom' : 'bottom-end'}
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [matchesXs ? -60 : 0, 9]
              }
            }
          ]
        }}
        role={undefined}
        sx={{
          maxHeight: 'calc(100vh - 250px)',
          overflow: 'auto'
        }}
        transition
      >
        {({ TransitionProps }) => (
          <Transitions
            in={open}
            position={matchesXs ? 'top' : 'top-right'}
            type='grow'
            {...TransitionProps}
          >
            <Paper
              sx={{
                boxShadow: theme.customShadows.z1,
                maxWidth: 420,
                minWidth: 285,
                [theme.breakpoints.down('md')]: {
                  maxWidth: 285
                },
                width: '100%'
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard
                  border={false}
                  content={false}
                  elevation={0}
                  secondary={
                    <IconButton onClick={handleToggle} size='small'>
                      <CloseOutlined />
                    </IconButton>
                  }
                  title='Message'
                >
                  <List
                    component='nav'
                    sx={{
                      '& .MuiListItemButton-root': {
                        '& .MuiAvatar-root': avatarSX,
                        '& .MuiListItemSecondaryAction-root': {
                          ...actionSX,
                          position: 'relative'
                        },
                        py: 1.5
                      },
                      p: 0
                    }}
                  >
                    <ListItemButton>
                      <ListItemAvatar></ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant='h6'>
                            It&apos;s{' '}
                            <Typography component='span' variant='subtitle1'>
                              Cristina danny&apos;s
                            </Typography>{' '}
                            birthday today.
                          </Typography>
                        }
                        secondary='2 min ago'
                      />
                      <ListItemSecondaryAction>
                        <Typography noWrap variant='caption'>
                          3:00 AM
                        </Typography>
                      </ListItemSecondaryAction>
                    </ListItemButton>
                    <Divider />
                    <ListItemButton>
                      <ListItemAvatar></ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant='h6'>
                            <Typography component='span' variant='subtitle1'>
                              Aida Burg
                            </Typography>{' '}
                            commented your post.
                          </Typography>
                        }
                        secondary='5 August'
                      />
                      <ListItemSecondaryAction>
                        <Typography noWrap variant='caption'>
                          6:00 PM
                        </Typography>
                      </ListItemSecondaryAction>
                    </ListItemButton>
                    <Divider />
                    <ListItemButton>
                      <ListItemAvatar></ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography component='span' variant='subtitle1'>
                            There was a failure to your setup.
                          </Typography>
                        }
                        secondary='7 hours ago'
                      />
                      <ListItemSecondaryAction>
                        <Typography noWrap variant='caption'>
                          2:45 PM
                        </Typography>
                      </ListItemSecondaryAction>
                    </ListItemButton>
                    <Divider />
                    <ListItemButton>
                      <ListItemAvatar></ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant='h6'>
                            <Typography component='span' variant='subtitle1'>
                              Cristina Danny
                            </Typography>{' '}
                            invited to join{' '}
                            <Typography component='span' variant='subtitle1'>
                              Meeting.
                            </Typography>
                          </Typography>
                        }
                        secondary='Daily scrum meeting time'
                      />
                      <ListItemSecondaryAction>
                        <Typography noWrap variant='caption'>
                          9:10 PM
                        </Typography>
                      </ListItemSecondaryAction>
                    </ListItemButton>
                    <Divider />
                    <ListItemButton sx={{ textAlign: 'center' }}>
                      <ListItemText
                        primary={
                          <Typography color='primary' variant='h6'>
                            View All
                          </Typography>
                        }
                      />
                    </ListItemButton>
                  </List>
                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  )
}
