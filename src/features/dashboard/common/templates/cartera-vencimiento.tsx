import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import {
  type CarteraDesglose,
  carteraDesglose,
  carteraDesgloseMetadata,
  type CarteraResumen,
  carteraResumen,
  carteraResumenMetadata
} from '@d/common/models/cartera'
import { Container, Grid, Paper } from '@mui/material'
import type { FC } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { paperStyle } from '~/resources/config/paper'

export const CarteraVencimiento: FC<{
  desglose: CarteraDesglose[]
  onClick?: (index: number) => void
  resumen: CarteraResumen[]
}> = ({ desglose, onClick, resumen }) => (
  //const { user } = authStore.use()
  <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TitleSubtitle title='Resumen Resultados' />
        <Paper elevation={10} sx={paperStyle}>
          <GenericTable
            data={resumen}
            entries={carteraResumen}
            metadata={carteraDesgloseMetadata}
          />
        </Paper>
      </Grid>
      <Grid item xs={3}>
        {/*user?.role === Role.ADMIN && (
          <>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cobertura derivados</h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <GenericTable
                headers={headersCobertura}
                rows={[]}
                rowsHeaders={rowHeadCobertura}
              />
            </Paper>
          </>
        )*/}
      </Grid>
      <Grid item xs={12}>
        <TitleSubtitle title='Desglose por activos' />
        <Paper elevation={10} sx={paperStyle}>
          <GenericTable
            data={desglose}
            entries={carteraDesglose}
            metadata={carteraResumenMetadata}
            onRowClick={onClick}
          />
        </Paper>
      </Grid>
    </Grid>
  </Container>
)
