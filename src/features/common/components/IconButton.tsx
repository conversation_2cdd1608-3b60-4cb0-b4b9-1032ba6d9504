/* eslint-disable unicorn/no-useless-switch-case */
import {
  alpha,
  IconButton as MuiIconButton,
  type IconButtonProps,
  styled,
  useTheme
} from '@mui/material'
import { forwardRef, type ReactNode, type ReactPortal } from 'react'

import { getColors } from '~/resources/helpers/get-colors'
import { getShadow } from '~/resources/helpers/get-shadow'
import type {
  ButtonVariantProps,
  ExtendedStyleProps,
  IconButtonShapeProps
} from '~/types/extended'

type IconButtonStyleProps = ExtendedStyleProps & {
  variant?: ButtonVariantProps
}

const getColorStyle = ({ color, theme, variant }: IconButtonStyleProps) => {
  const { contrastText, dark, light, lighter, main } = getColors(theme, color)

  // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
  const buttonShadow = `${color}Button`
  const shadows = getShadow(theme, buttonShadow)

  const commonShadow = {
    '&::after': {
      boxShadow: `0 0 6px 6px ${alpha(main, 0.9)}`
    },
    '&:active::after': {
      boxShadow: `0 0 0 0 ${alpha(main, 0.9)}`
    },
    '&:focus-visible': {
      outline: `2px solid ${dark}`,
      outlineOffset: 2
    }
  }

  switch (variant) {
    case 'contained': {
      return {
        '&:hover': {
          backgroundColor: dark
        },
        backgroundColor: main,
        color: contrastText,
        ...commonShadow
      }
    }
    case 'dashed': {
      return {
        '&:hover': {
          borderColor: dark,
          color: dark
        },
        backgroundColor: lighter,
        ...commonShadow
      }
    }
    case 'light': {
      return {
        '&:hover': {
          backgroundColor: light
        },
        backgroundColor: lighter,
        color: main,
        ...commonShadow
      }
    }
    case 'outlined': {
      return {
        '&:hover': {
          backgroundColor: 'transparent',
          borderColor: dark,
          color: dark
        },
        ...commonShadow
      }
    }
    case 'shadow': {
      return {
        '&:hover': {
          backgroundColor: dark,
          boxShadow: 'none'
        },
        backgroundColor: main,
        boxShadow: shadows,
        color: contrastText,
        ...commonShadow
      }
    }
    case 'text':
    case undefined:
    default: {
      return {
        '&:hover': {
          backgroundColor: lighter,
          color: dark
        },
        ...commonShadow
      }
    }
  }
}

const IconButtonStyle = styled(MuiIconButton, {
  shouldForwardProp: prop => prop !== 'variant'
})(
  ({
    color,
    shape,
    theme,
    variant
  }: IconButtonStyleProps & {
    shape?: IconButtonShapeProps
  }) => ({
    '::after': {
      borderRadius: shape === 'rounded' ? '50%' : 4,
      content: '""',
      display: 'block',
      height: '100%',
      left: 0,
      opacity: 0,
      position: 'absolute',
      top: 0,
      transition: 'all 0.5s',
      width: '100%'
    },
    ':active::after': {
      borderRadius: shape === 'rounded' ? '50%' : 4,
      left: 0,
      opacity: 1,
      position: 'absolute',
      top: 0,
      transition: '0s'
    },

    position: 'relative',
    ...(shape === 'rounded' && {
      borderRadius: '50%'
    }),
    ...(variant === 'outlined' && {
      border: '1px solid',
      borderColor: 'inherit'
    }),
    ...(variant === 'dashed' && {
      border: '1px dashed',
      borderColor: 'inherit'
    }),
    ...(variant !== 'text' && {
      '&.Mui-disabled': {
        backgroundColor: theme.palette.grey[200]
      }
    }),
    ...getColorStyle({ color, theme, variant })
  })
)

export const IconButton = Object.assign(
  forwardRef<
    HTMLButtonElement,
    IconButtonProps & {
      children: ReactNode
      shape?: IconButtonShapeProps
      tooltip?: ReactPortal | boolean
      variant?: ButtonVariantProps
    }
  >(
    (
      {
        children,
        color = 'primary',
        shape = 'square',
        tooltip: _,
        variant = 'text',
        ...others
      },
      ref
    ) => {
      const theme = useTheme()

      return (
        <IconButtonStyle
          color={color}
          disableRipple
          ref={ref}
          shape={shape}
          theme={theme}
          variant={variant}
          {...others}
        >
          {children}
        </IconButtonStyle>
      )
    }
  ),
  { displayName: 'IconButton' }
)
