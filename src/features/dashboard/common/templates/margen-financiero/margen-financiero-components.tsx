/* eslint-disable @typescript-eslint/no-misused-spread */
/* eslint-disable sonarjs/no-selector-parameter */
import {
  type ActivoPasivo,
  activoPasivo,
  activoPasivoMetadata
} from '@d/common/models/activo-pasivo'
import {
  Box,
  type SxProps,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography
} from '@mui/material'
import { piped, prop } from 'rambdax'
import type { FC } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { ThemeMode } from '~/types/resources'
import { numberFormat } from '~/utils/format-number'
import { getProps } from '~/utils/models'

type CellConfig = {
  align: 'center' | 'left' | 'right'
  isBold?: boolean
  isPercent?: boolean
  sx?: SxProps
}

type TableMetadata = { key: string; title: string; type: string }

const cellConfig: Record<string, CellConfig> = {
  tipoAnual: { align: 'center', isPercent: true },
  tipoMedio: { align: 'center', isPercent: true },
  tipoMes: { align: 'center', isPercent: true }
}

const getCellConfig = (
  tableMetadata: TableMetadata,
  band = false
): CellConfig => {
  const config = cellConfig[tableMetadata.key]

  if (config !== undefined) return config
  else if (tableMetadata.type === 'number')
    return {
      align: 'right',
      sx: { fontWeight: band ? 'bold' : 'normal' }
    }
  else
    return {
      align: 'left',
      sx: { fontWeight: band ? 'bold' : 'normal' }
    }
}

const getValueFormat = (value: number | string, isPercentage = false) => {
  if (isPercentage) return `${numberFormat(Number(value) * 100, 3)}%`
  else if (typeof value === 'number') return numberFormat(Number(value), 0)
  else return value
}

export const TableMargenFinanciero: FC<{
  data: ActivoPasivo[]
  onRowClick?: (index: number) => void
}> = ({ data, onRowClick }) => {
  const { mode } = useContext(ResourceContext)

  const colorIndCont = useRef(-1)
  const colorRGBScale = useRef(
    mode === ThemeMode.DARK
      ? ['#3b3a3a', '#525151', '#606b64']
      : ['#E5ECEA', '#CBDAD6', '#B2C8C2']
  )

  const tableMetadata: TableMetadata[] = [...activoPasivoMetadata].flatMap(
    data =>
      piped(
        getProps(activoPasivo.toJSON()).find(el => el.key === data.key),
        entry =>
          entry === undefined
            ? []
            : [{ key: data.key, title: data.header, type: entry.value }]
      )
  )

  return data.length === 0 ? (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        my: 2,
        width: '100%'
      }}
    >
      <Typography sx={{ fontStyle: 'italic' }} variant='subtitle1'>
        No hay datos que mostrar
      </Typography>
    </Box>
  ) : (
    <Table size='small'>
      <TableHead
        sx={{
          bgcolor: mode === ThemeMode.DARK ? '#424242' : null,
          borderColor: mode === ThemeMode.DARK ? '#212121' : null
        }}
      >
        <TableRow
          sx={{
            color: mode === ThemeMode.DARK ? '#fff' : '#004B36',
            whiteSpace: 'nowrap'
          }}
        >
          {tableMetadata.map((metadata, index) =>
            piped(getCellConfig(metadata, true), config => (
              <TableCell
                align={config.align}
                key={index}
                sx={{
                  ...config.sx,
                  color: mode === ThemeMode.DARK ? '#fff' : '#004B36'
                }}
              >
                {metadata.title}
              </TableCell>
            ))
          )}
        </TableRow>
      </TableHead>
      <TableBody
        sx={{ bgcolor: mode === ThemeMode.DARK ? ' #212121' : 'white' }}
      >
        {data.map((row, rowIndex) => {
          let band = false
          if (row.nombre.includes('Total')) {
            colorIndCont.current++
            if (colorIndCont.current > 2) colorIndCont.current = 0
            band = true
          }
          return (
            <TableRow
              key={rowIndex}
              onClick={() => onRowClick?.(rowIndex)}
              sx={
                band
                  ? {
                      backgroundColor:
                        colorRGBScale.current[colorIndCont.current],
                      fontWeight: 'bold'
                    }
                  : {}
              }
            >
              {tableMetadata.map((metadata, index) => {
                const value = piped(
                  // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                  prop(metadata.key as keyof ActivoPasivo, row) as
                    | number
                    | string
                    | undefined,
                  value => value ?? ''
                )
                const isNumeric = !Number.isNaN(Number(value))
                const isNegative = isNumeric && Number(value) < 0
                const cellCfg = getCellConfig(metadata, band)

                const sx = {
                  ...(band ? { fontWeight: 'bold' } : {}),
                  ...(isNegative ? { color: 'red' } : {}),
                  ...cellCfg.sx
                }

                return (
                  <TableCell align={cellCfg.align} key={index} sx={sx}>
                    {isNumeric
                      ? getValueFormat(Number(value), cellCfg.isPercent)
                      : value}
                  </TableCell>
                )
              })}
            </TableRow>
          )
        })}
      </TableBody>
    </Table>
  )
}
