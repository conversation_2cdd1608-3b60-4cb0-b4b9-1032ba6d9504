import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type FiltroEstado,
  filtroEstado,
  filtroEstadoDefault,
  filtroEstadoMetadata
} from '@d/admin/models/filtro-estado'
import { FiltroEstadoRepository } from '@d/admin/repositories/filtro-estado-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteFiltroEstado,
    getFiltroEstado,
    insertFiltroEstado,
    updateFiltroEstado
  } = container.get(FiltroEstadoRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(filtroEstadoDefault)

  const {
    data: allFiltroEstadoData,
    isFetching: isGetFiltroEstadoPending,
    refetch: refetchFiltroEstado
  } = useQuery({
    queryFn: getFiltroEstado,
    queryKey: ['getFiltroEstado']
  })

  const submitAction = async (
    incoming: FiltroEstado,
    _: FormikHelpers<FiltroEstado>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertFiltroEstado(incoming)
      : updateFiltroEstado(
          selectedData.current.filtroEstadoID?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchFiltroEstado()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteFiltroEstado(
        selectedData.current.filtroEstadoID?.toString() ?? '0'
      )
      setLoadingDialog(false)
      void refetchFiltroEstado()
    }
  }

  return isGetFiltroEstadoPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchFiltroEstado()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = filtroEstadoDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={allFiltroEstadoData ?? []}
                entries={filtroEstado}
                isDeleteAction
                isEditAction
                metadata={filtroEstadoMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allFiltroEstadoData ?? []) ?? filtroEstadoDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<FiltroEstado>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          codigo: Yup.string().max(20).required('El codigo es requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<FiltroEstado>
              field='estado'
              label='Estado: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.estado?.toString() ?? ''}
            />
            <TextFormControl<FiltroEstado>
              field='comentarios'
              label='Comentarios: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.comentarios?.toString() ?? ''}
            />
            <TextFormControl<FiltroEstado>
              field='codigo'
              label='Código: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.comentarios?.toString() ?? ''}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
