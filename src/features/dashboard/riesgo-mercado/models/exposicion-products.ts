/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const exposicionProducts = type({
  delta: 'number',
  position: 'number',
  product: 'string',
  pV: 'number'
})

export const exposicionProductsMetadata = new Set([
  {
    header: 'Producto',
    key: 'product'
  },
  {
    header: 'Posición',
    key: 'position'
  },
  {
    header: 'PV',
    key: 'pV'
  },
  {
    header: 'Delta',
    key: 'delta'
  }
])

export type ExposicionProducts = typeof exposicionProducts.infer
export const exposicionProductsValidate = (attempt?: ExposicionProducts) =>
  exposicionProducts(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
