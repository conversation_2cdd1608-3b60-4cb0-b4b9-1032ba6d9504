import { store } from '@davstack/store'

export const alertStore = store<{
  message: string | null
  severity: 'error' | 'info' | 'success' | 'warning' | null
  show: boolean
}>({
  message: null,
  severity: null,
  show: false
}).actions(store => ({
  displayError: (message?: string, time = 3000) => {
    store.show.set(true)
    store.message.set(message ?? null)
    store.severity.set('error')
    setTimeout(() => store.show.set(false), time)
  },
  displayInfo: (message?: string, time = 3000) => {
    store.show.set(true)
    store.message.set(message ?? null)
    store.severity.set('success')
    setTimeout(() => store.show.set(false), time)
  }
}))
