/* eslint-disable sonarjs/void-use */
import { Paper } from '@mui/material'
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

import { paperStyle } from '~/resources/config/paper'

const route = '/admin/config-manual'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/ajustes-trading`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/pool`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/derivados`)
        setMenuId(2)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('ajustes-trading')) setMenuId(0)
    if (path.includes('pool')) setMenuId(1)
    if (path.includes('derivados')) setMenuId(2)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Ajustes Trading</MenuItem>
                  <MenuItem value={1}>Pool</MenuItem>
                  <MenuItem value={2}>Ajustes Trading Derivados</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Paper elevation={1} sx={paperStyle}>
            <Outlet />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}
