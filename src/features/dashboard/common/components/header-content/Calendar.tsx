import 'dayjs/locale/es'

import { Box } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import dayjs from 'dayjs'

import { calendarStore } from '@/common/store/calendar-store'

export const Calendar = () => {
  const { actualDay } = calendarStore.use()

  return (
    <Box sx={{ flexShrink: 0 }}>
      <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
        <DatePicker
          onChange={e => calendarStore.setActualDay(e ?? dayjs())}
          slotProps={{ textField: { size: 'small' } }}
          value={dayjs(actualDay)}
        />
      </LocalizationProvider>
    </Box>
  )
}
