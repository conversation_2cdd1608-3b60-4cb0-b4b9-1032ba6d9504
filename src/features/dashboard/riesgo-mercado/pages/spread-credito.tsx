import { AppChart } from '@d/common/components/Chart'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import { expoPieChartOps } from '@d/riesgo-mercado/data/expo-pie-chart'
import {
  spread,
  spreadAreaMetadata,
  spreadSectoresMetadata
} from '@d/riesgo-mercado/models/spread'
import { SpreadRepository } from '@d/riesgo-mercado/repositories/spread-repository'
import { Box, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useRef } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getArea, getTerm } = container.get(SpreadRepository)

  const invSeries = useRef<number[]>([])
  const invOptions = useRef(expoPieChartOps)
  const negSeries = useRef<number[]>([])
  const negOptions = useRef(expoPieChartOps)

  const {
    data: areaInvData,
    error: areaInvError,
    isError: isAreaInvError,
    isFetching: isareaInvPending,
    refetch: refetchAreaInv
  } = useQuery({
    queryFn: () => getArea(),
    queryKey: ['getAreaInvSpread']
  })

  const {
    data: areaNegData,
    error: areaNegError,
    isError: isAreaNegError,
    isFetching: isareaNegPending,
    refetch: refetchAreaNeg
  } = useQuery({
    queryFn: () => getArea('Neg'),
    queryKey: ['getAreaNegSpread']
  })

  const {
    data: termInvData,
    error: termInvError,
    isError: isTermInvError,
    isFetching: istermInvPending,
    refetch: refetchTermInv
  } = useQuery({
    queryFn: () =>
      getTerm().then(data => {
        invOptions.current = { labels: data.map(el => el.curveID ?? '') }
        invSeries.current = data.map(el => el.delta / -1000)
        return data
      }),
    queryKey: ['getTermInvSpread']
  })

  const {
    data: termNegData,
    error: termNegError,
    isError: isTermNegError,
    isFetching: istermNegPending,
    refetch: refetchTermNeg
  } = useQuery({
    queryFn: () =>
      getTerm('Neg').then(data => {
        negOptions.current = { labels: data.map(el => el.curveID ?? '') }
        negSeries.current = data.map(el => el.delta / -1000)
        return data
      }),
    queryKey: ['getTermNegSpread']
  })

  const refetch = () => {
    void refetchAreaInv()
    void refetchAreaNeg()
    void refetchTermInv()
    void refetchTermNeg()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={2}>
          <Grid item sx={{ alignItems: 'start', display: 'flex' }} xs={12}>
            <h2> Cartera de Inversión</h2>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Spread Áreas</h3>
            </div>
            <Fetching
              errorMessage={areaInvError?.message}
              isError={isAreaInvError}
              isFetching={isareaInvPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={areaInvData ?? []}
                  entries={spread}
                  metadata={spreadAreaMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Spread Sectores</h3>
            </div>
            <Fetching
              errorMessage={termInvError?.message}
              isError={isTermInvError}
              isFetching={istermInvPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={termInvData ?? []}
                  entries={spread}
                  metadata={spreadSectoresMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ m: 7 }}></Box>
            <Paper elevation={1} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                <Fetching
                  errorMessage={termInvError?.message}
                  isError={isTermInvError}
                  isFetching={istermInvPending}
                >
                  <AppChart
                    height={305}
                    options={invOptions.current}
                    series={invSeries.current}
                    type='donut'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>
          <Grid item sx={{ alignItems: 'start', display: 'flex' }} xs={12}>
            <h2> Cartera de Negociación</h2>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Spread Áreas</h3>
            </div>
            <Fetching
              errorMessage={areaNegError?.message}
              isError={isAreaNegError}
              isFetching={isareaNegPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={areaNegData ?? []}
                  entries={spread}
                  metadata={spreadAreaMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Spread Sectores</h3>
            </div>
            <Fetching
              errorMessage={termNegError?.message}
              isError={isTermNegError}
              isFetching={istermNegPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={termNegData ?? []}
                  entries={spread}
                  metadata={spreadSectoresMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ m: 7 }}></Box>
            <Paper elevation={1} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                <Fetching
                  errorMessage={termNegError?.message}
                  isError={isTermNegError}
                  isFetching={istermNegPending}
                >
                  <AppChart
                    height={305}
                    options={negOptions.current}
                    series={negSeries.current}
                    type='donut'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
