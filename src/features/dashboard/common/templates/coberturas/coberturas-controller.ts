import type { SelectChangeEvent } from '@mui/material'
import { nth, piped, prop } from 'rambdax'
import { type ChangeEvent, useCallback, useMemo, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { customExportToCSV } from '~/utils/csv'

import {
  type Bonos,
  bonosMetadata,
  type Derivados,
  derivadosMetadata
} from '../../models/coberturas'

export const coberturasController = (
  bonos: Bonos[],
  derivados: Derivados[],
  onBonosClick: (isin: string, book: string) => void
) => {
  const [menuIdDerivados, setMenuIdDerivados] = useState(0)
  const [menuIdBonos, setMenuIdBonos] = useState(0)
  const [filterBonos, setFilterBonos] = useState('')
  const [filterDerivados, setFilterDerivados] = useState('')

  const { actualDay } = calendarStore.use()

  const handleBonosChange = useCallback((event: SelectChangeEvent) => {
    setMenuIdBonos(Number(event.target.value))
    if (menuIdBonos === 0) setFilterBonos('')
  }, [])

  const handleDerivadosChange = useCallback((event: SelectChangeEvent) => {
    setMenuIdDerivados(Number(event.target.value))
    if (menuIdDerivados === 0) setFilterDerivados('')
  }, [])

  const handleBonosTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFilterBonos(event.currentTarget.value),
    []
  )

  const handleDerivadosTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFilterDerivados(event.currentTarget.value),
    []
  )

  const filterBonosData = useMemo(() => {
    if (!filterBonos.trim()) return bonos

    return bonos.filter(item => {
      const value = prop(
        piped(
          [...bonosMetadata].map(el => el.key),
          keys => nth(menuIdBonos, keys) as keyof Bonos
        ),
        item
      )

      if (typeof value === 'string') {
        return value.toLowerCase().includes(filterBonos.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(filterBonos)
      }
      return false
    })
  }, [filterBonos, menuIdBonos, bonos, actualDay])

  const filterDerivadosData = useMemo(() => {
    if (!filterDerivados.trim()) return derivados

    return derivados.filter(item => {
      const value = prop(
        piped(
          [...derivadosMetadata].map(el => el.key),
          keys => nth(menuIdDerivados, keys) as keyof Derivados
        ),
        item
      )

      if (typeof value === 'string') {
        return value.toLowerCase().includes(filterDerivados.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(filterDerivados)
      }
      return false
    })
  }, [filterDerivados, menuIdDerivados, derivados, actualDay])

  const exportBonosCSV = useCallback(
    () =>
      customExportToCSV(
        [...bonosMetadata].map(el => el.header),
        bonos,
        [...bonosMetadata].map(el => el.key),
        'Bonos.csv'
      ),
    [bonos]
  )

  const exportDerivadosCSV = useCallback(
    () =>
      customExportToCSV(
        [...derivadosMetadata].map(el => el.header),
        derivados,
        [...derivadosMetadata].map(el => el.key),
        'Derivados.csv'
      ),
    [derivados]
  )

  const onBonosFilteredClick = (index: number) => {
    const selectedItem = nth(index, filterBonosData)
    onBonosClick(selectedItem?.isin ?? '', selectedItem?.book ?? '')
  }

  return {
    exportBonosCSV,
    exportDerivadosCSV,
    filterBonosData,
    filterDerivadosData,
    handleBonosChange,
    handleBonosTextChange,
    handleDerivadosChange,
    handleDerivadosTextChange,
    menuIdBonos,
    menuIdDerivados,
    onBonosFilteredClick
  }
}
