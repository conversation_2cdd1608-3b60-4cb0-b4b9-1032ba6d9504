/* eslint-disable sonarjs/use-type-alias */
import type { Equivalencia } from '@d/admin/models/equivalencia'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class EquivalenciaRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/equivalenciasgt'

  readonly deleteEquivalencia = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getEquivalencia = () =>
    this.api.client
      .get<Equivalencia[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertEquivalencia = (portfolEquivalencia: Equivalencia) =>
    this.api.client.post<{ message: string }>(this.route, portfolEquivalencia)

  readonly updateEquivalencia = (
    id: string,
    portfolEquivalencia: Equivalencia
  ) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      portfolEquivalencia
    )
}
