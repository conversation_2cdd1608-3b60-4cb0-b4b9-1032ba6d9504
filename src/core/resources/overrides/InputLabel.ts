import type { Theme } from '@mui/material/styles'

export const InputLabel = (theme: Theme) => ({
  MuiInputLabel: {
    styleOverrides: {
      outlined: {
        '&.MuiInputLabel-shrink': {
          background: theme.palette.background.paper,
          lineHeight: '1.4375em',
          marginLeft: -6,
          padding: '0 8px'
        },
        '&.MuiInputLabel-sizeSmall': {
          lineHeight: '1em'
        },
        lineHeight: '0.8em'
      },
      root: {
        color: theme.palette.grey[600]
      }
    }
  }
})
