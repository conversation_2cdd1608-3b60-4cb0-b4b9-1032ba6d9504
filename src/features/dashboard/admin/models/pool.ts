/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'
import dayjs from 'dayjs'

import { resetObject } from '~/utils/models'

export const pool = type({
  ajustePool: 'number?',
  atasPool: 'number?',
  currencyId: 'string?',
  depoDia: 'number?',
  deposAdaptiv: 'number?',
  fecha: 'string',
  id: 'number',
  saldo: 'number',
  tipo: 'number?',
  totalDepo: 'number?',
  tradeGroup: 'string?'
})

export const poolMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Tipo',
    key: 'tipo'
  },
  {
    header: 'Total Depo',
    key: 'totalDepo'
  },
  {
    header: 'Atas Pool',
    key: 'atasPool'
  },
  {
    header: 'Depos Adaptiv',
    key: 'deposAdaptiv'
  },
  {
    header: 'Depo Dia',
    key: 'depoDia'
  },
  {
    header: 'Ajuste Pool',
    key: 'ajustePool'
  }
])

export type Pool = typeof pool.infer

export const poolDefault: Pool = {
  ajustePool: 0,
  atasPool: 0,
  currencyId: '',
  depoDia: 0,
  deposAdaptiv: 0,
  fecha: dayjs().format('YYYY-MM-DD'),
  id: 0,
  saldo: 0,
  tipo: 0,
  totalDepo: 0,
  tradeGroup: ''
}

export const poolValidate = (attempt?: Pool) =>
  pool(attempt) instanceof type.errors ? resetObject(attempt) : attempt
