import type { ApexOptions } from 'apexcharts'

import { numberFormat } from '~/utils/format-number'

export const anSensilChartOpts: ApexOptions = {
  chart: { id: 'apex-bar' },
  dataLabels: { enabled: false },
  plotOptions: { bar: { borderRadius: 4 } },
  xaxis: {
    categories: [''],
    labels: { rotate: -45, style: { fontSize: '12px' } }
  },
  yaxis: {
    labels: {
      formatter: (value: number) => `${numberFormat(value / 1000, 0)}k`
    }
  }
}

export const anSensilDurationChartOpts: ApexOptions = {
  ...anSensilChartOpts,
  yaxis: {
    labels: {
      formatter: (value: number) => `${numberFormat(value / 1000000, 0)}m`
    }
  }
}
