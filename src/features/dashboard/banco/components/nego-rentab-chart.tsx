/* eslint-disable import/default */
import { negoRentabChartOpts } from '@d/banco/data/nego-rentab-chart'
import {
  type ControlTradingChart,
  controlTradingChartSeries
} from '@d/banco/models/nego-ctr'
import {
  type MargenAreas,
  margenAreasSeries
} from '@d/banco/models/rentabilidad'
import { GraficasRepository } from '@d/banco/repository/graficas-repository'
import { AppChart } from '@d/common/components/Chart'
import { Box } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { type FC, useEffect, useRef } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'

export const NegoRentabChart: FC<{
  isNego?: boolean
  temp: 'day' | 'month' | 'year'
}> = ({ isNego = false, temp }) => {
  const day = calendarStore.actualDay.use()
  const {
    getDayNegotiation,
    getMargenCumul,
    getMargenDay,
    getMargenMonth,
    getMonthNegotiation,
    getYearNegotiation
  } = container.get(GraficasRepository)

  const series = useRef<ApexAxisChartSeries>([])

  const fetchChart = async () => {
    switch (temp) {
      case 'day': {
        return isNego ? getDayNegotiation() : getMargenDay()
      }
      case 'month': {
        return isNego ? getMonthNegotiation() : getMargenMonth()
      }
      case 'year': {
        return isNego ? getYearNegotiation() : getMargenCumul()
      }
    }
  }

  const { error, isError, isFetching, refetch } = useQuery({
    queryFn: () =>
      fetchChart().then(data => {
        isNego
          ? controlTradingChartSeries(data as unknown as ControlTradingChart[])
          : margenAreasSeries(data as unknown as MargenAreas[])
        return data
      }),
    queryKey: ['fetchChart']
  })

  useEffect(() => {
    void refetch()
  }, [day])

  return (
    <Fetching
      errorMessage={error?.message}
      isError={isError}
      isFetching={isFetching}
    >
      <Box id='chart' sx={{ bgcolor: 'transparent' }}>
        <AppChart
          height={350}
          options={negoRentabChartOpts(temp, isNego)}
          series={series.current}
          type='area'
        />
      </Box>
    </Fetching>
  )
}
