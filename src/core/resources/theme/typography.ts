import type { TypographyVariantsOptions } from '@mui/material/styles'

import type { FontFamily } from '~/types/resources'

export const Typography = (
  fontFamily: FontFamily
): TypographyVariantsOptions => ({
  body1: {
    fontSize: '0.875rem',
    lineHeight: 1.57
  },
  body2: {
    fontSize: '0.75rem',
    lineHeight: 1.66
  },
  button: {
    textTransform: 'capitalize'
  },
  caption: {
    fontSize: '0.75rem',
    fontWeight: 400,
    lineHeight: 1.66
  },
  fontFamily,
  fontWeightBold: 600,
  fontWeightLight: 300,
  fontWeightMedium: 500,
  fontWeightRegular: 400,
  h1: {
    fontSize: '2.375rem',
    fontWeight: 600,
    lineHeight: 1.21
  },
  h2: {
    fontSize: '1.875rem',
    fontWeight: 600,
    lineHeight: 1.27
  },
  h3: {
    fontSize: '1.5rem',
    fontWeight: 600,
    lineHeight: 1.33
  },
  h4: {
    fontSize: '1.25rem',
    fontWeight: 600,
    lineHeight: 1.4
  },
  h5: {
    fontSize: '1rem',
    fontWeight: 600,
    lineHeight: 1.5
  },
  h6: {
    fontSize: '0.875rem',
    fontWeight: 400,
    lineHeight: 1.57
  },
  htmlFontSize: 16,
  overline: {
    lineHeight: 1.66
  },
  subtitle1: {
    fontSize: '0.875rem',
    fontWeight: 600,
    lineHeight: 1.57
  },
  subtitle2: {
    fontSize: '0.75rem',
    fontWeight: 500,
    lineHeight: 1.66
  }
})
