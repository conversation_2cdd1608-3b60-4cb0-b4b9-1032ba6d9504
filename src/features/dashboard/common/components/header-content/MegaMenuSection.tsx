import { ArrowRightOutlined, WindowsOutlined } from '@ant-design/icons'
import { AnimateButton } from '@c/components/AnimateButton'
import { Dot } from '@c/components/Dot'
import { IconButton } from '@c/components/IconButton'
import { MainCard } from '@c/components/MainCard'
import { Transitions } from '@c/components/Transitions'
import {
  Box,
  Button,
  ClickAwayListener,
  Grid,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  Paper,
  Popper,
  Stack,
  Typography
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { useRef, useState } from 'react'
import { Link } from 'react-router'

import { DRAWER_WIDTH } from '~/constants'
import { ThemeMode } from '~/types/resources'

export const MegaMenuSection = () => {
  const theme = useTheme()

  const anchorRef = useRef<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState(false)
  const handleToggle = () => {
    setOpen(prevOpen => !prevOpen)
  }

  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current?.contains(event.target as Node) ?? false) return
    setOpen(false)
  }

  const iconBackColorOpen =
    theme.palette.mode === ThemeMode.DARK ? 'grey.200' : 'grey.300'
  const iconBackColor =
    theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'grey.100'

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <IconButton
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup='true'
        aria-label='open profile'
        color='secondary'
        onClick={handleToggle}
        ref={anchorRef}
        sx={{
          bgcolor: open ? iconBackColorOpen : iconBackColor,
          color: 'text.primary'
        }}
        variant='light'
      >
        <WindowsOutlined />
      </IconButton>
      <Popper
        anchorEl={anchorRef.current}
        disablePortal
        open={open}
        placement='bottom'
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [-180, 9]
              }
            }
          ]
        }}
        role={undefined}
        transition
      >
        {({ TransitionProps }) => (
          <Transitions
            in={open}
            position='top'
            type='grow'
            {...TransitionProps}
          >
            <Paper
              sx={{
                boxShadow: theme.customShadows.z1,
                maxWidth: 1024,
                minWidth: 750,
                width: {
                  lg: `calc(100vw - ${(DRAWER_WIDTH + 100).toString()}px)`,
                  md: `calc(100vw - 100px)`,
                  xl: `calc(100vw - ${(DRAWER_WIDTH + 140).toString()}px)`
                }
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard border={false} content={false} elevation={0}>
                  <Grid container>
                    <Grid item md={4}>
                      <Box sx={{ p: 4.5, pb: 3 }}>
                        <Stack sx={{ color: 'background.paper' }}>
                          <Typography
                            sx={{ fontSize: '1.875rem', mb: 1 }}
                            variant='h2'
                          >
                            Explore Components
                          </Typography>
                          <Typography variant='h6'>
                            Try our pre made component pages to check how it
                            feels and suits as per your need.
                          </Typography>
                          <Stack
                            alignItems='flex-end'
                            direction='row'
                            justifyContent='space-between'
                            sx={{ mt: -1 }}
                          >
                            <AnimateButton>
                              <Button
                                color='secondary'
                                component={Link}
                                endIcon={<ArrowRightOutlined />}
                                sx={{
                                  '&:hover': {
                                    bgcolor: 'background.paper',
                                    color: 'text.primary'
                                  },
                                  bgcolor: 'background.paper',
                                  color: 'text.primary'
                                }}
                                target='_blank'
                                to='/components-overview/buttons'
                                variant='contained'
                              >
                                View All
                              </Button>
                            </AnimateButton>
                          </Stack>
                        </Stack>
                      </Box>
                    </Grid>
                    <Grid item md={8}>
                      <Box
                        sx={{
                          '& .MuiList-root': {
                            pb: 0
                          },
                          '& .MuiListItemButton-root': {
                            '&:hover': {
                              '& .MuiTypography-root': {
                                color: 'primary.main'
                              },
                              background: 'transparent'
                            },
                            p: 0.5
                          },
                          '& .MuiListSubheader-root': {
                            p: 0,
                            pb: 1.5
                          },
                          p: 4
                        }}
                      >
                        <Grid container spacing={6}>
                          <Grid item xs={4}>
                            <List
                              aria-labelledby='nested-list-user'
                              component='nav'
                              subheader={
                                <ListSubheader id='nested-list-user'>
                                  <Typography
                                    color='textPrimary'
                                    variant='subtitle1'
                                  >
                                    Authentication
                                  </Typography>
                                </ListSubheader>
                              }
                            >
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/auth/login'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Login' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/auth/register'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Register' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/auth/reset-password'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Reset Password' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/auth/forgot-password'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Forgot Password' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/auth/code-verification'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Verification Code' />
                              </ListItemButton>
                            </List>
                          </Grid>
                          <Grid item xs={4}>
                            <List
                              aria-labelledby='nested-list-user'
                              component='nav'
                              subheader={
                                <ListSubheader id='nested-list-user'>
                                  <Typography
                                    color='textPrimary'
                                    variant='subtitle1'
                                  >
                                    Other Pages
                                  </Typography>
                                </ListSubheader>
                              }
                            >
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='About us' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/contact-us'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Contact us' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                to='/pricing'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Pricing' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                to='/apps/profiles/user/payment'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Payment' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/maintenance/under-construction'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Construction' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/maintenance/coming-soon'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Coming Soon' />
                              </ListItemButton>
                            </List>
                          </Grid>
                          <Grid item xs={4}>
                            <List
                              aria-labelledby='nested-list-user'
                              component='nav'
                              subheader={
                                <ListSubheader id='nested-list-user'>
                                  <Typography
                                    color='textPrimary'
                                    variant='subtitle1'
                                  >
                                    SAAS Pages
                                  </Typography>
                                </ListSubheader>
                              }
                            >
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/maintenance/404'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='404 Error' />
                              </ListItemButton>
                              <ListItemButton
                                component={Link}
                                disableRipple
                                target='_blank'
                                to='/'
                              >
                                <ListItemIcon>
                                  <Dot
                                    color='secondary'
                                    size={7}
                                    variant='outlined'
                                  />
                                </ListItemIcon>
                                <ListItemText primary='Landing' />
                              </ListItemButton>
                            </List>
                          </Grid>
                        </Grid>
                      </Box>
                    </Grid>
                  </Grid>
                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  )
}
