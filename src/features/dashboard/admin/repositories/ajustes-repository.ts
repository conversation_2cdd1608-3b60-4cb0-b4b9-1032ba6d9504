/* eslint-disable sonarjs/use-type-alias */
import type { Ajustes } from '@d/admin/models/ajustes'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class AjustesRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/ajustes'

  readonly deleteAjustes = (
    fecha: string,
    areaId: 'COAP' | 'MCAP' | 'MEMO' | 'TESO',
    concepto: string
  ) =>
    this.api.client.delete<{ message: string }>(
      `${this.route}/${fecha}/${areaId}/${concepto}`
    )

  readonly getAjustes = (
    fecha: string,
    areaId: 'COAP' | 'MCAP' | 'MEMO' | 'TESO'
  ) =>
    this.api.client
      .get<Ajustes[]>(`${this.route}/${fecha}/${areaId}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertAjustes = (ajustes: Ajustes) =>
    this.api.client.post<{ message: string }>(this.route, ajustes)

  readonly updateAjustes = (
    fecha: string,
    importe: number,
    accountingAreaID: 'COAP' | 'MCAP' | 'MEMO' | 'TESO',
    concepto: string,
    comentarios?: string
  ) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${fecha}/${importe.toString()}/${accountingAreaID}/${concepto}/${comentarios ?? '0'}`
    )
}
