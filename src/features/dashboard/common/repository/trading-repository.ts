import { DerivadosTrading, Trading } from '@d/common/models/trading'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class TradingRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly derivadosRoute = '/derivados'
  private readonly mercMonetRoute = '/mercadomonetario'
  private readonly route = '/rentafija'

  readonly getMercadoMonetarioTrading = (areaId: string) =>
    this.api.client
      .get<Trading[]>(`${this.mercMonetRoute}/${this.attachDate()}/${areaId}`)

      .then(apires => apires.data)
      .then(data => data ?? Array.of<Trading>())

  readonly getTrading = (areaId: string) =>
    this.api.client
      .get<Trading[]>(`${this.route}/PLRFAMDNEW/${this.attachDate()}/${areaId}`)

      .then(apires => apires.data)
      .then(data => data ?? Array.of<Trading>())

  readonly getTradingSwap = () =>
    this.api.client
      .get<DerivadosTrading[]>(`${this.derivadosRoute}/${this.attachDate()}/14`)

      .then(apires => apires.data)
      .then(data => data ?? Array.of<DerivadosTrading>())

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
