/* eslint-disable @typescript-eslint/no-invalid-void-type */
import { CloseCircleOutlined, DownloadOutlined } from '@ant-design/icons'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormGroup,
  Grid
} from '@mui/material'
import { Formik, type FormikErrors, type <PERSON>ikHelpers } from 'formik'
import type { JSX } from 'react'

type Props<T extends object> = {
  children?: (childProps: {
    errors: FormikErrors<T>
    handleBlur: (event: React.FocusEvent<unknown>) => void
    handleChange: (event: React.ChangeEvent<unknown>) => void
    setFieldValue: (
      field: string,
      value: unknown,
      shouldValidate?: boolean
    ) => void
    values: T
  }) => JSX.Element
  onClose: () => void
  onSubmit: (
    values: T,
    formikHelpers: FormikHelpers<T>
  ) => Promise<unknown> | void
  open: boolean
  title?: string
  validate?: (values: T) => Promise<object> | object | void
  validationSchema?: object
  values: T
}

export const DialogForm = <T extends object>({
  children,
  onClose,
  onSubmit,
  open,
  title = 'Detalles',
  validate,
  validationSchema,
  values
}: Props<T>) => (
  <Dialog
    aria-describedby='create-dialog-description'
    aria-labelledby='create-dialog-title'
    open={open}
  >
    <DialogTitle
      component='h3'
      id='create-dialog-title'
      sx={{ fontSize: '20px', fontWeight: 'bold' }}
      variant='h3'
    >
      {title}
    </DialogTitle>
    <Formik<T>
      initialValues={{ ...values, submit: null }}
      onSubmit={onSubmit}
      validate={validate}
      validationSchema={validationSchema}
    >
      {({
        errors,
        handleBlur,
        handleChange,
        handleSubmit,
        setFieldValue,
        values
      }) => (
        <form onSubmit={handleSubmit}>
          <DialogContent>
            <FormGroup
              sx={{
                alignContent: 'space-between',
                alignItems: 'start',
                display: 'flex',
                flexDirection: 'row'
              }}
            >
              <Grid container spacing={2} sx={{ maxWidth: '350px' }}>
                {children?.({
                  errors,
                  handleBlur,
                  handleChange,
                  setFieldValue,
                  values
                })}
              </Grid>
            </FormGroup>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={onClose}
              startIcon={<CloseCircleOutlined />}
              variant='dashed'
            >
              Cancelar
            </Button>
            <Button
              autoFocus
              startIcon={<DownloadOutlined />}
              type='submit'
              variant='contained'
            >
              Guardar
            </Button>
          </DialogActions>
        </form>
      )}
    </Formik>
  </Dialog>
)
