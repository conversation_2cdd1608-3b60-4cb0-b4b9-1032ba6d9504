/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'
import { prop } from 'rambdax'

export const controlTradingChart = type({
  cOAP: 'number',
  fecha: 'string',
  mercadodeDivisas: 'number',
  mercadoMonetario: 'number',
  rentaFija: 'number',
  total: 'number'
})

export const controlTradingChartMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'COAP',
    key: 'cOAP'
  },
  {
    header: 'Mercado Monetario',
    key: 'mercadoMonetario'
  },
  {
    header: 'Mercado de Divisas',
    key: 'mercadodeDivisas'
  },
  {
    header: 'Renta Fija',
    key: 'rentaFija'
  },
  {
    header: 'Total',
    key: 'total'
  }
])

export type ControlTradingChart = typeof controlTradingChart.infer

export const controlTradingChartSeries = (data: ControlTradingChart[]) => {
  const series: ApexAxisChartSeries = []

  for (const metadata of controlTradingChartMetadata) {
    const serie = Array.of<number[]>()

    for (const row of data) {
      const key = metadata.key as keyof ControlTradingChart

      if (key !== 'fecha') {
        serie.push([new Date(row.fecha).getTime(), prop(key, row)])
      }
    }

    if (metadata.key !== 'fecha')
      series.push({ data: serie, name: metadata.header })
  }

  return series
}
