import { Limites } from '@d/riesgo-mercado/models/limites'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class LimitesRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/limites'

  readonly getLimites = () =>
    this.api.client
      .get<Limites[]>(`${this.route}/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly setCallLimitesConsumUpd = (limDis: string, limConsID: string) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/CallLimitesConsumUpd/${this.attachDate()}/${limDis}/${limConsID}`
    )

  readonly updateLimites = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/updateLimites/${this.attachDate()}`
    )

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
