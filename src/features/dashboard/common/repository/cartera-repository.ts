import type { CarteraDesglose, CarteraResumen } from '@d/common/models/cartera'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class CarteraRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/rentafija'
  readonly getInvDesglose = (areaId: string) =>
    this.api.client
      .get<CarteraDesglose[]>(
        `${this.route}/PLRFINV/${this.attachDate()}/${areaId}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getInvResumen = (areaId: string) =>
    this.api.client
      .get<CarteraResumen[]>(
        `${this.route}/PLRFINV_Summary/${this.attachDate()}/${areaId}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getVtoDesglose = (areaId: string) =>
    this.api.client
      .get<CarteraDesglose[]>(
        `${this.route}/PLRFINV_VTO/${this.attachDate()}/${areaId}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getVtoResumen = (areaId: string) =>
    this.api.client
      .get<CarteraResumen[]>(
        `${this.route}/PLRFINV_VTO_Summary/${this.attachDate()}/${areaId}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
