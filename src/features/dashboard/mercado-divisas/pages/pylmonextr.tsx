import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { trading, tradingMetadata } from '@d/common/models/trading'
import { MercadoDivisasRepository } from '@d/mercado-divisas/repository/mercado-divisas-repository'
import { Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getPlMonedaExtranjera } = container.get(MercadoDivisasRepository)

  const {
    data: plMonedaExtranjeraData,
    isFetching: isPlMonedaExtranjeraPending,
    refetch: refetchPlMonedaExtranjera
  } = useQuery({
    queryFn: getPlMonedaExtranjera,
    queryKey: ['getMonedaExtranjera']
  })

  useEffect(() => {
    void refetchPlMonedaExtranjera()
  }, [day])

  return isPlMonedaExtranjeraPending ? (
    <Loading />
  ) : (
    <>
      <TitleSubtitle title='P&L Moneda Extranjera' />
      <Paper elevation={10} sx={paperStyle}>
        <GenericTable
          data={plMonedaExtranjeraData ?? []}
          entries={trading}
          metadata={tradingMetadata}
        />
      </Paper>
    </>
  )
}
