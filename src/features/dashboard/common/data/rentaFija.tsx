import { BankFilled } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const rentaFija: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'rentaFija-margenFinanciero',
          title: <FormattedMessage id='margenFinanciero' />,
          type: 'item',
          url: '/renta-fija/margen-financiero'
        },
        {
          id: 'rentaFija-trading',
          title: <FormattedMessage id='trading' />,
          type: 'item',
          url: '/renta-fija/trading'
        },
        {
          id: 'rentaFija-carteraInversion',
          title: <FormattedMessage id='carteraInversion' />,
          type: 'item',
          url: '/renta-fija/cartera-inversion'
        },
        {
          id: 'rentaFija-carteraVencimiento',
          title: <FormattedMessage id='carteraVencimiento' />,
          type: 'item',
          url: '/renta-fija/cartera-vencimiento'
        },
        {
          id: 'rentaFija-coberturas',
          title: <FormattedMessage id='coberturas' />,
          type: 'item',
          url: '/renta-fija/coberturas'
        },
        {
          id: 'rentaFija-riesgoMercado',
          title: <FormattedMessage id='riesgoMercado' />,
          type: 'item',
          url: '/renta-fija/riesgo-mercado'
        }
      ],
      icon: BankFilled,
      id: 'rentaFija',
      title: <FormattedMessage id='rentaFija' />,
      type: 'collapse'
    }
  ],
  icon: BankFilled,
  id: 'group-rentaFija',
  title: <FormattedMessage id='rentaFija' />,
  type: 'group'
}
