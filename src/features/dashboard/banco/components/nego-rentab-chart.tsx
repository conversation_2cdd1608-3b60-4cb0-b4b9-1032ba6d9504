/* eslint-disable import/default */
import { negoRentabChartOpts } from '@d/banco/data/nego-rentab-chart'
import { controlTradingChartSeries } from '@d/banco/models/nego-ctr'
import { margenAreasSeries } from '@d/banco/models/rentabilidad'
import { GraficasRepository } from '@d/banco/repository/graficas-repository'
import { AppChart } from '@d/common/components/Chart'
import { Loading } from '@d/common/components/Loading'
import { Box } from '@mui/material'
import { type FC, useEffect, useRef, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const NegoRentabChart: FC<{
  isNego?: boolean
  temp: 'day' | 'month' | 'year'
}> = ({ isNego = false, temp }) => {
  const day = calendarStore.actualDay.use()
  const {
    getDayNegotiation,
    getMargenCumul,
    getMargenDay,
    getMargenMonth,
    getMonthNegotiation,
    getYearNegotiation
  } = container.get(GraficasRepository)

  const series = useRef<ApexAxisChartSeries>([])

  const [isLoading, setIsLoading] = useState(false)

  const fetchChart = async () => {
    setIsLoading(true)

    switch (temp) {
      case 'day': {
        series.current = isNego
          ? controlTradingChartSeries(await getDayNegotiation())
          : margenAreasSeries(await getMargenDay())
        break
      }
      case 'month': {
        series.current = isNego
          ? controlTradingChartSeries(await getMonthNegotiation())
          : margenAreasSeries(await getMargenMonth())
        break
      }
      case 'year': {
        series.current = isNego
          ? controlTradingChartSeries(await getYearNegotiation())
          : margenAreasSeries(await getMargenCumul())
        break
      }
    }

    setIsLoading(false)
  }

  useEffect(() => {
    void fetchChart()
  }, [day])

  return (
    <Box id='chart' sx={{ bgcolor: 'transparent' }}>
      {isLoading ? (
        <Loading />
      ) : (
        <AppChart
          height={350}
          options={negoRentabChartOpts(temp, isNego)}
          series={series.current}
          type='area'
        />
      )}
    </Box>
  )
}
