import { LineChartOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import { Role, type UserProfile } from '~/types/auth'

const riesgoMercadoAdmin = [
  {
    id: 'riesgoMercado-exposicion',
    title: <FormattedMessage id='exposicion' />,
    type: 'item',
    url: '/riesgo-mercado/exposicion'
  },
  {
    id: 'riesgoMercado-varArea',
    title: <FormattedMessage id='varArea' />,
    type: 'item',
    url: '/riesgo-mercado/var-area'
  },
  {
    id: 'riesgoMercado-varMarginal',
    title: <FormattedMessage id='varMarginal' />,
    type: 'item',
    url: '/riesgo-mercado/var-marginal'
  },
  {
    id: 'riesgoMercado-backtesting',
    title: <FormattedMessage id='backtesting' />,
    type: 'item',
    url: '/riesgo-mercado/backtesting'
  },
  {
    id: 'riesgoMercado-stress',
    title: <FormattedMessage id='stress' />,
    type: 'item',
    url: '/riesgo-mercado/stress'
  },
  {
    id: 'riesgoMercado-initialMargin',
    title: <FormattedMessage id='initialMargin' />,
    type: 'item',
    url: '/riesgo-mercado/initial-margin'
  },
  {
    id: 'riesgoMercado-analisisSensibilidades',
    title: <FormattedMessage id='analisisSensibilidades' />,
    type: 'item',
    url: '/riesgo-mercado/analisis-sensibilidades'
  },
  {
    id: 'riesgoMercado-spreadCredito',
    title: <FormattedMessage id='spreadCredito' />,
    type: 'item',
    url: '/riesgo-mercado/spread-credito'
  },
  {
    id: 'riesgoMercado-limites',
    title: <FormattedMessage id='limites' />,
    type: 'item',
    url: '/riesgo-mercado/limites'
  }
]

const riesgoMercadoUser = [
  {
    id: 'riesgoMercado-initialMargin',
    title: <FormattedMessage id='initialMargin' />,
    type: 'item',
    url: '/riesgo-mercado/initial-margin'
  },
  {
    id: 'riesgoMercado-varArea',
    title: <FormattedMessage id='varArea' />,
    type: 'item',
    url: '/riesgo-mercado/var-area'
  },
  {
    id: 'riesgoMercado-limites',
    title: <FormattedMessage id='limites' />,
    type: 'item',
    url: '/riesgo-mercado/limites'
  }
]

export const riesgoMercado = (user?: UserProfile | null) => ({
  children: [
    {
      children:
        user?.role === Role.ADMIN ? riesgoMercadoAdmin : riesgoMercadoUser,
      icon: LineChartOutlined,
      id: 'riesgoMercado',
      title: <FormattedMessage id='riesgoMercado' />,
      type: 'collapse'
    }
  ],
  icon: LineChartOutlined,
  id: 'group-riesgoMercado',
  title: <FormattedMessage id='riesgoMercado' />,
  type: 'group'
})
