import { SearchOutlined } from '@ant-design/icons'
import { CheckFormControl } from '@d/admin/components/check-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Trade,
  trade,
  tradeDefault,
  tradeMetadata
} from '@d/admin/models/trade'
import { TradeRepository } from '@d/admin/repositories/trade-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  type SelectChangeEvent,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth, piped, prop } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { deleteTrade, getTrade, insertTrade, updateTrade } =
    container.get(TradeRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [menuId, setMenuId] = useState(0)
  const { areas } = configStore.use()
  const [filterTradeText, setFilterTradeText] = useState('')
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(tradeDefault)

  const {
    data: allTradeData,
    isFetching: isGetTradePending,
    refetch: refetchTrade
  } = useQuery({
    queryFn: getTrade,
    queryKey: ['getTrade']
  })

  const handleChange = (event: SelectChangeEvent) => {
    setMenuId(Number(event.target.value))
  }

  const handleTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFilterTradeText(event.currentTarget.value),
    []
  )

  const filterTrade = useMemo(() => {
    if (!filterTradeText.trim()) return allTradeData ?? []

    return (allTradeData ?? []).filter(item => {
      const value = prop(
        piped(
          [...tradeMetadata].map(el => el.key),
          keys => nth(menuId, keys) as keyof Trade
        ),
        item
      )
      if (typeof value === 'string') {
        return value.toLowerCase().includes(filterTradeText.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(filterTradeText)
      }
      return false
    })
  }, [filterTradeText, menuId, allTradeData])

  const submitAction = async (incoming: Trade, _: FormikHelpers<Trade>) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertTrade(incoming)
      : updateTrade(
          selectedData.current.tradeGroupID?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchTrade()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteTrade(selectedData.current.tradeGroupID?.toString() ?? '0')
      setLoadingDialog(false)
      void refetchTrade()
    }
  }

  return isGetTradePending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchTrade()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = tradeDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          {(allTradeData ?? []).length > 0 && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                height: '70px',
                justifyContent: 'space-between',
                pl: '30px',
                pr: '40px',
                width: '100%'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  mt: 2
                }}
              >
                <Typography sx={{ mt: 1 }} variant='subtitle1'>
                  Buscar en:
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    minWidth: 200,
                    ml: 2
                  }}
                >
                  <FormControl fullWidth>
                    <Select
                      id='demo-simple-select'
                      onChange={handleChange}
                      value={menuId.toString()}
                    >
                      {[...tradeMetadata].map((data, index) => (
                        <MenuItem key={index} value={index}>
                          {data.header}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      ml: { md: 1, xs: 0 }
                    }}
                  >
                    <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                      <OutlinedInput
                        aria-describedby='header-search-text'
                        endAdornment={
                          <InputAdornment position='end' sx={{ mr: -0.5 }}>
                            <SearchOutlined />
                          </InputAdornment>
                        }
                        id='header-search'
                        inputProps={{ 'aria-label': 'weight' }}
                        onChange={handleTextChange}
                        placeholder='Buscar'
                      />
                    </FormControl>
                  </Box>
                </Box>
              </Box>
            </Box>
          )}
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={filterTrade}
                entries={trade}
                isDeleteAction
                isEditAction
                metadata={tradeMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allTradeData ?? []) ?? tradeDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Trade>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          trade: Yup.string().max(20).required('Trade group is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Trade>
              field='tradeGroup'
              label='Trade Group: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.tradeGroup.toString()}
            />
            <TextFormControl<Trade>
              field='accountingAreaID'
              label='Accounting area ID: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.accountingAreaID?.toString() ?? ''}
            />
            <SelectFormControl<Trade>
              field='areaID'
              label='Area: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.areaID?.toString() ?? ''}
            >
              {areas.map((area, ind) => (
                <MenuItem key={ind} value={ind}>
                  {area.titulo}
                </MenuItem>
              ))}
            </SelectFormControl>
            <CheckFormControl<Trade>
              field='excludedFromPLRF'
              label='Excluded From Plrf: '
              onChange={e => handleChange(e as ChangeEvent)}
              required
              value={values.excludedFromPLRF ?? false}
            />
            <CheckFormControl<Trade>
              field='excludedFromVaR'
              label='Excluded From VaR: '
              onChange={e => handleChange(e as ChangeEvent)}
              required
              value={values.excludedFromVaR ?? false}
            />
            <CheckFormControl<Trade>
              field='investment'
              label='Investment: '
              onChange={e => handleChange(e as ChangeEvent)}
              required
              value={values.excludedFromVaR ?? false}
            />
            <TextFormControl<Trade>
              field='description'
              label='Descripción: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.description?.toString() ?? '0'}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
