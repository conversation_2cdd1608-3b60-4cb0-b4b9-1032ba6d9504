import { ActivoPasivo } from '@d/common/models/activo-pasivo'
import { DesgloseDerivadoCobertura } from '@d/common/models/desglose-derivado-cobertura'
import { DesgloseProductoActivoPasivo } from '@d/common/models/desglose-producto-activo-pasivo'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class ActivoPasivoRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly mercDivRoute = '/mercadodivisas'
  private readonly route = '/saldostesoreros'

  readonly desgloseMFDerivadoCobertura = (isin: string, areaId: string) =>
    this.api.client
      .get<DesgloseDerivadoCobertura[]>(
        `${this.route}/desgloseMFDerivadoCobertura/${this.attachDate()}/${isin}/${areaId}`
      )
      .then(apires => apires.data)
      .then(data => data ?? Array.of<DesgloseDerivadoCobertura>())

  readonly desgloseProductoActivo = (
    producto: string,
    balance: string,
    tradeGroup: string
  ) =>
    this.api.client
      .get<DesgloseProductoActivoPasivo[]>(
        `${this.route}/desgloseProductoActivo/${this.attachDate()}/${producto}/${balance}/${tradeGroup}`
      )

      .then(apires => apires.data)
      .then(data => data ?? Array.of<DesgloseProductoActivoPasivo>())

  readonly desgloseProductoPasivo = (
    producto: string,
    balance: string,
    tradeGroup: string
  ) =>
    this.api.client
      .get<DesgloseProductoActivoPasivo[]>(
        `${this.route}/desgloseProductoPasivo/${this.attachDate()}/${producto}/${balance}/${tradeGroup}`
      )
      .then(apires => apires.data)
      .then(data => data ?? Array.of<DesgloseProductoActivoPasivo>())

  readonly getActivo = (areaId: string) =>
    this.api.client
      .get<ActivoPasivo[]>(
        `${this.route}/activo/${this.attachDate()}/${areaId}`
      )

      .then(apires => apires.data)
      .then(data => data ?? Array.of<ActivoPasivo>())

  readonly getPasivo = (areaId: string) =>
    this.api.client
      .get<ActivoPasivo[]>(
        `${this.route}/pasivo/${this.attachDate()}/${areaId}`
      )

      .then(apires => apires.data)
      .then(data => data ?? Array.of<ActivoPasivo>())

  readonly showTradeGroup = (
    balance: string,
    areaID: string,
    epigrafeID: string
  ) =>
    this.api.client
      .get<ActivoPasivo[]>(
        `${this.route}/showTradeGroup/${this.attachDate()}/${balance}/${areaID}/${epigrafeID}`
      )

      .then(apires => apires.data)
      .then(data => data ?? Array.of<ActivoPasivo>())

  readonly showTradeGroupDivisa = (
    balance: string,
    areaID: string,
    epigrafeID: string
  ) =>
    this.api.client
      .get<ActivoPasivo[]>(
        `${this.mercDivRoute}/showTradeGroupsDivisa/${this.attachDate()}/${areaID}/${balance}/${epigrafeID}`
      )
      .then(apires => apires.data)
      .then(data => data ?? Array.of<ActivoPasivo>())

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
