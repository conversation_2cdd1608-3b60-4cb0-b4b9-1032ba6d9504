import { CalendarioIntereses } from '@d/banco/models/calendario-intereses'
import { ControlDPVRFHTMRF } from '@d/banco/models/control-RF'
import { ControlDPVRV } from '@d/banco/models/control-RV'
import { ControlTrading } from '@d/banco/models/control-trading'
import { MargenCalendarizado } from '@d/banco/models/margen-calendarizado'
import { MargenDeltasSummary } from '@d/banco/models/margen-deltas-summary'
import { MargenSummary } from '@d/banco/models/margen-summary'
import { SumMargen } from '@d/banco/models/sum-margen'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class BancoRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly calIntRoute = '/calendariointereses'
  private readonly route = '/balance'

  readonly getAnual = () =>
    this.api.client
      .get<CalendarioIntereses[]>(
        `${this.calIntRoute}/anual/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getControlDPVRF = () =>
    this.api.client
      .get<ControlDPVRFHTMRF[]>(
        `${this.route}/controlDPVRF/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getControlDPVRV = () =>
    this.api.client
      .get<ControlDPVRV[]>(`${this.route}/controlDPVRV/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getControlHTMRF = () =>
    this.api.client
      .get<ControlDPVRFHTMRF[]>(
        `${this.route}/controlHTMRF/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getControlTrading = () =>
    this.api.client
      .get<ControlTrading[]>(
        `${this.route}/controlTrading/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getCuadreContable = () =>
    this.api.client
      .get<CalendarioIntereses[]>(
        `${this.calIntRoute}/cuadreContable/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMargenCalendarizado = () =>
    this.api.client
      .get<MargenCalendarizado[]>(
        `${this.calIntRoute}/margenCalendarizado/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMargenDeltasSummary = () =>
    this.api.client
      .get<MargenDeltasSummary[]>(
        `${this.route}/margenDeltasSummary/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMargenSummary = () =>
    this.api.client
      .get<MargenSummary[]>(`${this.route}/margenSumary/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMes = () =>
    this.api.client
      .get<CalendarioIntereses[]>(
        `${this.calIntRoute}/mes/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getPatrimonioNeto = () =>
    this.api.client
      .get<CalendarioIntereses[]>(
        `${this.route}/patrimonioNeto/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getSumMargen = (areaId: string) =>
    this.api.client
      .get<SumMargen[]>(
        `${this.route}/sumMargen/${this.attachDate()}/${areaId}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
