/* eslint-disable sonarjs/no-nested-functions */
import { AppChart } from '@d/common/components/Chart'
import { Reload } from '@d/common/components/Reload'
import { varAreaChartOpts } from '@d/riesgo-mercado/data/var-area-chart'
import {
  estadistica,
  estadisticaMetadata
} from '@d/riesgo-mercado/models/estadistica'
import type { HistoricoVar } from '@d/riesgo-mercado/models/historico-var'
import { varArea, varAreaMetadata } from '@d/riesgo-mercado/models/var-area'
import {
  varParametrico,
  varParametricoMetadata
} from '@d/riesgo-mercado/models/var-parametrico'
import { VarAreaRepository } from '@d/riesgo-mercado/repositories/var-area-repository'
import { Box, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { useEffect, useRef } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { GenericTable } from '@/dashboard/common/components/generic-table/GenericTable'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

const seriesGen = (data: HistoricoVar[]) => [
  {
    data: data.map(el => [dayjs(el.fecha).valueOf(), el.dia]),
    name: 'Dia'
  },
  {
    data: data.map(el => [dayjs(el.fecha).valueOf(), el.maximos]),
    name: 'Máximos'
  },
  {
    data: data.map(el => [dayjs(el.fecha).valueOf(), el.media]),
    name: 'Media'
  }
]

export default () => {
  const day = calendarStore.actualDay.use()

  const {
    getEstadisticaInv,
    getEstadisticaNeg,
    getHistoricoVar,
    getVarArea,
    getVarParametrico
  } = container.get(VarAreaRepository)

  const histInvSHSeries = useRef<ApexAxisChartSeries>([])
  const histInvSPSeries = useRef<ApexAxisChartSeries>([])
  const histNegSHSeries = useRef<ApexAxisChartSeries>([])
  const histNegSPSeries = useRef<ApexAxisChartSeries>([])

  const {
    data: getVarAreaInvData,
    error: getVarAreaInvError,
    isError: isGetVarAreaInvError,
    isFetching: isgetVarAreaInvPending,
    refetch: refetchGetVarAreaInv
  } = useQuery({
    queryFn: () => getVarArea(),
    queryKey: ['getGetVarAreaInv']
  })

  const {
    data: getVarAreaNegData,
    error: getVarAreaNegError,
    isError: isGetVarAreaNegError,
    isFetching: isgetVarAreaNegPending,
    refetch: refetchGetVarAreaNeg
  } = useQuery({
    queryFn: () => getVarArea('Negociacion'),
    queryKey: ['getGetVarAreaNeg']
  })

  const {
    data: getEstadisticaInvData,
    error: getEstadisticaInvError,
    isError: isGetEstadisticaInvError,
    isFetching: isgetEstadisticaInvPending,
    refetch: refetchGetEstadisticaInv
  } = useQuery({
    queryFn: getEstadisticaInv,
    queryKey: ['getEstadisticaInv']
  })

  const {
    data: getEstadisticaNegData,
    error: getEstadisticaNegError,
    isError: isGetEstadisticaNegError,
    isFetching: isgetEstadisticaNegPending,
    refetch: refetchGetEstadisticaNeg
  } = useQuery({
    queryFn: getEstadisticaNeg,
    queryKey: ['getEstadisticaNeg']
  })

  const {
    data: getVarParametricoInvData,
    error: getVarParametricoInvError,
    isError: isGetVarParametricoInvError,
    isFetching: isgetVarParametricoInvPending,
    refetch: refetchGetVarParametricoInv
  } = useQuery({
    queryFn: () => getVarParametrico('Inversion'),
    queryKey: ['getVarParametrico']
  })

  const {
    data: getVarParametricoNegData,
    error: getVarParametricoNegError,
    isError: isGetVarParametricoNegError,
    isFetching: isgetVarParametricoNegPending,
    refetch: refetchGetVarParametricoNeg
  } = useQuery({
    queryFn: () => getVarParametrico('Negociacion'),
    queryKey: ['getVarParametricoNeg']
  })

  const {
    error: getHistoricoVarInvSHError,
    isError: isGetHistoricoVarInvSHError,
    isFetching: isgetHistoricoVarInvSHPending,
    refetch: refetchGetHistoricoVarInvSH
  } = useQuery({
    queryFn: () =>
      getHistoricoVar().then(data => {
        histInvSHSeries.current = seriesGen(data)
        return data
      }),
    queryKey: ['getHistoricoVarInvSH']
  })

  const {
    error: getHistoricoVarNegSHError,
    isError: isGetHistoricoVarNegSHError,
    isFetching: isgetHistoricoVarNegSHPending,
    refetch: refetchGetHistoricoVarNegSH
  } = useQuery({
    queryFn: () =>
      getHistoricoVar('Negociacion').then(data => {
        histNegSHSeries.current = seriesGen(data)
        return data
      }),
    queryKey: ['getHistoricoVarNegSH']
  })

  const {
    error: getHistoricoVarInvSPError,
    isError: isGetHistoricoVarInvSPError,
    isFetching: isgetHistoricoVarInvSPPending,
    refetch: refetchGetHistoricoVarInvSP
  } = useQuery({
    queryFn: () =>
      getHistoricoVar('Inversion', 'SP').then(data => {
        histInvSPSeries.current = seriesGen(data)
        return data
      }),
    queryKey: ['getHistoricoVarInvSP']
  })

  const {
    error: getHistoricoVarNegSPError,
    isError: isGetHistoricoVarNegSPError,
    isFetching: isgetHistoricoVarNegSPPending,
    refetch: refetchGetHistoricoVarNegSP
  } = useQuery({
    queryFn: () =>
      getHistoricoVar('Negociacion', 'SP').then(data => {
        histNegSPSeries.current = seriesGen(data)
        return data
      }),
    queryKey: ['getHistoricoVarNegSP']
  })

  const refetch = () => {
    void refetchGetVarAreaInv()
    void refetchGetVarAreaNeg()
    void refetchGetEstadisticaInv()
    void refetchGetEstadisticaNeg()
    void refetchGetVarParametricoInv()
    void refetchGetVarParametricoNeg()
    void refetchGetHistoricoVarInvSH()
    void refetchGetHistoricoVarNegSH()
    void refetchGetHistoricoVarInvSP()
    void refetchGetHistoricoVarNegSP()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera disponible / VaR Áreas</h3>
            </div>
            <Fetching
              errorMessage={getVarAreaInvError?.message}
              isError={isGetVarAreaInvError}
              isFetching={isgetVarAreaInvPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getVarAreaInvData ?? []}
                  entries={varArea}
                  metadata={varAreaMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera disponible / Estadísticas</h3>
            </div>
            <Fetching
              errorMessage={getEstadisticaInvError?.message}
              isError={isGetEstadisticaInvError}
              isFetching={isgetEstadisticaInvPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getEstadisticaInvData ?? []}
                  entries={estadistica}
                  metadata={estadisticaMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}></Grid>
          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> </h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent', width: '100%' }}>
                <Fetching
                  errorMessage={getHistoricoVarInvSHError?.message}
                  isError={isGetHistoricoVarInvSHError}
                  isFetching={isgetHistoricoVarInvSHPending}
                >
                  <AppChart
                    height={350}
                    options={varAreaChartOpts}
                    series={histInvSHSeries.current}
                    type='line'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Cartera disponible / VaR Paramétrico</h3>
            </div>
            <Fetching
              errorMessage={getVarParametricoInvError?.message}
              isError={isGetVarParametricoInvError}
              isFetching={isgetVarParametricoInvPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getVarParametricoInvData ?? []}
                  entries={varParametrico}
                  metadata={varParametricoMetadata}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Cartera disponible / VaR Paramétrico</h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent', width: '100%' }}>
                <Fetching
                  errorMessage={getHistoricoVarInvSPError?.message}
                  isError={isGetHistoricoVarInvSPError}
                  isFetching={isgetHistoricoVarInvSPPending}
                >
                  <AppChart
                    height={350}
                    options={varAreaChartOpts}
                    series={histInvSPSeries.current}
                    type='line'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera negociación / VaR Áreas</h3>
            </div>
            <Fetching
              errorMessage={getVarAreaNegError?.message}
              isError={isGetVarAreaNegError}
              isFetching={isgetVarAreaNegPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getVarAreaNegData ?? []}
                  entries={varArea}
                  metadata={varAreaMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera negociación / Estadísticas</h3>
            </div>
            <Fetching
              errorMessage={getEstadisticaNegError?.message}
              isError={isGetEstadisticaNegError}
              isFetching={isgetEstadisticaNegPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getEstadisticaNegData ?? []}
                  entries={estadistica}
                  metadata={estadisticaMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}></Grid>
          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> </h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent', width: '100%' }}>
                <Fetching
                  errorMessage={getHistoricoVarNegSHError?.message}
                  isError={isGetHistoricoVarNegSHError}
                  isFetching={isgetHistoricoVarNegSHPending}
                >
                  <AppChart
                    height={350}
                    options={varAreaChartOpts}
                    series={histNegSHSeries.current}
                    type='line'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Cartera negociación / VaR Paramétrico</h3>
            </div>
            <Fetching
              errorMessage={getVarParametricoNegError?.message}
              isError={isGetVarParametricoNegError}
              isFetching={isgetVarParametricoNegPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getVarParametricoNegData ?? []}
                  entries={varParametrico}
                  metadata={varParametricoMetadata}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera negociación / VaR Paramétrico</h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent', width: '100%' }}>
                <Fetching
                  errorMessage={getHistoricoVarNegSPError?.message}
                  isError={isGetHistoricoVarNegSPError}
                  isFetching={isgetHistoricoVarNegSPPending}
                >
                  <AppChart
                    height={350}
                    options={varAreaChartOpts}
                    series={histNegSPSeries.current}
                    type='line'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
