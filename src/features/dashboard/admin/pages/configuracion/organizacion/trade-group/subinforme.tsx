import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import {
  type Subinformes,
  subinformes,
  subinformesDefault,
  subinformesMetadata
} from '@d/admin/models/subinformes'
import { SubinformesRepository } from '@d/admin/repositories/subinformes-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, MenuItem, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteSubinformes,
    getSubinformes,
    insertSubinformes,
    updateSubinformes
  } = container.get(SubinformesRepository)

  const { areas } = configStore.use()
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(subinformesDefault)

  const {
    data: allSubinformesData,
    isFetching: isGetSubinformesPending,
    refetch: refetchSubinformes
  } = useQuery({
    queryFn: getSubinformes,
    queryKey: ['getSubinformes']
  })

  const submitAction = async (
    incoming: Subinformes,
    _: FormikHelpers<Subinformes>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertSubinformes(incoming)
      : updateSubinformes(
          selectedData.current.breakDownId?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchSubinformes()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteSubinformes(
        selectedData.current.breakDownId?.toString() ?? '0'
      )
      setLoadingDialog(false)
      void refetchSubinformes()
    }
  }

  return isGetSubinformesPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchSubinformes()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = subinformesDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allSubinformesData ?? []}
                entries={subinformes}
                isDeleteAction
                isEditAction
                metadata={subinformesMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allSubinformesData ?? []) ?? subinformesDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Subinformes>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          nombreLargoSGT: Yup.string().max(50).required('Nombre es requerido'),
          trade: Yup.string().max(20).required('Trade group requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <SelectFormControl<Subinformes>
              field='areaID'
              label='Area: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.areaID.toString()}
            >
              {areas.map((area, ind) => (
                <MenuItem key={ind} value={ind}>
                  {area.titulo}
                </MenuItem>
              ))}
            </SelectFormControl>
            <SelectFormControl<Subinformes>
              field='tradeGroupID'
              label='TradeGroup: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.areaID.toString()}
            ></SelectFormControl>
          </>
        )}
      </DialogForm>
    </>
  )
}
