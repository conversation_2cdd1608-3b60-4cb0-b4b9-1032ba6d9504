/* eslint-disable unicorn/no-keyword-prefix */
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import {
  fondoArcano,
  fondoArcanoDefault,
  fondoArcanoMetadata
} from '@d/admin/models/fondo-arcano'
import { FondoArcanoRepository } from '@d/admin/repositories/fondo-arcano-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  OutlinedInput,
  Paper
} from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import { delay, nth } from 'rambdax'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { deleteFondoArcano, getFondoArcano, insertFondoArcano } =
    container.get(FondoArcanoRepository)

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [titulo, setTitulo] = useState('')
  const [valLiq, setValLiq] = useState('')
  const [date, setDate] = useState<Dayjs | null>(dayjs())

  const disabledButton = useMemo(
    () => !!(date === null || titulo === '' || valLiq === ''),
    [date, titulo, valLiq]
  )

  const selectedData = useRef(fondoArcanoDefault)

  const {
    data: fondoArcanoData,
    isFetching: isFondoArcanoPending,
    refetch: refetchFondoArcano
  } = useQuery({
    queryFn: getFondoArcano,
    queryKey: ['getFondoArcano']
  })

  const calculatePl = async () => {
    setLoadingDialog(true)
    await insertFondoArcano(
      date?.format('YYYY-MM-DD') ?? dayjs().format('YYYY-MM-DD'),
      titulo,
      valLiq
    )
    setLoadingDialog(false)
    void refetchFondoArcano()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteFondoArcano(selectedData.current.fecha)
      setLoadingDialog(false)
      void refetchFondoArcano()
    }
  }

  return isFondoArcanoPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchFondoArcano()} />
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ alignItems: 'center', display: 'flex' }} xs={12}>
            <Box sx={{ width: '100%' }}>
              <Grid item xs={12}>
                <LocalizationProvider
                  adapterLocale='es'
                  dateAdapter={AdapterDayjs}
                >
                  <DatePicker<dayjs.Dayjs>
                    defaultValue={dayjs().subtract(1, 'day')}
                    onChange={setDate}
                    slotProps={{ textField: { size: 'small' } }}
                  />
                </LocalizationProvider>
              </Grid>
            </Box>
            <Box sx={{ width: '100%' }}>
              <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                <OutlinedInput
                  id='my-label-id'
                  onChange={e => setTitulo(e.target.value)}
                  placeholder='N° Títulos'
                  type='number'
                />
              </FormControl>
            </Box>
            <Box sx={{ width: '100%' }}>
              <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                <OutlinedInput
                  id='my-label-id'
                  onChange={e => setValLiq(e.target.value)}
                  placeholder='Valor Liquidativo'
                  type='number'
                />
              </FormControl>
            </Box>
            <Box sx={{ width: '100%' }}>
              <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                <Button
                  disabled={disabledButton}
                  onClick={calculatePl}
                  sx={{
                    background: '#E5F1EE',
                    color: 'green',
                    marginRight: '5px'
                  }}
                  variant='contained'
                >
                  Calcular PL
                </Button>
              </FormControl>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '400px',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={fondoArcanoData ?? []}
                entries={fondoArcano}
                isDeleteAction
                metadata={fondoArcanoMetadata}
                onDeleteClick={index => {
                  selectedData.current =
                    nth(index, fondoArcanoData ?? []) ?? fondoArcanoDefault
                  setOpenDeleteDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog open={loadingDialog} />
    </>
  )
}
