import { ControlTradingChart } from '@d/banco/models/nego-ctr'
import { MargenAreas } from '@d/banco/models/rentabilidad'
import { Variaciones } from '@d/banco/models/variaciones'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class GraficasRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/graficas'

  readonly getDayNegotiation = () =>
    this.api.client
      .get<ControlTradingChart[]>(
        `${this.route}/controlTrading_Hist/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMargenCumul = () =>
    this.api.client
      .get<MargenAreas[]>(`${this.route}/margenCumul/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMargenDay = () =>
    this.api.client
      .get<MargenAreas[]>(
        `${this.route}/margenDiaAreas_Hist/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMargenMonth = () =>
    this.api.client
      .get<MargenAreas[]>(
        `${this.route}/margenMonthAreas_Hist_lag/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMonthNegotiation = () =>
    this.api.client
      .get<ControlTradingChart[]>(
        `${this.route}/controlTrading_AcumuladoMensual/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getVariaciones = () =>
    this.api.client
      .get<Variaciones[]>(
        `${this.route}/margenDiaAreas_Hist_lag/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getYearNegotiation = () =>
    this.api.client
      .get<ControlTradingChart[]>(
        `${this.route}/controlTrading_AcumuladoAnual/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
