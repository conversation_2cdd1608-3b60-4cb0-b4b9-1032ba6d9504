/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const plFechas = type({
  acumuladoANUAL: 'number',
  acumuladoMES: 'number',
  fecha: 'string',
  pLCONTADO_MESACONTADO: 'number',
  pLCONTADO_MESAPLAZO: 'number',
  pLPLAZO_MESAPLAZO: 'number',
  pLTOTAL: 'number',
  pLTOTAL_MESAPLAZO: 'number'
})

export const plFechasMetadata = new Set([
  {
    header: 'P&L Mesa Contado',
    key: 'pLCONTADO_MESACONTADO'
  },
  {
    header: 'P&L Mesa Plazo',
    key: 'pLCONTADO_MESAPLAZO'
  },
  {
    header: 'P&L Total',
    key: 'pLTOTAL'
  }
])

export const plFechasHistoricoMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'P&L Mesa Contado',
    key: 'pLCONTADO_MESACONTADO'
  },
  {
    header: 'P&L Mesa Plazo',
    key: 'pLCONTADO_MESAPLAZO'
  },
  {
    header: 'P&L Día',
    key: 'pLTOTAL'
  },
  {
    header: 'P&L Mes',
    key: 'acumuladoMES'
  },
  {
    header: 'P&L Año',
    key: 'acumuladoANUAL'
  }
])

export type PlFechas = typeof plFechas.infer

export const plFechasValidate = (attempt?: PlFechas) =>
  plFechas(attempt) instanceof type.errors ? resetObject(attempt) : attempt
