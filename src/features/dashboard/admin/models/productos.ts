/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const productos = type({
  descripcionCorta: 'string',
  descripcionLarga: 'string',
  nombreActivo: 'string',
  nombrePasivo: 'string',
  orden: 'number?',
  product: 'string',
  productID: 'number'
})

export type Productos = typeof productos.infer

export const productosMetadata = new Set([
  {
    header: 'Producto',
    key: 'product'
  },
  {
    header: 'Descripción Larga',
    key: 'descripcionLarga'
  },
  {
    header: 'Descripción Corta',
    key: 'descripcionCorta'
  },
  {
    header: 'Nombre Activo',
    key: 'nombreActivo'
  },
  {
    header: 'Nombre Pasivo',
    key: 'nombrePasivo'
  },
  {
    header: 'Orden',
    key: 'orden'
  }
])

export const productosDefault: Productos = {
  descripcionCorta: '',
  descripcionLarga: '',
  nombreActivo: '',
  nombrePasivo: '',
  product: '',
  productID: 0
}

export const productosValidate = (attempt?: Productos) =>
  productos(attempt) instanceof type.errors ? resetObject(attempt) : attempt
