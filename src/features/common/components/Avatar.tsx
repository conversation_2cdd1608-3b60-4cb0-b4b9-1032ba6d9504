/* eslint-disable unicorn/no-useless-switch-case */
import {
  Avatar as MuiAvatar,
  type AvatarProps,
  styled,
  type Theme,
  useTheme
} from '@mui/material'
import type { FC, ReactNode } from 'react'

import { getColors } from '~/resources/helpers/get-colors'
import type {
  AvatarTypeProps,
  ColorProps,
  ExtendedStyleProps,
  SizeProps
} from '~/types/extended'

const getColorsStyle = ({
  color,
  theme,
  type
}: ExtendedStyleProps & {
  type?: AvatarTypeProps
}) => {
  const { contrastText, light, main } = getColors(theme, color)

  switch (type ?? 'filled') {
    case 'combined': {
      return {
        backgroundColor: light,
        border: '1px solid',
        borderColor: light,
        color: main
      }
    }
    case 'filled': {
      return {
        backgroundColor: main,
        color: contrastText
      }
    }
    case 'outlined': {
      return {
        backgroundColor: 'transparent',
        border: '1px solid',
        borderColor: main,
        color: main
      }
    }
    default: {
      return {
        backgroundColor: light,
        color: main
      }
    }
  }
}

const getSizeStyle = (size?: SizeProps) => {
  switch (size ?? 'md') {
    case 'badge': {
      return {
        border: '2px solid',
        fontSize: '0.675rem',
        height: 20,
        width: 20
      }
    }
    case 'lg': {
      return {
        fontSize: '1.2rem',
        height: 52,
        width: 52
      }
    }
    case 'sm': {
      return {
        fontSize: '0.875rem',
        height: 32,
        width: 32
      }
    }
    case 'xl': {
      return {
        fontSize: '1.5rem',
        height: 64,
        width: 64
      }
    }
    case 'xs': {
      return {
        fontSize: '0.75rem',
        height: 24,
        width: 24
      }
    }
    case 'md':
    default: {
      return {
        fontSize: '1rem',
        height: 40,
        width: 40
      }
    }
  }
}

const AvatarStyle = styled(MuiAvatar, {
  shouldForwardProp: prop =>
    prop !== 'color' && prop !== 'type' && prop !== 'size'
})(
  ({
    color,
    size,
    theme,
    type
  }: {
    color: ColorProps
    size?: SizeProps
    theme: Theme
    type?: AvatarTypeProps
  }) => ({
    ...getSizeStyle(size),
    ...getColorsStyle({ color, theme, type }),
    ...(size === 'badge' && {
      borderColor: theme.palette.background.default
    })
  })
)

export const Avatar: FC<
  AvatarProps & {
    children?: ReactNode | string
    color?: ColorProps
    size?: SizeProps
    type?: AvatarTypeProps
  }
> = ({
  children,
  color = 'primary',
  size = 'md',
  type,
  variant = 'circular',
  ...others
}) => {
  const theme = useTheme()

  return (
    <AvatarStyle
      color={color}
      size={size}
      theme={theme}
      type={type}
      variant={variant}
      {...others}
    >
      {children}
    </AvatarStyle>
  )
}
