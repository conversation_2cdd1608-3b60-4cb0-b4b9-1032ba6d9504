/* eslint-disable sonarjs/void-use */
import { Paper } from '@mui/material'
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

import { paperStyle } from '~/resources/config/paper'

const route = '/admin/input-manual'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/otros-saldos/banco-espana`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/fondo-arcano`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/ajustes/margen-financiero`)
        setMenuId(2)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('otros-saldos/banco-espana')) setMenuId(0)
    if (path.includes('fondo-arcano')) setMenuId(1)
    if (path.includes('ajustes/margen-financiero')) setMenuId(2)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Otros Saldos</MenuItem>
                  <MenuItem value={1}>Fondo Arcano</MenuItem>
                  <MenuItem value={2}>Ajustes</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Paper elevation={1} sx={paperStyle}>
            <Outlet />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}
