import type { Theme } from '@mui/material/styles'

import { getColors } from '~/resources/helpers/get-colors'
import type { ExtendedStyleProps } from '~/types/extended'

const getColorStyle = ({ color, theme }: ExtendedStyleProps) => {
  const { lighter, main } = getColors(theme, color)

  return {
    backgroundColor: lighter,
    color: main
  }
}

export const Badge = (theme: Theme) => {
  const defaultLightBadge = getColorStyle({ color: 'primary', theme })

  return {
    MuiBadge: {
      styleOverrides: {
        light: {
          ...defaultLightBadge,
          '&.MuiBadge-colorError': getColorStyle({ color: 'error', theme }),
          '&.MuiBadge-colorInfo': getColorStyle({ color: 'info', theme }),
          '&.MuiBadge-colorPrimary': getColorStyle({ color: 'primary', theme }),
          '&.MuiBadge-colorSecondary': getColorStyle({
            color: 'secondary',
            theme
          }),
          '&.MuiBadge-colorSuccess': getColorStyle({ color: 'success', theme }),
          '&.MuiBadge-colorWarning': getColorStyle({ color: 'warning', theme })
        },
        standard: {
          height: theme.spacing(2),
          minWidth: theme.spacing(2),
          padding: theme.spacing(0.5)
        }
      }
    }
  }
}
