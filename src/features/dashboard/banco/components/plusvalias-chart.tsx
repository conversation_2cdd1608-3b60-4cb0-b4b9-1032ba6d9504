/* eslint-disable import/default */
import { plusvaliasChartOpt } from '@d/banco/data/plusvalias-chart'
import { BancoRepository } from '@d/banco/repository/banco-repository'
import { AppChart } from '@d/common/components/Chart'
import { Box, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { ApexOptions } from 'apexcharts'
import { useEffect, useRef } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'

const paperStyle = {
  alignItems: 'left',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

export const PlusvaliasChart = () => {
  const day = calendarStore.actualDay.use()
  const { getControlDPVRF } = container.get(BancoRepository)

  const series = useRef<ApexAxisChartSeries>([])
  const options = useRef<ApexOptions>(plusvaliasChartOpt)

  const {
    error: plusvaliasError,
    isError: isPlusvaliasError,
    isFetching: isPlusvaliasPending,
    refetch: refetchPlusvalias
  } = useQuery({
    queryFn: () =>
      getControlDPVRF().then(data => {
        series.current = [{ data: data.map(el => el.plusvalia) }]
        options.current = {
          ...options.current,
          xaxis: {
            ...options.current.xaxis,
            categories: data.map(el => el.titulo ?? '')
          }
        }
        return data
      }),
    queryKey: ['getControlDPVRF']
  })

  useEffect(() => {
    void refetchPlusvalias()
  }, [day])

  return (
    <Grid item xs={6}>
      <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
        <TitleSubtitle title='Plusvalías Latentes DPV Renta Fija' />
      </div>
      <Fetching
        errorMessage={plusvaliasError?.message}
        isError={isPlusvaliasError}
        isFetching={isPlusvaliasPending}
      >
        <Paper elevation={10} sx={paperStyle}>
          <Box id='chart' sx={{ bgcolor: 'transparent' }}>
            <AppChart
              height={250}
              options={options.current}
              series={series.current}
              type='bar'
            />
          </Box>
        </Paper>
      </Fetching>
    </Grid>
  )
}
