/* eslint-disable react-hooks/exhaustive-deps */
import {
  BorderInnerOutlined,
  CloseCircleOutlined,
  HighlightOutlined,
  LayoutOutlined,
  SettingOutlined
} from '@ant-design/icons'
import { IconButton } from '@c/components/IconButton'
import { MainCard } from '@c/components/MainCard'
import { SimpleBarScroll } from '@c/components/SimpleBar'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Drawer,
  Stack,
  Typography
} from '@mui/material'
import { useTheme } from '@mui/material/styles'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { ThemeMode } from '~/types/resources'

import { ThemeLayout } from './ThemeLayout'
import { ThemeMenuLayout } from './ThemeMenuLayout'
import { ThemeModeLayout } from './ThemeMode'

export const Customization = () => {
  const theme = useTheme()
  const { menuOrientation, miniDrawer, mode, themeDirection } =
    useContext(ResourceContext)

  const themeLayout = useMemo(
    () => <ThemeLayout />,
    [miniDrawer, themeDirection]
  )
  const themeMenuLayout = useMemo(() => <ThemeMenuLayout />, [menuOrientation])
  const themeMode = useMemo(() => <ThemeModeLayout />, [mode])

  const [open, setOpen] = useState(false)
  const handleToggle = () => {
    setOpen(!open)
  }

  const iconBackColorOpen =
    theme.palette.mode === ThemeMode.DARK ? 'grey.200' : 'grey.300'
  const iconBackColor =
    theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'grey.100'

  return (
    <>
      <Box sx={{ flexShrink: 0, ml: 0.75 }}>
        <IconButton
          aria-label='settings toggler'
          color='secondary'
          onClick={handleToggle}
          sx={{
            bgcolor: open ? iconBackColorOpen : iconBackColor,
            color: 'text.primary'
          }}
          variant='light'
        >
          <SettingOutlined />
        </IconButton>
      </Box>
      <Drawer
        anchor='right'
        onClose={handleToggle}
        open={open}
        PaperProps={{ sx: { width: 340 } }}
        sx={{ zIndex: 2001 }}
      >
        {open && (
          <MainCard
            content={false}
            secondary={
              <IconButton
                onClick={handleToggle}
                shape='rounded'
                size='small'
                sx={{ color: 'background.paper' }}
              >
                <CloseCircleOutlined style={{ fontSize: '1.15rem' }} />
              </IconButton>
            }
            sx={{
              '& .MuiCardHeader-root': {
                '& .MuiTypography-root': { fontSize: '1rem' },
                bgcolor: 'primary.main',
                color: 'background.paper'
              },
              border: 'none',
              borderRadius: 0,
              height: '100vh'
            }}
            title='Preferencias'
          >
            <SimpleBarScroll
              sx={{
                '& .simplebar-content': {
                  display: 'flex',
                  flexDirection: 'column'
                }
              }}
            >
              <Box
                sx={{
                  '& .MuiAccordion-root': {
                    '& .Mui-expanded': {
                      color: theme.palette.primary.main
                    },
                    '& .MuiAccordionDetails-root': {
                      border: 'none'
                    },
                    '& .MuiAccordionSummary-root': {
                      bgcolor: 'transparent',
                      flexDirection: 'row',
                      pl: 1
                    },
                    borderColor: theme.palette.divider
                  },
                  height: 'calc(100vh - 64px)'
                }}
              >
                <Accordion defaultExpanded sx={{ borderTop: 'none' }}>
                  <AccordionSummary
                    aria-controls='panel1d-content'
                    id='panel1d-header'
                  >
                    <Stack alignItems='center' direction='row' spacing={1.5}>
                      <IconButton
                        aria-label='settings toggler'
                        color='primary'
                        disableRipple
                        onClick={handleToggle}
                        sx={{ bgcolor: 'primary.lighter' }}
                      >
                        <LayoutOutlined />
                      </IconButton>
                      <Stack>
                        <Typography color='textPrimary' variant='subtitle1'>
                          Diseño del tema
                        </Typography>
                        <Typography color='textSecondary' variant='caption'>
                          Elija uno...
                        </Typography>
                      </Stack>
                    </Stack>
                  </AccordionSummary>
                  <AccordionDetails>{themeLayout}</AccordionDetails>
                </Accordion>
                <Accordion defaultExpanded>
                  <AccordionSummary
                    aria-controls='panel4d-content'
                    id='panel4d-header'
                  >
                    <Stack alignItems='center' direction='row' spacing={1.5}>
                      <IconButton
                        aria-label='settings toggler'
                        color='primary'
                        disableRipple
                        onClick={handleToggle}
                        sx={{ bgcolor: 'primary.lighter' }}
                      >
                        <BorderInnerOutlined />
                      </IconButton>
                      <Stack>
                        <Typography color='textPrimary' variant='subtitle1'>
                          Orientación del menú
                        </Typography>
                        <Typography color='textSecondary' variant='caption'>
                          Elija horizontal o vertical...
                        </Typography>
                      </Stack>
                    </Stack>
                  </AccordionSummary>
                  <AccordionDetails>{themeMenuLayout}</AccordionDetails>
                </Accordion>
                <Accordion defaultExpanded>
                  <AccordionSummary
                    aria-controls='panel2d-content'
                    id='panel2d-header'
                  >
                    <Stack alignItems='center' direction='row' spacing={1.25}>
                      <IconButton
                        aria-label='settings toggler'
                        color='primary'
                        disableRipple
                        onClick={handleToggle}
                        sx={{ bgcolor: 'primary.lighter' }}
                      >
                        <HighlightOutlined />
                      </IconButton>
                      <Stack>
                        <Typography color='textPrimary' variant='subtitle1'>
                          Modo
                        </Typography>
                        <Typography color='textSecondary' variant='caption'>
                          Elija modo claro o modo oscuro
                        </Typography>
                      </Stack>
                    </Stack>
                  </AccordionSummary>
                  <AccordionDetails>{themeMode}</AccordionDetails>
                </Accordion>
              </Box>
            </SimpleBarScroll>
          </MainCard>
        )}
      </Drawer>
    </>
  )
}
