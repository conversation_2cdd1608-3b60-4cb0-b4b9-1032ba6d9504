import { DialogTable } from '@d/common/components/DialogTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  detallesDerivadosCobertura,
  detallesDerivadosCoberturaMetadata
} from '@d/common/models/detalles-derivados-cobertura'
import { CarteraRepository } from '@d/common/repository/cartera-repository'
import { DetallesDerivdosCoberturaRepository } from '@d/common/repository/detalles-derivados-cobertura-repository'
import { CarteraInversion } from '@d/common/templates/cartera-inversion/cartera-inversion'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import type { CarteraDesglose } from '@/dashboard/common/models/cartera'
import { container } from '~/modules/di-module'

export default () => {
  const day = calendarStore.actualDay.use()

  const [openDialog, setOpenDialog] = useState(false)

  const desgloseSelected = useRef<CarteraDesglose | null>(null)

  const { getInvDesglose, getInvResumen } = container.get(CarteraRepository)

  const { getDetallesDerivadosCobertura } = container.get(
    DetallesDerivdosCoberturaRepository
  )

  const {
    data: detallesData,
    error: detallesError,
    isError: isDetallesError,
    isFetching: isDetallesPending,
    refetch: refetchDetalles
  } = useQuery({
    enabled: !!desgloseSelected.current,
    queryFn: () =>
      getDetallesDerivadosCobertura(
        desgloseSelected.current?.referencia ?? '',
        desgloseSelected.current?.tradeGroup ?? ''
      ).then(data => {
        setOpenDialog(true)
        return data
      }),
    queryKey: ['getDetallesDerivadosCobertura']
  })

  const {
    data: invSummaryData,
    error: invSummaryError,
    isError: isInvSummaryError,
    isFetching: isInvSummaryPending,
    refetch: refetchInvSummary
  } = useQuery({
    queryFn: () => getInvResumen('4'),
    queryKey: ['getINVSummaryRentaFija']
  })

  const {
    data: plrfinvDesgloseData,
    error: plrfinvDesgloseError,
    isError: isPLRFINVDesgloseError,
    isFetching: isPLRFINVDesglosePending,
    refetch: refetchPLRFINVDesglose
  } = useQuery({
    queryFn: () => getInvDesglose('4'),
    queryKey: ['getPLRFINVDesglose']
  })

  const refetch = () => {
    void refetchInvSummary()
    void refetchPLRFINVDesglose()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <Fetching
      errorMessage={invSummaryError?.message ?? plrfinvDesgloseError?.message}
      fullHeight
      isError={isInvSummaryError || isPLRFINVDesgloseError}
      isFetching={isInvSummaryPending || isPLRFINVDesglosePending}
    >
      <Reload onClick={refetch} />
      <CarteraInversion
        desglose={plrfinvDesgloseData ?? []}
        onClick={rowIndex => {
          desgloseSelected.current =
            nth(rowIndex, plrfinvDesgloseData ?? []) ?? null
          void refetchDetalles()
        }}
        resumen={invSummaryData ?? []}
      />
      <LoadingDialog open={isDetallesPending} />
      <DialogTable
        data={(detallesData ?? []).map(el => ({
          ...el,
          archiveDate: el.archiveDate.split('T')[0] ?? ''
        }))}
        entries={detallesDerivadosCobertura}
        error={detallesError?.message}
        isError={isDetallesError}
        metadata={detallesDerivadosCoberturaMetadata}
        open={openDialog}
        rowsHeaders={[]}
        setOpen={setOpenDialog}
        title='Detalles por operación'
      />
    </Fetching>
  )
}
