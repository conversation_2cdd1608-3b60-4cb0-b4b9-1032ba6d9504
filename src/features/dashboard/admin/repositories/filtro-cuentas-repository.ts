/* eslint-disable sonarjs/use-type-alias */
import type { FiltroCuentas } from '@d/admin/models/filtro-cuentas'
import { FiltroCuentasBalance } from '@d/admin/models/filtro-cuentas-balance'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class FiltroCuentasRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/cuentasdeorden'

  readonly deleteFiltroCuentas = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getFiltroCuentas = () =>
    this.api.client
      .get<FiltroCuentas[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getFiltroCuentasBalance = (tradeGroup: string) =>
    this.api.client
      .get<FiltroCuentasBalance[]>(
        `${this.route}/${this.attachDate()}/${tradeGroup}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertFiltroCuentas = (filtroCuentas: FiltroCuentas) =>
    this.api.client.post<{ message: string }>(this.route, filtroCuentas)

  readonly updateFiltroCuentas = (id: string, filtroCuentas: FiltroCuentas) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      filtroCuentas
    )

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
