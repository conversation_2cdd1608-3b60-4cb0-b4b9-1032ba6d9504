/* eslint-disable sonarjs/use-type-alias */
import type { Trade } from '@d/admin/models/trade'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class TradeRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/tradegroups'

  readonly deleteTrade = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getTrade = () =>
    this.api.client
      .get<Trade[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertTrade = (portfolTrade: Trade) =>
    this.api.client.post<{ message: string }>(this.route, portfolTrade)

  readonly updateTrade = (id: string, portfolTrade: Trade) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      portfolTrade
    )
}
