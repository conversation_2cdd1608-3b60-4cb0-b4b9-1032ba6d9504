import type { AjusteManual } from '@d/coap/models/ajuste-manual'
import type { <PERSON><PERSON> } from '@d/coap/models/cartera'
import type { Desglose } from '@d/coap/models/desglose'
import type { Equity } from '@d/coap/models/equity'
import type { SwitchTrade } from '@d/coap/models/switch'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class CoapRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/coap'

  readonly getCartera = () =>
    this.api.client
      .get<Cartera[]>(`${this.route}/PLRFAMD/${this.attachDate()}/4`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getDesglose = () =>
    this.api.client
      .get<Desglose[]>(
        `${this.route}/DesgloseNoRealizadoCOAPCRV/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getDividendos = (accountingAreaId: string) =>
    this.api.client
      .get<AjusteManual[]>(`${this.route}/ajusteManual/${accountingAreaId}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getEquity = () =>
    this.api.client
      .get<Equity[]>(`${this.route}/PLEQAMDNEW/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getSwitch = (concepto: string) =>
    this.api.client
      .get<SwitchTrade[]>(`${this.route}/ajusteManual/COAP/${concepto}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
