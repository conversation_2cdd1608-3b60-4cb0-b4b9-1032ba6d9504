import type { Areas } from '@d/admin/models/areas'
import type { FiltroEstado } from '@d/admin/models/filtro-estado'
import type { Productos } from '@d/admin/models/productos'
import { store } from '@davstack/store'

export const configStore = store<{
  activoPasivo: FiltroEstado[]
  areas: Areas[]
  productos: Productos[]
}>({
  activoPasivo: [],
  areas: [],
  productos: []
}).actions(store => ({
  setActivoPasivo: (activoPasivo: FiltroEstado[]) =>
    store.activoPasivo.set(activoPasivo),
  setAreas: (areas: Areas[]) => store.areas.set(areas),
  setProductos: (productos: Productos[]) => store.productos.set(productos)
}))
