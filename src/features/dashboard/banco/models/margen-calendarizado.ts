/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const margenCalendarizado = type({
  abril: 'number?',
  agosto: 'number?',
  balance: 'string',
  diciembre: 'number?',
  enero: 'number?',
  febrero: 'number?',
  julio: 'number?',
  junio: 'number?',
  marzo: 'number?',
  mayo: 'number?',
  noviembre: 'number?',
  octubre: 'number?',
  producto: 'string',
  septiembre: 'number?',
  tradeGroup: 'string'
})

export const margenCalendarizadoMetadata = new Set([
  {
    header: 'Balance',
    key: 'balance'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Enero',
    key: 'enero'
  },
  {
    header: 'Febrero',
    key: 'febrero'
  },
  {
    header: 'Marzo',
    key: 'marzo'
  },
  {
    header: 'Abril',
    key: 'abril'
  },
  {
    header: '<PERSON>',
    key: 'mayo'
  },
  {
    header: 'Jun<PERSON>',
    key: 'junio'
  },
  {
    header: '<PERSON>',
    key: 'julio'
  },
  {
    header: 'Agos<PERSON>',
    key: 'agosto'
  },
  {
    header: 'Septiembre',
    key: 'septiembre'
  },
  {
    header: 'Octubre',
    key: 'octubre'
  },
  {
    header: 'Noviembre',
    key: 'noviembre'
  },
  {
    header: 'Diciembre',
    key: 'diciembre'
  }
])

export type MargenCalendarizado = typeof margenCalendarizado.infer

export const margenCalendarizadoValidate = (attempt?: MargenCalendarizado) =>
  margenCalendarizado(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
