import { Reload } from '@d/common/components/Reload'
import { CarteraRepository } from '@d/common/repository/cartera-repository'
import { CarteraVencimiento } from '@d/common/templates/cartera-vencimiento'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'

export default () => {
  const { getVtoDesglose, getVtoResumen } = container.get(CarteraRepository)

  const day = calendarStore.actualDay.use()

  const {
    data: invVencData,
    error: invVencError,
    isError: isInvVencError,
    isFetching: isInvVencPending,
    refetch: refetchInvVenc
  } = useQuery({
    queryFn: () => getVtoResumen('1'),
    queryKey: ['getRentaFijaResumen']
  })

  const {
    data: plrfinvDesgloseData,
    error: plrfinvDesgloseError,
    isError: isPLRFINVDesgloseError,
    isFetching: isPLRFINVDesglosePending,
    refetch: refetchPLRFINVDesglose
  } = useQuery({
    queryFn: () => getVtoDesglose('1'),
    queryKey: ['getRentaFijaDesglose']
  })

  const refetch = () => {
    void refetchInvVenc()
    void refetchPLRFINVDesglose()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <Fetching
      errorMessage={invVencError?.message ?? plrfinvDesgloseError?.message}
      fullHeight
      isError={isInvVencError || isPLRFINVDesgloseError}
      isFetching={isInvVencPending || isPLRFINVDesglosePending}
    >
      <Reload onClick={refetch} />
      <CarteraVencimiento
        desglose={plrfinvDesgloseData ?? []}
        resumen={invVencData ?? []}
      />
    </Fetching>
  )
}
