/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const calAgr = type({
  clave: 'string',
  dias: 'string',
  fecha: 'string'
})

export const calAgrMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Dias',
    key: 'dias'
  }
])

export type CalAgr = typeof calAgr.infer

export const calAgrDefault: CalAgr = {
  clave: '',
  dias: '',
  fecha: ''
}

export const calAgrValidate = (attempt?: CalAgr) =>
  calAgr(attempt) instanceof type.errors ? resetObject(attempt) : attempt
