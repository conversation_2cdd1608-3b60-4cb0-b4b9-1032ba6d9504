/* eslint-disable unicorn/no-keyword-prefix */
import { TitleSubtitle } from '@c/components/TitleSubtitle'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import {
  plFechas,
  plFechasHistoricoMetadata,
  plFechasMetadata
} from '@d/mercado-divisas/models/pl-fechas'
import { MercadoDivisasRepository } from '@d/mercado-divisas/repository/mercado-divisas-repository'
import { Container, LinearProgress, Paper, Typography } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { getPlFechas, getPlFechasHistorico } = container.get(
    MercadoDivisasRepository
  )

  const [startDate, setStartDate] = useState(dayjs())
  const [endDate, setEndDate] = useState(dayjs())

  const day = calendarStore.actualDay.use()

  const {
    data: getPlFechasData,
    error: getPlFechasError,
    isError: isGetPlFechasError,
    isFetching: isGetPlFechasPending,
    refetch: refetchGetPlFechas
  } = useQuery({
    queryFn: () =>
      getPlFechas(startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')),
    queryKey: ['getPlFechas']
  })

  const {
    data: getPlFechasHistoricoData,
    error: getPlFechasHistoricoError,
    isError: isGetPlFechasHistoricoError,
    isFetching: isGetPlFechasHistoricoPending,
    refetch: refetchGetPlFechasHistorico
  } = useQuery({
    queryFn: getPlFechasHistorico,
    queryKey: ['getPlFechasHistorico']
  })

  const renew = () => {
    void refetchGetPlFechasHistorico()
    setStartDate(dayjs())
    setEndDate(dayjs())
    void refetchGetPlFechas()
  }

  useEffect(() => {
    renew()
  }, [day])

  return (
    <Fetching
      errorMessage={
        getPlFechasError?.message ?? getPlFechasHistoricoError?.message
      }
      isError={isGetPlFechasError || isGetPlFechasHistoricoError}
      isFetching={isGetPlFechasPending || isGetPlFechasHistoricoPending}
    >
      <Reload onClick={renew} />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Container sx={{ alignItems: 'baseline', display: 'flex' }}>
          <DatePicker
            defaultValue={startDate}
            label='Inicio'
            onChange={newValue => {
              setStartDate(dayjs(newValue))
              setTimeout(() => refetchGetPlFechas(), 100)
            }}
            slotProps={{ textField: { size: 'small' } }}
            value={startDate}
          />
          <Typography component='h5' sx={{ marginX: '20px' }} variant='h5'>
            -
          </Typography>
          <DatePicker
            defaultValue={endDate}
            label='Fin'
            onChange={newValue => {
              setEndDate(dayjs(newValue))
              setTimeout(() => refetchGetPlFechas(), 100)
            }}
            slotProps={{ textField: { size: 'small' } }}
            value={endDate}
          />
        </Container>

        <TitleSubtitle title='PL entre ambas fechas' />

        {isGetPlFechasPending && <LinearProgress sx={{ mb: 3 }} />}

        <Paper elevation={10} sx={paperStyle}>
          <GenericTable
            data={getPlFechasData ?? []}
            entries={plFechas}
            metadata={plFechasMetadata}
          />
        </Paper>

        <TitleSubtitle title='PL Histórico' />

        <Paper elevation={10} sx={paperStyle}>
          <GenericTable
            data={getPlFechasHistoricoData ?? []}
            entries={plFechas}
            metadata={plFechasHistoricoMetadata}
          />
        </Paper>
      </LocalizationProvider>
    </Fetching>
  )
}
