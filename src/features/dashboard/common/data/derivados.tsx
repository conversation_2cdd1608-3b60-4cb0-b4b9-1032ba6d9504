import { Bar<PERSON>hartOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const derivados: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'derivados-trading',
          title: <FormattedMessage id='trading' />,
          type: 'item',
          url: '/derivados/trading'
        },
        {
          id: 'derivados-riesgoMercado',
          title: <FormattedMessage id='riesgoMercado' />,
          type: 'item',
          url: '/derivados/riesgo-mercado'
        }
      ],
      icon: BarChartOutlined,
      id: 'derivados',
      title: <FormattedMessage id='derivados' />,
      type: 'collapse'
    }
  ],
  icon: BarChartOutlined,
  id: 'group-derivados',
  title: <FormattedMessage id='derivados' />,
  type: 'group'
}
