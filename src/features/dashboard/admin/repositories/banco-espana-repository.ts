/* eslint-disable sonarjs/use-type-alias */
import type { BancoEspana } from '@d/admin/models/banco-espana'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class BancoEspanaRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly excesoRoute = '/bancoesexceso'
  private readonly route = '/bancoes'

  readonly deleteBancoEspana = (id: string, isExceso = false) =>
    this.api.client.delete<{ message: string }>(
      `${isExceso ? this.excesoRoute : this.route}/${id}`
    )

  readonly getBancoEspana = (isExceso = false) =>
    this.api.client
      .get<BancoEspana[]>(isExceso ? this.excesoRoute : this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertBancoEspana = (bancoEspana: BancoEspana, isExceso = false) =>
    this.api.client.post<{ message: string }>(
      isExceso ? this.excesoRoute : this.route,
      bancoEspana
    )

  readonly updateBancoEspana = (
    id: string,
    bancoEspana: BancoEspana,
    isExceso = false
  ) =>
    this.api.client.put<{ message: string }>(
      `${isExceso ? this.excesoRoute : this.route}/${id}`,
      bancoEspana
    )
}
