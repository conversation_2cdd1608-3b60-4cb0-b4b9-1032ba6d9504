/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { DashboardOutlined, GoldOutlined } from '@ant-design/icons'
import type { AntdIconProps } from '@ant-design/icons/lib/components/AntdIcon'
import type { ForwardRefExoticComponent, RefAttributes } from 'react'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'
import type { GenericCardProps } from '~/types/root'

type Icon =
  | ForwardRefExoticComponent<
      Omit<AntdIconProps, 'ref'> & RefAttributes<HTMLSpanElement>
    >
  | GenericCardProps['iconPrimary']
  | string

const icons = {
  components: GoldOutlined,
  dashboard: DashboardOutlined
}

export const Menu = () => {
  const { menu } = { menu: { children: [{}], title: 'hi' } }

  const SubChildrenLis = (SubChildrenLis: NavItemType[]) =>
    SubChildrenLis.map(subList => ({
      ...subList,
      // @ts-ignore
      icon: icons[subList.icon] as Icon,
      // eslint-disable-next-line @typescript-eslint/no-base-to-string
      title: <FormattedMessage id={subList.title?.toString()} />
    }))

  const itemList = (subList: NavItemType) => {
    const list: NavItemType = {
      ...subList,
      // @ts-ignore
      icon: icons[subList.icon] as Icon,
      // eslint-disable-next-line @typescript-eslint/no-base-to-string
      title: <FormattedMessage id={subList.title?.toString()} />
    }

    if (subList.type === 'collapse') {
      list.children = SubChildrenLis(subList.children ?? [])
    }
    return list
  }

  const withoutMenu = menu.children.filter(
    (item: NavItemType) => item.id !== 'no-menu'
  )
  const ChildrenList: NavItemType[] | undefined = withoutMenu.map(
    // eslint-disable-next-line functional/prefer-tacit
    (subList: NavItemType) => itemList(subList)
  )

  const menuList: NavItemType = {
    ...menu,
    children: ChildrenList,
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    icon: icons[menu.icon] as Icon,
    title: <FormattedMessage id={menu.title} />
  }

  return menuList
}
