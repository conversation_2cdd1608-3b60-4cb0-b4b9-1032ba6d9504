/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const detallesDerivadosCobertura = type({
  archiveDate: 'string',
  comPeridPagPerid: 'number',
  comPtePerCobrada: 'number',
  comPtePerPagada: 'number',
  counterParty: 'string',
  importePeriodPago: 'number',
  importePeriodRecib: 'number',
  instrumentID: 'string',
  iSINCobertura: 'string',
  marketID: 'string',
  netPVAdaptiv: 'number',
  plusvaliaTotal: 'number',
  tradeGroup: 'string'
})

export const detallesDerivadosCoberturaMetadata = new Set([
  {
    header: 'Fecha',
    key: 'archiveDate'
  },
  {
    header: 'Instrument ID',
    key: 'instrumentID'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Referencia',
    key: 'iSINCobertura'
  },
  {
    header: 'Counterparty ID',
    key: 'counterParty'
  },
  {
    header: 'Plusvalía Total',
    key: 'plusvaliaTotal'
  }
])

export type DetallesDerivadosCobertura = typeof detallesDerivadosCobertura.infer

export const detallesDerivadosCoberturaValidate = (
  attempt: DetallesDerivadosCobertura
) =>
  detallesDerivadosCobertura(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
