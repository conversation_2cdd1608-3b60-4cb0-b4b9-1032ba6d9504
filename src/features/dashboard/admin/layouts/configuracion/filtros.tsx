/* eslint-disable sonarjs/void-use */
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

const route = '/admin/configuracion/filtros'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/trade-group`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/contrapartida`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/renta-fija`)
        setMenuId(2)
        break
      }
      case 3: {
        if (val !== menuId) void navigate(`${route}/operaciones`)
        setMenuId(3)
        break
      }
      case 4: {
        if (val !== menuId) void navigate(`${route}/cuentas-orden`)
        setMenuId(4)
        break
      }
      case 5: {
        if (val !== menuId) void navigate(`${route}/estados`)
        setMenuId(5)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('trade-group')) setMenuId(0)
    if (path.includes('contrapartida')) setMenuId(1)
    if (path.includes('renta-fija')) setMenuId(2)
    if (path.includes('operaciones')) setMenuId(3)
    if (path.includes('cuentas-orden')) setMenuId(4)
    if (path.includes('estados')) setMenuId(5)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Filtros Trade Group</MenuItem>
                  <MenuItem value={1}>Filtros Contrapartida</MenuItem>
                  <MenuItem value={2}>Filtros Renta Fija</MenuItem>
                  <MenuItem value={3}>Filtros Operaciones</MenuItem>
                  <MenuItem value={4}>Cuentas de Orden</MenuItem>
                  <MenuItem value={5}>Estados</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Outlet />
        </Grid>
      </Grid>
    </Container>
  )
}
