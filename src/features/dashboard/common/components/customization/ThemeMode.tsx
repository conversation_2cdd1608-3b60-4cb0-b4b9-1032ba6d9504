import { MainCard } from '@c/components/MainCard'
import {
  CardMedia,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Stack,
  Typography
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import type { ChangeEvent } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import darkLayout from '~/assets/dark.svg'
import defaultLayout from '~/assets/default.svg'
import { ThemeMode } from '~/types/resources'

export const ThemeModeLayout = () => {
  const theme = useTheme()

  const { mode, onChangeMode } = useContext(ResourceContext)

  const handleModeChange = (event: ChangeEvent<HTMLInputElement>) =>
    onChangeMode(event.target.value as ThemeMode)

  return (
    <RadioGroup
      aria-label='payment-card'
      name='payment-card'
      onChange={handleModeChange}
      row
      value={mode}
    >
      <Grid container spacing={1.75} sx={{ ml: 0 }}>
        <Grid item>
          <FormControlLabel
            control={<Radio sx={{ display: 'none' }} value='light' />}
            label={
              <MainCard
                border={false}
                content={false}
                sx={{
                  bgcolor:
                    mode === ThemeMode.DARK
                      ? 'secondary.lighter'
                      : 'primary.lighter',
                  p: 1
                }}
                {...(mode === ThemeMode.LIGHT && {
                  boxShadow: true,
                  shadow: theme.customShadows.primary
                })}
              >
                <Stack alignItems='center' spacing={1.25}>
                  <CardMedia
                    alt='Vertical'
                    component='img'
                    src={defaultLayout}
                    sx={{ borderRadius: 1, height: 64, width: 64 }}
                  />
                  <Typography variant='caption'>Claro</Typography>
                </Stack>
              </MainCard>
            }
            sx={{
              '& .MuiFormControlLabel-label': { flex: 1 },
              display: 'flex'
            }}
          />
        </Grid>
        <Grid item>
          <FormControlLabel
            control={<Radio sx={{ display: 'none' }} value='dark' />}
            label={
              <MainCard
                border={false}
                content={false}
                sx={{
                  bgcolor:
                    mode === ThemeMode.DARK
                      ? 'primary.lighter'
                      : 'secondary.lighter',
                  p: 1
                }}
                {...(mode === ThemeMode.DARK && {
                  boxShadow: true,
                  shadow: theme.customShadows.primary
                })}
              >
                <Stack alignItems='center' spacing={1.25}>
                  <CardMedia
                    alt='Vertical'
                    component='img'
                    src={darkLayout}
                    sx={{ borderRadius: 1, height: 64, width: 64 }}
                  />
                  <Typography variant='caption'>Oscuro</Typography>
                </Stack>
              </MainCard>
            }
            sx={{
              '& .MuiFormControlLabel-label': { flex: 1 },
              display: 'flex'
            }}
          />
        </Grid>
      </Grid>
    </RadioGroup>
  )
}
