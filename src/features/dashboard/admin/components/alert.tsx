import { Alert, Fade, Snackbar } from '@mui/material'
import type { FC } from 'react'

const alertCalc = (
  severity: 'error' | 'info' | 'success' | 'warning',
  message?: string
) => {
  if (message === undefined && severity === 'success') {
    return 'Operación realizada exitosmente'
  } else if (message === undefined && severity === 'error') {
    return ' ERROR: inténtelo más tarde o consulte con el técnico responsable...'
  } else {
    return message
  }
}

export const OverlayAlert: FC<{
  message?: string
  open: boolean
  severity?: 'error' | 'info' | 'success' | 'warning'
}> = ({ message, open, severity = 'success' }) => (
  <Snackbar
    anchorOrigin={{ horizontal: 'right', vertical: 'top' }}
    autoHideDuration={3000}
    open={open}
    TransitionComponent={Fade}
  >
    <Alert severity={severity} sx={{ width: '100%' }}>
      {alertCalc(severity, message)}
    </Alert>
  </Snackbar>
)
