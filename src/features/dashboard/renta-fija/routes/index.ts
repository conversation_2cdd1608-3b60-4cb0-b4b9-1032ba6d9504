import { route } from '@react-router/dev/routes'

export const rentaFijaRouter = [
  route(
    'renta-fija/coberturas',
    'features/dashboard/renta-fija/pages/coberturas.tsx'
  ),
  route(
    'renta-fija/riesgo-mercado',
    'features/dashboard/renta-fija/pages/riesgo-mercado.tsx'
  ),
  route(
    'renta-fija/cartera-inversion',
    'features/dashboard/renta-fija/pages/cartera-inversion.tsx'
  ),
  route(
    'renta-fija/margen-financiero',
    'features/dashboard/renta-fija/pages/margen/margen.tsx'
  ),
  route(
    'renta-fija/cartera-vencimiento',
    'features/dashboard/renta-fija/pages/cartera-vencimiento.tsx'
  ),
  route('renta-fija/trading', 'features/dashboard/renta-fija/pages/trading.tsx')
]
