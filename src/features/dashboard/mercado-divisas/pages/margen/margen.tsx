import { DialogTable } from '@d/common/components/DialogTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  activoPasivo,
  activoPasivoDetailMetadata
} from '@d/common/models/activo-pasivo'
import { MargenFinanciero } from '@d/common/templates/margen-financiero/margen-financiero'
import { DialogDolEur } from '@d/mercado-divisas/dialogs/dialog-doleur'

import { Fetching } from '@/dashboard/common/components/Fetching'

import { margenController } from './margen-controller'

export default () => {
  const {
    activoData,
    activoError,
    dialog1Data,
    dialog1Error,
    isActivoError,
    isActivoPending,
    isDialog1Error,
    isDialog1Pending,
    isPasivoError,
    isPasivoPending,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    pasivoError,
    refetch,
    selectedActivoPasivo2,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  } = margenController()

  return (
    <Fetching
      errorMessage={activoError?.message ?? pasivoError?.message}
      fullHeight
      isError={isActivoError || isPasivoError}
      isFetching={isActivoPending || isPasivoPending}
    >
      <Reload onClick={refetch} />
      <MargenFinanciero
        activo={activoData ?? []}
        onActivoClick={index =>
          onRowActivoPasivo1(activoData ?? [], index, true)
        }
        onPasivoClick={index =>
          onRowActivoPasivo1(pasivoData ?? [], index, false)
        }
        pasivo={pasivoData ?? []}
      />
      <DialogTable
        data={dialog1Data ?? []}
        entries={activoPasivo}
        error={dialog1Error?.message}
        isError={isDialog1Error}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, true)}
        open={openDialogActivo1}
        setOpen={setOpenDialogActivo1}
        title='Activo'
      />
      <DialogTable
        data={dialog1Data ?? []}
        entries={activoPasivo}
        error={dialog1Error?.message}
        isError={isDialog1Error}
        metadata={activoPasivoDetailMetadata}
        onRowClick={index => onRowActivoPasivo2(index, false)}
        open={openDialogPasivo1}
        setOpen={setOpenDialogPasivo1}
        title='Pasivo'
      />
      <DialogDolEur
        dataDialog={selectedActivoPasivo2.current}
        open={openDialogActivo2}
        setOpen={setOpenDialogActivo2}
        title='Operaciones Depósitos en Divisa Activo'
      />
      <DialogDolEur
        dataDialog={selectedActivoPasivo2.current}
        open={openDialogPasivo2}
        setOpen={setOpenDialogPasivo2}
        title='Operaciones Depósitos en Divisa Pasivo'
      />
      <LoadingDialog open={isDialog1Pending} />
    </Fetching>
  )
}
