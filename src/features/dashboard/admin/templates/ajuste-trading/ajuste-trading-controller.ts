import {
  type AjusteTrading,
  ajusteTradingDefault
} from '@d/admin/models/ajuste-trading'
import { AjusteTradingRepository } from '@d/admin/repositories/ajuste-trading-repository'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import { useCallback, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

const conceptosTrading: ConceptosTrading[] = [
  'A_DERIV',
  'FUTUROS',
  'RF_NEGO',
  'VEN_PLA',
  'CUPON_RF'
]

type ConceptosTrading =
  | 'A_DERIV'
  | 'CUPON_RF'
  | 'FUTUROS'
  | 'RF_NEGO'
  | 'VEN_PLA'

type ConceptosTradingDerivados =
  | 'A_DERIV_CAMP'
  | 'A_DERIV'
  | 'CUPON_RF'
  | 'FMVLRZPG'
  | 'FUTUROS'
  | 'FUTUROSRF'
  | 'RF_NEGO'
  | 'VEN_PLA'

const conceptosTradingDerivados: ConceptosTradingDerivados[] = [
  'A_DERIV',
  'A_DERIV_CAMP',
  'FUTUROS',
  'RF_NEGO',
  'VEN_PLA',
  'FUTUROSRF',
  'FMVLRZPG',
  'CUPON_RF'
]

export const validate = (pool: AjusteTrading) => {
  const errors: Record<string, string> = {}
  if (!pool.fecha) errors['fecha'] = 'Required'
  return errors
}

export const ajusteTradingController = (unicId: 'MCAP' | 'TESO') => {
  const day = calendarStore.actualDay.use()

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)

  const {
    createAjusteManual,
    deleteAjusteManual,
    getAjusteManual,
    updateAjusteManual
  } = container.get(AjusteTradingRepository)

  const condConcept = useRef(
    unicId === 'TESO' ? conceptosTrading : conceptosTradingDerivados
  )

  const [activeConcepto, setActiveConcepto] = useState(
    nth(0, condConcept.current) ?? 'A_DERIV'
  )

  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(ajusteTradingDefault)

  const {
    data: dataAjuste,
    isFetching: isDataAjustePending,
    refetch: dataAjusteRefetch
  } = useQuery({
    queryFn: () => getAjusteManual(unicId, activeConcepto),
    queryKey: ['getAjusteTrading']
  })

  const submitAction = useCallback(
    async (aj: AjusteTrading, _: FormikHelpers<AjusteTrading>) => {
      setOpenCreateEditDialog(false)
      await delay(150)
      setLoadingDialog(true)

      await (selectedIndex.current === null
        ? createAjusteManual(
            unicId,
            aj.fecha,
            aj.importe ?? 0,
            activeConcepto,
            aj.comentarios
          )
        : updateAjusteManual(
            selectedData.current.accountingAreaID,
            aj.fecha,
            aj.importe ?? 0,
            aj.concepto,
            aj.comentarios
          ))

      setLoadingDialog(false)
      void dataAjusteRefetch()
    },
    []
  )

  const deleteAction = useCallback(async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteAjusteManual(
        selectedData.current.accountingAreaID,
        selectedData.current.fecha,
        selectedData.current.concepto
      )
      setLoadingDialog(false)
      void dataAjusteRefetch()
    }
  }, [])

  useEffect(() => {
    void dataAjusteRefetch()
  }, [activeConcepto, day])

  return {
    activeConcepto,
    condConcept,
    dataAjuste,
    dataAjusteRefetch,
    deleteAction,
    isDataAjustePending,
    loadingDialog,
    openCreateEditDialog,
    openDeleteDialog,
    selectedData,
    selectedIndex,
    setActiveConcepto,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  }
}
