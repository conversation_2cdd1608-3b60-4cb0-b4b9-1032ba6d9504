/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const mesaContado = type({
  divisa_Nombre: 'string',
  ordenCont: 'number',
  pnLCont: 'number',
  posEurContado: 'number',
  posLocalContado: 'number'
})

export const mesaContadoMetadata = new Set([
  {
    header: 'Divisa',
    key: 'divisa_Nombre'
  },
  {
    header: 'Posición Contado',
    key: 'posLocalContado'
  },
  {
    header: 'Posición Contado €',
    key: 'posEurContado'
  },
  {
    header: 'P&L Contado €',
    key: 'pnLCont'
  }
])

export type MesaContado = typeof mesaContado.infer

export const mesaContadoValidate = (attempt?: MesaContado) =>
  mesaContado(attempt) instanceof type.errors ? resetObject(attempt) : attempt
