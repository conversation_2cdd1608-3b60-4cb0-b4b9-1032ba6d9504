/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const calcMargen = type({
  importBnd: 'string',
  importCsh: 'string',
  importID: 'number',
  importLnd: 'string',
  importPLEQ: 'string',
  importPLRF: 'string',
  importRep: 'string',
  importSwp: 'string',
  importVAR: 'string'
})

export type CalcMargen = typeof calcMargen.infer

export const calcMargenMetadata = new Set([
  {
    header: 'Depósitos',
    key: 'importCsh'
  },
  {
    header: 'Bonos',
    key: 'importBnd'
  },
  {
    header: 'Repos',
    key: 'importRep'
  },
  {
    header: 'Derivadas',
    key: 'importSwp'
  },
  {
    header: 'Bond Lending',
    key: 'importLnd'
  }
])
