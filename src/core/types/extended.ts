import type { ButtonProps, ChipProps, IconButtonProps } from '@mui/material'
import type { Theme } from '@mui/material/styles'

export type AvatarTypeProps = 'combined' | 'filled' | 'outlined'

export type ButtonVariantProps =
  | 'contained'
  | 'dashed'
  | 'light'
  | 'outlined'
  | 'shadow'
  | 'text'

export type ColorProps =
  | ButtonProps['color']
  | ChipProps['color']
  | IconButtonProps['color']
  | 'default'
  | 'error'
  | 'info'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'

export type ExtendedStyleProps = {
  color: ColorProps
  theme: Theme
}

export type IconButtonShapeProps = 'rounded' | 'square'
export type SizeProps = 'badge' | 'lg' | 'md' | 'sm' | 'xl' | 'xs'
