/* eslint-disable functional/no-try-statements */
import { Button, CircularProgress, FormHelperText, Grid } from '@mui/material'
import { Formik } from 'formik'
import { object as yupObject, string as yupString } from 'yup'

import { AnimateButton } from '@/common/components/AnimateButton'

import { LoginTitle, LoginWrapper, TextBox } from './login-components'
import { useLoginController } from './login-controller'

export default () => {
  const {
    color,
    handleClickShowPassword,
    mode,
    onSubmit,
    showPassword,
    themeDirection
  } = useLoginController()

  return (
    <LoginWrapper direction={themeDirection} mode={mode}>
      <Grid item xs={12}>
        <LoginTitle color={color} title='¡Que gusto verte de nuevo!' />
        <LoginTitle
          color={color}
          title='Ingresa tus datos para ingresar al sistema'
          variant='h5'
        />
      </Grid>
      <Grid item xs={12}>
        <Formik
          initialValues={{
            email: '',
            password: '',
            submit: null
          }}
          onSubmit={onSubmit}
          validationSchema={yupObject().shape({
            email: yupString().max(25).required('Por favor, ponga un usuario'),
            password: yupString()
              .max(40)
              .required('Por favor, ponga una contraseña')
          })}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values
          }) => (
            <form noValidate onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <TextBox
                  error={Boolean((touched.email ?? false) && errors.email)}
                  errorPlaceholder={errors.email}
                  id='email-login'
                  label='Usuario'
                  name='email'
                  onBlur={handleBlur}
                  onChange={handleChange}
                  onError={
                    (touched.password ?? false) && errors.password != null
                  }
                  placeholder='Introduce tu usuario'
                  value={values.email}
                />
                <TextBox
                  error={Boolean(
                    (touched.password ?? false) && errors.password
                  )}
                  errorPlaceholder={errors.password}
                  id='password-login'
                  label='Contraseña'
                  name='password'
                  onBlur={handleBlur}
                  onChange={handleChange}
                  onError={
                    (touched.password ?? false) && errors.password != null
                  }
                  onMouseDown={event => event.preventDefault()}
                  onShowClick={handleClickShowPassword}
                  placeholder='Introduce tu contraseña'
                  showPassword={showPassword}
                  type={showPassword ? 'text' : 'password'}
                  value={values.password}
                />
                {errors.submit != null && (
                  <Grid item xs={12}>
                    <FormHelperText error>{errors.submit}</FormHelperText>
                  </Grid>
                )}
                <Grid
                  item
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center'
                  }}
                  xs={12}
                >
                  {isSubmitting && <CircularProgress sx={{ mr: 3 }} />}
                  <AnimateButton>
                    <Button
                      disabled={isSubmitting}
                      disableElevation
                      fullWidth
                      size='large'
                      sx={{ background: '#007A5E' }}
                      type='submit'
                      variant='contained'
                    >
                      Iniciar sesión
                    </Button>
                  </AnimateButton>
                </Grid>
              </Grid>
            </form>
          )}
        </Formik>
      </Grid>
    </LoginWrapper>
  )
}
