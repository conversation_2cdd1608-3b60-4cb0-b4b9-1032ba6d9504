import type { Theme } from '@mui/material/styles'

export const Accordion = (theme: Theme) => ({
  MuiAccordion: {
    defaultProps: {
      disableGutters: true,
      elevation: 0,
      square: true
    },
    styleOverrides: {
      root: {
        '&.Mui-disabled': {
          backgroundColor: theme.palette.secondary.lighter
        },
        '&:before': { display: 'none' },
        '&:not(:last-child)': { borderBottom: 0 },
        border: `1px solid ${theme.palette.secondary.light}`
      }
    }
  }
})
