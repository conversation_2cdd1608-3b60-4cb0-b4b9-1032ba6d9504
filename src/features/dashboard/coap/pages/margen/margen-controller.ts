import type { ActivoPasivo } from '@d/common/models/activo-pasivo'
import type { DesgloseDerivadoCobertura } from '@d/common/models/desglose-derivado-cobertura'
import type { DesgloseProductoActivoPasivo } from '@d/common/models/desglose-producto-activo-pasivo'
import { ActivoPasivoRepository } from '@d/common/repository/activo-pasivo-repository'
import { useQuery } from '@tanstack/react-query'
import { delay, nth } from 'rambdax'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const margenController = () => {
  const {
    desgloseMFDerivadoCobertura,
    desgloseProductoActivo,
    desgloseProductoPasivo,
    getActivo,
    getPasivo,
    showTradeGroup
  } = container.get(ActivoPasivoRepository)

  const day = calendarStore.actualDay.use()

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDialogActivo1, setOpenDialogActivo1] = useState(false)
  const [openDialogActivo2, setOpenDialogActivo2] = useState(false)
  const [openDialogPasivo1, setOpenDialogPasivo1] = useState(false)
  const [openDialogPasivo2, setOpenDialogPasivo2] = useState(false)
  const [openDialogActivo3, setOpenDialogActivo3] = useState(false)

  const [dialog1Data, setDialog1Data] = useState<ActivoPasivo[]>([])
  const [dialog2Data, setDialog2Data] = useState<
    DesgloseProductoActivoPasivo[]
  >([])
  const [dialog3Data, setDialog3Data] = useState<DesgloseDerivadoCobertura[]>(
    []
  )

  const [balance, setBalance] = useState('Activo')

  const {
    data: activoData,
    error: activoError,
    isError: isActivoError,
    isFetching: isActivoPending,
    refetch: refetchActivo
  } = useQuery({
    queryFn: () => getActivo('4'),
    queryKey: ['getActivoCOAP']
  })

  const {
    data: pasivoData,
    error: pasivoError,
    isError: isPasivoError,
    isFetching: isPasivoPending,
    refetch: refetchPasivo
  } = useQuery({
    queryFn: () => getPasivo('4'),
    queryKey: ['getPasivoCOAP']
  })

  const onRowActivoPasivo1 = useCallback(
    async (
      activoPasivoData: ActivoPasivo[],
      rowIndex: number,
      activoPasivo: boolean
    ) => {
      const activoPasivoRow = nth(rowIndex, activoPasivoData)

      const balance = activoPasivo
        ? 'Activo'
        : (activoPasivoRow?.balance ?? 'Pasivo')
      setBalance(balance)
      setLoadingDialog(true)
      setDialog1Data(
        await showTradeGroup(
          balance,
          '4',
          activoPasivoRow?.epigrafeID?.toString() ?? ''
        )
      )

      setLoadingDialog(false)
      activoPasivo ? setOpenDialogActivo1(true) : setOpenDialogPasivo1(true)
    },
    []
  )

  const onRowActivoPasivo2 = async (
    rowIndex: number,
    activoPasivo: boolean
  ) => {
    const activoPasivoRow = nth(rowIndex, dialog1Data)
    const producto = activoPasivoRow?.producto ?? ''
    const tradeGroup = activoPasivoRow?.tradeGroup ?? ''

    setLoadingDialog(true)

    await delay(100)

    setDialog2Data(
      activoPasivo
        ? (await desgloseProductoActivo(producto, balance, tradeGroup)).map(
            el => ({
              ...el,
              expiryDate: el.expiryDate.split('T')[0] ?? '',
              startDateAdj: el.startDateAdj.split('T')[0] ?? ''
            })
          )
        : (await desgloseProductoPasivo(producto, balance, tradeGroup)).map(
            el => ({
              ...el,
              expiryDate: el.expiryDate.split('T')[0] ?? '',
              startDateAdj: el.startDateAdj.split('T')[0] ?? ''
            })
          )
    )

    setLoadingDialog(false)
    activoPasivo ? setOpenDialogActivo2(true) : setOpenDialogPasivo2(true)
  }

  const onRowActivo3 = async (rowIndex: number) => {
    const desgloseRow = nth(rowIndex, dialog2Data)
    setLoadingDialog(true)
    setDialog3Data(
      await desgloseMFDerivadoCobertura(desgloseRow?.instrumentID ?? '', '4')
    )
    setLoadingDialog(false)
    setOpenDialogActivo3(true)
  }

  const refetch = useCallback(() => {
    void refetchPasivo()
    void refetchActivo()
  }, [])

  useEffect(() => {
    refetch()
  }, [day])

  return {
    activoData,
    activoError,
    dialog1Data,
    dialog2Data,
    dialog3Data,
    isActivoError,
    isActivoPending,
    isPasivoError,
    isPasivoPending,
    loadingDialog,
    onRowActivo3,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogActivo3,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    pasivoError,
    refetch,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogActivo3,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  }
}
