import { cartera, carteraMetadata } from '@d/coap/models/cartera'
import { CoapRepository } from '@d/coap/repository/coap-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Container,
  Grid,
  MenuItem,
  Paper,
  Select,
  type SelectChangeEvent
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { authStore } from '@/common/store/auth-store'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { Role } from '~/types/auth'

import { switchTrade, switchTradeMetadata } from '../models/switch'

export default () => {
  const day = calendarStore.actualDay.use()
  const user = authStore.user.use()

  const { getCartera, getSwitch } = container.get(CoapRepository)

  const [menuId, setMenuId] = useState(0)

  const selectConcepto = useRef<'A_DERIV' | 'CUPON_RF' | 'FUTUROS' | 'VEN_PLA'>(
    'A_DERIV'
  )

  const {
    data: getCarteraData,
    isFetching: isGetCarteraPending,
    refetch: refetchGetCartera
  } = useQuery({
    queryFn: getCartera,
    queryKey: ['getCartera']
  })

  const {
    data: getSwitchData,
    isFetching: isGetSwitchPending,
    refetch: refetchGetSwitch
  } = useQuery({
    queryFn: () => getSwitch(selectConcepto.current),
    queryKey: ['getSwitch']
  })

  const refetch = () => {
    void refetchGetSwitch()
    void refetchGetCartera()
  }

  const handleChange = (event: SelectChangeEvent) => {
    setMenuId(Number(event.target.value))
    switch (Number(event.target.value)) {
      case 0: {
        selectConcepto.current = 'A_DERIV'
        break
      }
      case 1: {
        selectConcepto.current = 'FUTUROS'
        break
      }
      case 2: {
        selectConcepto.current = 'VEN_PLA'
        break
      }
      case 3: {
        selectConcepto.current = 'CUPON_RF'
        break
      }
    }
    void refetchGetSwitch()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera de Negociación</h3>
            </div>
            {isGetCarteraPending ? (
              <Loading />
            ) : (
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getCarteraData ?? []}
                  entries={cartera}
                  metadata={carteraMetadata}
                  noTotal={false}
                />
              </Paper>
            )}
          </Grid>
          {user?.role === Role.ADMIN && (
            <Grid item xs={12}>
              <div
                style={{ alignItems: 'start', display: 'flex', width: '100%' }}
              >
                <Box sx={{ minWidth: 150 }}>
                  <Select
                    id='simple-select'
                    onChange={handleChange}
                    value={menuId.toString()}
                  >
                    <MenuItem value={0}>Ajustes derivados</MenuItem>
                    <MenuItem value={1}>Futuros</MenuItem>
                    <MenuItem value={2}>Ventas a Plazo</MenuItem>
                    <MenuItem value={3}>Cupon RF</MenuItem>
                  </Select>
                </Box>
              </div>
              <br></br>
              {isGetSwitchPending ? (
                <Loading />
              ) : (
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getSwitchData ?? []}
                    entries={switchTrade}
                    metadata={switchTradeMetadata}
                  />
                </Paper>
              )}
            </Grid>
          )}
          <Grid item sx={{ display: 'flex', justifyContent: 'end' }} xs={12}>
            {/*<Button
              onClick={() => {
                //setCreateDialogOpen(true)
              }}
              startIcon={<PlusCircleOutlined />}
              variant='contained'
            >
              Nuevo
            </Button> */}
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
