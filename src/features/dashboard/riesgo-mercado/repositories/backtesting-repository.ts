import { Backtesting } from '@d/riesgo-mercado/models/backtesting'
import { Exced } from '@d/riesgo-mercado/models/exced'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class BacktestingRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/backtesting'

  readonly getBacktesting = (
    area: string,
    producto: string,
    portfolio: 'Inversion' | 'Negociacion' = 'Inversion'
  ) =>
    this.api.client
      .get<Backtesting[]>(
        `${this.route}/${this.attachDate()}/${portfolio}/${area}/${producto}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getExcedInv = () =>
    this.api.client
      .get<Exced[]>(`${this.route}/excedInv/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getExcedNeg = () =>
    this.api.client
      .get<Exced[]>(`${this.route}/excedNeg/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
