/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const subinformes = type({
  areaID: 'number',
  breakDownId: 'number?',
  tradeGroupID: 'number'
})
/*
const headersSubInforme = [
  'Trade Groups Trade',
  'Trade Groups',
  'Trade Groups Excluded',
  'Investment',
  'Excluded from Plrf',
  'Á<PERSON>s título',
  'Acciones'
]*/

export const subinformesMetadata = new Set([
  {
    header: 'Trade Groups',
    key: 'tradeGroupID'
  },
  {
    header: 'Areas',
    key: 'areaID'
  }
])

export type Subinformes = typeof subinformes.infer

export const subinformesDefault: Subinformes = {
  areaID: 0,
  breakDownId: 0,
  tradeGroupID: 0
}
