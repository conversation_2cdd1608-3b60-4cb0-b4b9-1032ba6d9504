import { FileExcelOutlined } from '@ant-design/icons'
import {
  calendarioInteresesCuadreMetadata,
  calendarioInteresesMetadata
} from '@d/banco/models/calendario-intereses'
import {
  margenCalendarizado,
  margenCalendarizadoMetadata
} from '@d/banco/models/margen-calendarizado'
import { BancoRepository } from '@d/banco/repository/banco-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Box, Button, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { customExportToCSV } from '~/utils/csv'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getAnual, getCuadreContable, getMargenCalendarizado, getMes } =
    container.get(BancoRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)

  const {
    data: getMargenCalendarizadoData,
    isFetching: isGetMargenCalendarizadoPending,
    refetch: refetchGetMargenCalendarizado
  } = useQuery({
    queryFn: getMargenCalendarizado,
    queryKey: ['getMargenCalendarizado']
  })

  const downloadRequest = async (temp: 'anual' | 'cuadre' | 'mes') => {
    setLoadingDialog(true)

    switch (temp) {
      case 'anual': {
        customExportToCSV(
          [...calendarioInteresesMetadata].map(el => el.header),
          await getAnual(),
          [...calendarioInteresesMetadata].map(el => el.key),
          'CalendarioInteresesAnual.csv'
        )
        break
      }
      case 'cuadre': {
        customExportToCSV(
          [...calendarioInteresesCuadreMetadata].map(el => el.header),
          await getCuadreContable(),
          [...calendarioInteresesCuadreMetadata].map(el => el.key),
          'CalendarioInteresesMes_CuadreContable.csv'
        )
        break
      }
      case 'mes': {
        customExportToCSV(
          [...calendarioInteresesMetadata].map(el => el.header),
          await getMes(),
          [...calendarioInteresesMetadata].map(el => el.key),
          'CalendarioInteresesMes.csv'
        )
        break
      }
    }
    setLoadingDialog(false)
  }

  useEffect(() => {
    void refetchGetMargenCalendarizado()
  }, [day])

  return isGetMargenCalendarizadoPending ? (
    <Loading />
  ) : (
    <>
      <LoadingDialog open={loadingDialog} />
      <Reload onClick={() => refetchGetMargenCalendarizado()} />
      <Paper elevation={10} sx={{ ...paperStyle, alignItems: 'flex-start' }}>
        {(getMargenCalendarizadoData?.length ?? 0) > 0 && (
          <Box
            sx={{
              alignItems: 'center',

              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              my: 3,
              width: '100%'
            }}
          >
            <Button
              onClick={() =>
                customExportToCSV(
                  [...margenCalendarizadoMetadata].map(el => el.header),
                  getMargenCalendarizadoData ?? [],
                  [...margenCalendarizadoMetadata].map(el => el.key),
                  'MargenCalendarizado.csv',
                  ';'
                )
              }
              startIcon={<FileExcelOutlined />}
              sx={{ margin: '0px 0px 10px 5px' }}
              variant='contained'
            >
              Descargar resumen
            </Button>
            <Button
              onClick={() => downloadRequest('mes')}
              startIcon={<FileExcelOutlined />}
              sx={{ margin: '0px 0px 10px 5px' }}
              variant='contained'
            >
              Descargar fichero del mes en curso
            </Button>
            <Button
              onClick={() => downloadRequest('anual')}
              startIcon={<FileExcelOutlined />}
              sx={{ margin: '0px 0px 10px 5px' }}
              variant='contained'
            >
              Descargar fichero del año en curso
            </Button>
            <Button
              onClick={() => downloadRequest('cuadre')}
              startIcon={<FileExcelOutlined />}
              sx={{ margin: '0px 0px 10px 5px' }}
              variant='contained'
            >
              Descargar fichero del mes en curso Cuadre Contable
            </Button>
          </Box>
        )}
        <GenericTable
          data={getMargenCalendarizadoData ?? []}
          entries={margenCalendarizado}
          metadata={margenCalendarizadoMetadata}
        />
      </Paper>
    </>
  )
}
