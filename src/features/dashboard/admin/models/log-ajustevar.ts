/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const logAjusteVar = type({
  descripcion: 'string',
  hora_Ajuste: 'string',
  logID: 'number',
  mensaje: 'string'
})

export const logAjusteVarMetadata = new Set([
  {
    header: 'Mensaje',
    key: 'mensaje'
  },
  {
    header: 'Descripción',
    key: 'descripcion'
  },
  {
    header: 'Hora Ajuste',
    key: 'hora_Ajuste'
  }
])

export type LogAjusteVar = typeof logAjusteVar.infer
