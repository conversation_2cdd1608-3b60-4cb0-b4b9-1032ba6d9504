import { Box, Button, Typography } from '@mui/material'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import bancoImg from '~/assets/Banco_Cooperativo_Espaol.png'
import bancoImgBlack from '~/assets/Banco_Cooperativo_Espaol_black.png'
import { ThemeMode } from '~/types/resources'

export default () => {
  const { mode } = useContext(ResourceContext)

  return (
    <>
      <Typography variant='h2'>Documentación</Typography>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          gap: 2,
          justifyContent: 'center',
          mt: 5,
          width: '100%'
        }}
      >
        <Button
          onClick={() => window.open('/manualUsuario.pdf', '_blank')}
          variant='contained'
        >
          Manual de Usuario
        </Button>
        <Button
          onClick={() => window.open('/manualTecnico.pdf', '_blank')}
          variant='contained'
        >
          Manual Técnico
        </Button>
      </Box>
      <Box
        sx={{
          alignItems: 'center',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Box
          alt='Banco Cooperativo Español'
          component='img'
          src={mode === ThemeMode.DARK ? bancoImgBlack : bancoImg}
          sx={{ mt: '12%', width: '45%' }}
        />
      </Box>
    </>
  )
}
