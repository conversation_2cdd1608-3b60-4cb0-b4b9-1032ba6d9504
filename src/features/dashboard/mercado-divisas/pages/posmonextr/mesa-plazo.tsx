import { FileExcelOutlined } from '@ant-design/icons'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import {
  spotAdaptiv,
  spotAdaptivMetadata
} from '@d/mercado-divisas/models/spot-adaptiv'
import { MercadoDivisasRepository } from '@d/mercado-divisas/repository/mercado-divisas-repository'
import { Button, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { exportToCSV } from '~/utils/csv'

export default () => {
  const { getMesaPlazo } = container.get(MercadoDivisasRepository)

  const day = calendarStore.actualDay.use()

  const {
    data: getMesaPlazoData,
    isFetching: isGetMesaPlazoPending,
    refetch: refetchGetMesaPlazo
  } = useQuery({
    queryFn: getMesaPlazo,
    queryKey: ['getMesaPlazo']
  })

  useEffect(() => {
    void refetchGetMesaPlazo()
  }, [day])

  return isGetMesaPlazoPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchGetMesaPlazo()} />
      <Paper elevation={10} sx={paperStyle}>
        <GenericTable
          data={getMesaPlazoData ?? []}
          entries={spotAdaptiv}
          metadata={spotAdaptivMetadata}
        />
        <Grid container spacing={1}>
          <Grid item sx={{ display: 'flex', justifyContent: 'start' }} xs={3}>
            <Button
              onClick={() =>
                exportToCSV(getMesaPlazoData ?? [], 'users.csv', ';')
              }
              startIcon={<FileExcelOutlined />}
            />
          </Grid>
        </Grid>
      </Paper>
    </>
  )
}
