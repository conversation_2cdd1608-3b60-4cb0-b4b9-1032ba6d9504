/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const sumMargen = type({
  balance: 'string',
  currencyID: 'string',
  fecha: 'string',
  intAnual: 'number',
  intDia: 'number',
  intMes: 'number',
  orden: 'number',
  rentabilidadAnual: 'number',
  rentabilidadDia: 'number',
  rentabilidadMes: 'number',
  saldo: 'number',
  saldoMes: 'number',
  tipoAnual: 'number',
  tipoMedio: 'number',
  tipoMes: 'number'
})

export const sumMargenMetadata = new Set([
  {
    header: 'Balance',
    key: 'balance'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Tipo Medio',
    key: 'tipoMedio'
  },
  {
    header: 'Rentab. Día',
    key: 'rentabilidadDia'
  },
  {
    header: 'Rentab. Mes',
    key: 'rentabilidadMes'
  },
  {
    header: 'Rentab. Anual',
    key: 'rentabilidadAnual'
  }
])

export type SumMargen = typeof sumMargen.infer

export const sumMargenValidate = (attempt?: SumMargen) =>
  sumMargen(attempt) instanceof type.errors ? resetObject(sumMargen) : attempt
