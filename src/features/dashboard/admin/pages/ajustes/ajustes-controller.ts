/* eslint-disable unicorn/no-keyword-prefix */
import { type Ajustes, ajustesDefault } from '@d/admin/models/ajustes'
import { AjustesRepository } from '@d/admin/repositories/ajustes-repository'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay } from 'rambdax'
import { useCallback, useEffect, useRef, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

type Area = 'COAP' | 'MCAP' | 'MEMO' | 'TESO'

export const validate = (ajustes: Ajustes) => {
  const errors: Record<string, string> = {}
  if (!ajustes.fecha) errors['fecha'] = 'Required'
  if (!ajustes.accountingAreaID || !ajustes.concepto)
    errors['accountingAreaID'] = 'Area y concepto obligatorios'
  return errors
}

export const ajustesController = () => {
  const day = calendarStore.formattedDay.use()

  const [activeArea, setActiveArea] = useState<Area>('MEMO')

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const [ajustesDate, setAjustesDate] = useState(day)

  const { deleteAjustes, getAjustes, insertAjustes, updateAjustes } =
    container.get(AjustesRepository)

  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(ajustesDefault)

  const {
    data: ajustesData,
    error: ajustesError,
    isError: isAjustesError,
    isFetching: isAjustesPending,
    refetch: refetchAjustes
  } = useQuery({
    queryFn: () => getAjustes(ajustesDate, activeArea),
    queryKey: ['getAjustes']
  })

  const submitAction = useCallback(
    async (ajustes: Ajustes, _: FormikHelpers<Ajustes>) => {
      setOpenCreateEditDialog(false)
      await delay(150)
      setLoadingDialog(true)

      await (selectedIndex.current === null
        ? insertAjustes(ajustes)
        : updateAjustes(
            ajustes.fecha,
            ajustes.importe,
            ajustes.accountingAreaID as Area,
            ajustes.concepto
          ))

      setLoadingDialog(false)
      void refetchAjustes()
    },
    []
  )

  const deleteAction = useCallback(async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteAjustes(
        ajustesDate,
        activeArea,
        ajustesData?.[selectedIndex.current ?? 0]?.concepto ?? 'FUTUROS'
      )
      setLoadingDialog(false)
      void refetchAjustes()
    }
  }, [])

  useEffect(() => {
    setAjustesDate(day)
    void refetchAjustes()
  }, [day])

  useEffect(() => {
    void refetchAjustes()
  }, [activeArea, ajustesDate])

  return {
    activeArea,
    ajustesData,
    ajustesDate,
    ajustesError,
    day,
    deleteAction,
    isAjustesError,
    isAjustesPending,
    loadingDialog,
    openCreateEditDialog,
    openDeleteDialog,
    refetchAjustes,
    selectedData,
    selectedIndex,
    setActiveArea,
    setAjustesDate,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  }
}
