import { ChartTabs } from '@d/banco/components/chart-tabs'
import { PlusvaliasChart } from '@d/banco/components/plusvalias-chart'
import { VariacionesChart } from '@d/banco/components/variaciones-chart'
import { Container, Grid } from '@mui/material'

export default () => (
  <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
    <Grid container spacing={3}>
      <ChartTabs isNego />
      <ChartTabs isNego={false} />
      <VariacionesChart />
      <PlusvaliasChart />
    </Grid>
  </Container>
)
