/* eslint-disable react/no-unused-prop-types */
import { ReloadOutlined } from '@ant-design/icons'
import { Box, IconButton } from '@mui/material'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { ThemeMode } from '~/types/resources'

export const Reload = ({
  children,
  onClick
}: {
  children?: ReactNode
  onClick: () => void
}) => {
  const { mode } = useContext(ResourceContext)

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        gap: children === undefined ? null : 2,
        position: 'absolute',
        right: 0,
        top: 30
      }}
    >
      {children}

      <IconButton
        onClick={onClick}
        sx={{ color: mode === ThemeMode.DARK ? '#fff' : 'primary' }}
      >
        <ReloadOutlined />
      </IconButton>
    </Box>
  )
}
