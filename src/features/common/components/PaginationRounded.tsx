import { Pagination, Stack, Typography } from '@mui/material'
import type { FC } from 'react'

export const PaginationRounded: FC<{
  count: number
  onChange: (page: number) => void
  total: number
}> = ({ count, onChange, total }) => (
  <Stack
    spacing={2}
    sx={{
      alignItems: 'baseline',
      display: 'flex',
      flexFlow: 'row nowrap',
      marginInline: 'auto',
      marginY: '5px'
    }}
  >
    <Pagination
      count={count}
      onChange={(_, value) => onChange(value)}
      shape='rounded'
      variant='outlined'
    />
    <Typography sx={{ paddingLeft: '18px' }} variant='subtitle1'>
      Total: {total}
    </Typography>
  </Stack>
)
