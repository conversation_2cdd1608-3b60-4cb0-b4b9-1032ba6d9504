/* eslint-disable sonarjs/use-type-alias */
import type { AjusteAplicados } from '@d/admin/models/ajustes-aplicados'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class AjustesAplicadosRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/ajustesaplicadosmf2'

  readonly getAjustesAplicados = () =>
    this.api.client
      .get<AjusteAplicados[]>(`${this.route}/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getAjustesAplicadosAno = () =>
    this.api.client
      .get<AjusteAplicados[]>(
        `${this.route}/ajustesDelAno/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
