import { SensitivityArea } from '@d/riesgo-mercado/models/sensitivity-area'
import { SensitivityDuration } from '@d/riesgo-mercado/models/sensitivity-duration'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class AnalisisSensibilidadesRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/sensibilidades'

  readonly getSensitivityDuration = (portfolio: 'Inv' | 'Neg' = 'Inv') =>
    this.api.client
      .get<SensitivityDuration[]>(
        `${this.route}/PortfolioDurationArea/${this.attachDate()}/${portfolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getSensitivityIntArea = (portfolio: 'Inv' | 'Neg' = 'Inv') =>
    this.api.client
      .get<SensitivityArea[]>(
        `${this.route}/IRSensitivityArea/${this.attachDate()}/${portfolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getSensitivityProdArea = (portfolio: 'Inv' | 'Neg' = 'Inv') =>
    this.api.client
      .get<SensitivityArea[]>(
        `${this.route}/IRSensitivityProducto/${this.attachDate()}/${portfolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
