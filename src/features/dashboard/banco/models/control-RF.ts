/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const controlDPVRFHTMRF = type({
  nominal: 'number',
  plusvalia: 'number',
  plusvaliaDiaAnterior: 'number',
  plusvaliaMesAnterior: 'number',
  titulo: 'string?',
  valorContable: 'number',
  valordeMercado: 'number',
  variacDiaria: 'number',
  variacMensual: 'number'
})

export const controlDPVRFHTMRFMetadata = new Set([
  {
    header: 'Cartera',
    key: 'titulo'
  },
  {
    header: 'Nominal',
    key: 'nominal'
  },
  {
    header: 'V. Contable',
    key: 'valorContable'
  },
  {
    header: 'V. Mercado',
    key: 'valordeMercado'
  },
  {
    header: 'Var. Diaria',
    key: 'variacDiaria'
  },
  {
    header: 'Var. Mensual',
    key: 'variacMensual'
  },
  {
    header: 'Plusvalía (*)',
    key: 'plusvalia'
  }
])

export type ControlDPVRFHTMRF = typeof controlDPVRFHTMRF.infer

export const controlDPVRFHTMRFValidate = (attempt?: ControlDPVRFHTMRF) =>
  controlDPVRFHTMRF(attempt) instanceof type.errors
    ? resetObject(controlDPVRFHTMRF)
    : attempt
