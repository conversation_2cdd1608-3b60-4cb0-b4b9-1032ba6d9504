/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const equivalencia = type({
  nombreCortoSGT: 'string?',
  nombreLargoSGT: 'string',
  sgtID: 'number?',
  tradeGroup: 'string'
})

export const equivalenciaMetadata = new Set([
  {
    header: 'SGT ID',
    key: 'sgtID'
  },
  {
    header: 'Nombre Largo SGT',
    key: 'nombreLargoSGT'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  }
])

export type Equivalencia = typeof equivalencia.infer

export const equivalenciaDefault: Equivalencia = {
  nombreCortoSGT: '',
  nombreLargoSGT: '',
  sgtID: 0,
  tradeGroup: ''
}
