import { store } from '@davstack/store'
import type { AlertProps, SnackbarOrigin } from '@mui/material'

type Snackbar = {
  action: boolean
  actionButton: boolean
  alert: AlertProps
  anchorOrigin: SnackbarOrigin
  close: boolean
  dense: boolean
  iconVariant: string
  maxStack: number
  message: string
  open: boolean
  transition: string
  variant: string
}

export const snackbarStore = store<Snackbar>({
  action: false,
  actionButton: false,
  alert: {
    // color: 'primary',
    color: 'success',
    variant: 'filled'
  },
  anchorOrigin: {
    horizontal: 'right',
    vertical: 'bottom'
  },
  close: true,
  dense: false,
  iconVariant: 'usedefault',
  maxStack: 3,
  message: 'Note archived',
  open: false,
  transition: 'Fade',
  variant: 'default'
}).actions(store => ({
  closeSnackbar: () => store.open.set(false),
  handlerDense: (dense: boolean) => store.dense.set(dense),
  handlerIconVariants: (iconVariant: string) =>
    store.iconVariant.set(iconVariant),
  handlerIncrease: (maxStack: number) => store.maxStack.set(maxStack),
  openSnackbar: ({
    action,
    actionButton,
    alert,
    anchorOrigin,
    close,
    message,
    open,
    transition,
    variant
  }: Partial<Snackbar>) => {
    store.action.set(action ?? store.action.get())
    store.open.set(open ?? store.open.get())
    store.message.set(message ?? store.message.get())
    store.anchorOrigin.set(anchorOrigin ?? store.anchorOrigin.get())
    store.variant.set(variant ?? store.variant.get())
    store.alert.set({
      color: alert?.color ?? store.alert.get().color ?? 'success',
      variant: alert?.variant ?? store.alert.get().variant ?? 'filled'
    })
    store.transition.set(transition ?? store.transition.get())
    store.close.set(close === false ? close : store.close.get())
    store.actionButton.set(actionButton ?? store.actionButton.get())
  }
}))
