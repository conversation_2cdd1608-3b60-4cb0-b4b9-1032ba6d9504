import { alpha, Box, styled, type Theme } from '@mui/material'
import type {
  MUIStyledCommonProps,
  SxProps,
  SystemStyleObject
} from '@mui/system'
import type { FC, ReactNode } from 'react'
import { BrowserView, MobileView } from 'react-device-detect'
//@ts-expect-error notesting for typings for this library
import SimpleBar from 'simplebar-react'

const RootStyle = styled(BrowserView)({
  flexGrow: 1,
  height: '100%',
  overflow: 'hidden'
})

const SimpleBarStyle = styled(SimpleBar)(({ theme }) => ({
  '& .simplebar-mask': {
    zIndex: 'inherit'
  },
  '& .simplebar-scrollbar': {
    '&.simplebar-visible:before': {
      opacity: 1
    },
    '&:before': {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
      backgroundColor: alpha(theme.palette.grey[500], 0.48)
    }
  },
  '& .simplebar-track.simplebar-horizontal .simplebar-scrollbar': {
    height: 6
  },
  '& .simplebar-track.simplebar-vertical': {
    width: 10
  },
  maxHeight: '100%'
}))

export const SimpleBarScroll: FC<{
  children: ReactNode
  other?: MUIStyledCommonProps<Theme>
  sx?: SxProps<Theme>
}> = ({ children, sx, ...other }) => (
  <>
    <RootStyle>
      <SimpleBarStyle clickOnTrack={false} sx={sx} {...other}>
        {children}
      </SimpleBarStyle>
    </RootStyle>
    <MobileView>
      <Box
        sx={{
          overflowX: 'auto',
          ...(sx as SystemStyleObject<Theme>)
        }}
        {...other}
      >
        {children}
      </Box>
    </MobileView>
  </>
)
