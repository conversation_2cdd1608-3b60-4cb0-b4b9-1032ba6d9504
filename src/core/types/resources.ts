export enum MenuOrientation {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical'
}

export enum ThemeDirection {
  LTR = 'ltr',
  RTL = 'rtl'
}

export enum ThemeMode {
  DARK = 'dark',
  LIGHT = 'light'
}

export type FontFamily =
  | `'Inter', sans-serif`
  | `'Poppins', sans-serif`
  | `'Public Sans', sans-serif`
  | `'Roboto', sans-serif`

export type PresetColor =
  | 'default'
  | 'theme1'
  | 'theme2'
  | 'theme3'
  | 'theme4'
  | 'theme5'
  | 'theme6'
  | 'theme7'
  | 'theme8'

export type ResourcesConfig = {
  container: boolean
  fontFamily: FontFamily
  i18n: 'en' | 'es'
  menuOrientation: MenuOrientation
  miniDrawer: boolean
  mode: ThemeMode
  presetColor: PresetColor
  themeDirection: ThemeDirection
}
