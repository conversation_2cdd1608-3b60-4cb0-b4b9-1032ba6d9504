/* eslint-disable @typescript-eslint/no-empty-object-type */
/* eslint-disable @typescript-eslint/consistent-type-definitions */

import '@mui/material'
import '@mui/material/styles'

declare module '@mui/material' {
  interface Color {
    0?: string
    A50?: string
    A800?: string
  }
}

declare module '@mui/material/Button' {
  export interface ButtonPropsSizeOverrides {
    extraSmall
  }

  export interface ButtonPropsVariantOverrides {
    dashed
    light
    shadow
  }
}

declare module '@mui/material/styles' {
  export interface PaletteOptions {
    0?: string
    50?: string
    100?: string
    200?: string
    300?: string
    400?: string
    500?: string
    600?: string
    700?: string
    800?: string
    900?: string
    A50?: string
    A100?: string
    A200?: string
    A300?: string
    A400?: string
    A700?: string
    A800?: string
    darker: string
    lighter: string
  }

  interface PaletteColor extends PaletteOptions {}

  interface SimplePaletteColorOptions extends PaletteOptions {}

  interface Theme {
    customShadows: {
      button: string
      error: string
      errorButton: string
      grey: string
      greyButton: string
      info: string
      infoButton: string
      primary: string
      primaryButton: string
      secondary: string
      secondaryButton: string
      success: string
      successButton: string
      text: string
      warning: string
      warningButton: string
      z1: string
    }
  }
}
