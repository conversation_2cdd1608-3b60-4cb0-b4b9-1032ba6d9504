import type { Theme } from '@mui/material/styles'

export const ToggleButton = (theme: Theme) => ({
  MuiToggleButton: {
    styleOverrides: {
      root: {
        '&.Mui-disabled': {
          borderColor: theme.palette.divider,
          color: theme.palette.text.disabled
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.palette.secondary.dark}`,
          outlineOffset: 2
        }
      }
    }
  }
})
