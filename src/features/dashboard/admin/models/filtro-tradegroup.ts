/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const filtroTradeGroup = type({
  activo: 'string?',
  activoID: 'number?',
  areaID: 'number?',
  areasBanco: 'string?',
  epigrafeActivo: 'string?',
  epigrafePasivo: 'string?',
  fecha: 'string?',
  iD: 'number?',
  pasivo: 'string?',
  pasivoID: 'number?',
  producto: 'string?',
  tradeGroup: 'string?'
})

export type FiltroTradeGroup = typeof filtroTradeGroup.infer

export const filtroTradeGroupDefault: FiltroTradeGroup = {
  activo: '',
  activoID: 0,
  areaID: 0,
  areasBanco: '',
  epigrafeActivo: '',
  epigrafePasivo: '',
  fecha: '',
  iD: 0,
  pasivo: '',
  pasivoID: 0,
  producto: '',
  tradeGroup: ''
}

export const filtroTradeGroupMetadata = new Set([
  {
    header: 'Areas Banco',
    key: 'areasBanco'
  },
  {
    header: 'Cartera',
    key: 'tradeGroup'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Activo',
    key: 'activo'
  },
  {
    header: 'Epígrafe Activo',
    key: 'epigrafeActivo'
  },
  {
    header: 'Pasivo',
    key: 'pasivo'
  },
  {
    header: 'Epígrafe Pasivo',
    key: 'epigrafePasivo'
  }
])
