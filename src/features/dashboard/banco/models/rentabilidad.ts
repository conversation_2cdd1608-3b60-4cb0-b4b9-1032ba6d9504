/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'
import { prop } from 'rambdax'

export const margenAreas = type({
  bancaCorporativa: 'number',
  cOAP: 'number',
  fecha: 'string',
  mercadodeDivisas: 'number',
  mercadoMonetario: 'number',
  rentaFija: 'number',
  saldosTesoreros: 'number',
  total: 'number'
})

export const margenAreasMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Banca Corporativa',
    key: 'bancaCorporativa'
  },
  {
    header: 'COAP',
    key: 'cOAP'
  },
  {
    header: 'Mercado Monetario',
    key: 'mercadoMonetario'
  },
  {
    header: 'Mercado de Divisas',
    key: 'mercadodeDivisas'
  },
  {
    header: 'Renta Fija',
    key: 'rentaFija'
  },
  {
    header: 'Saldos Tesoreros',
    key: 'saldosTesoreros'
  },
  {
    header: 'Total',
    key: 'total'
  }
])

export type MargenAreas = typeof margenAreas.infer

export const margenAreasSeries = (data: MargenAreas[]) => {
  const series: ApexAxisChartSeries = []

  for (const metadata of margenAreasMetadata) {
    const serie = Array.of<number[]>()

    for (const row of data) {
      const key = metadata.key as keyof MargenAreas

      if (key !== 'fecha') {
        serie.push([new Date(row.fecha).getTime(), prop(key, row)])
      }
    }

    if (metadata.key !== 'fecha')
      series.push({ data: serie, name: metadata.header })
  }

  return series
}
