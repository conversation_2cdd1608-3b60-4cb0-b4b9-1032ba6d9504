import { MainCard } from '@c/components/MainCard'
import {
  CardMedia,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Stack,
  Typography,
  useMediaQuery
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import type { ChangeEvent } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { menuStore } from '@/common/store/menu-store'
import containerLayout from '~/assets/container.svg'
import defaultLayout from '~/assets/default.svg'
import { MenuOrientation, ThemeDirection } from '~/types/resources'

export const ThemeMenuLayout = () => {
  const theme = useTheme()
  const downLG = useMediaQuery(theme.breakpoints.down('lg'))

  const {
    menuOrientation,
    onChangeDirection,
    onChangeMenuOrientation,
    onChangeMiniDrawer
  } = useContext(ResourceContext)

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG

  const handleContainerChange = (e: ChangeEvent<HTMLInputElement>) => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
    if (e.target.value === MenuOrientation.HORIZONTAL) {
      onChangeMiniDrawer(true)
      onChangeDirection(ThemeDirection.LTR)
      onChangeMenuOrientation(e.target.value)
      menuStore.openDrawer(false)
    } else {
      onChangeMiniDrawer(true)
      onChangeDirection(ThemeDirection.LTR)
      onChangeMenuOrientation(e.target.value as MenuOrientation)
      menuStore.openDrawer(true)
    }
  }

  return (
    <RadioGroup
      aria-label='payment-card'
      name='payment-card'
      onChange={handleContainerChange}
      row
      value={menuOrientation}
    >
      <Grid container spacing={1.75} sx={{ ml: 0 }}>
        <Grid item>
          <FormControlLabel
            control={
              <Radio
                sx={{ display: 'none' }}
                value={MenuOrientation.VERTICAL}
              />
            }
            label={
              <MainCard
                border={false}
                content={false}
                sx={{
                  bgcolor: isHorizontal
                    ? 'secondary.lighter'
                    : 'primary.lighter',
                  p: 1
                }}
                {...(!isHorizontal && {
                  boxShadow: true,
                  shadow: theme.customShadows.primary
                })}
              >
                <Stack alignItems='center' spacing={1.25}>
                  <CardMedia
                    alt='Vertical'
                    component='img'
                    src={defaultLayout}
                    sx={{ borderRadius: 1, height: 64, width: 64 }}
                  />
                  <Typography variant='caption'>Vertical</Typography>
                </Stack>
              </MainCard>
            }
            sx={{
              '& .MuiFormControlLabel-label': { flex: 1 },
              display: 'flex'
            }}
          />
        </Grid>
        <Grid item>
          <FormControlLabel
            control={
              <Radio
                sx={{ display: 'none' }}
                value={MenuOrientation.HORIZONTAL}
              />
            }
            label={
              <MainCard
                border={false}
                content={false}
                sx={{
                  bgcolor: isHorizontal
                    ? 'primary.lighter'
                    : 'secondary.lighter',
                  p: 1
                }}
                {...(isHorizontal && {
                  boxShadow: true,
                  shadow: theme.customShadows.primary
                })}
              >
                <Stack alignItems='center' spacing={1.25}>
                  <CardMedia
                    alt='horizontal'
                    component='img'
                    src={containerLayout}
                    sx={{ borderRadius: 1, height: 64, width: 64 }}
                  />
                  <Typography variant='caption'>Horizontal</Typography>
                </Stack>
              </MainCard>
            }
            sx={{
              '& .MuiFormControlLabel-label': { flex: 1 },
              display: 'flex'
            }}
          />
        </Grid>
      </Grid>
    </RadioGroup>
  )
}
