/* eslint-disable sonarjs/void-use */
import { Paper } from '@mui/material'
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

import { paperStyle } from '~/resources/config/paper'

const route = '/admin/calcular-margen'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/calcular-margen`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/ficheros-sgt`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/importar-plvar`)
        setMenuId(2)
        break
      }
      case 3: {
        if (val !== menuId) void navigate(`${route}/historico-benchmarks`)
        setMenuId(3)
        break
      }
      case 4: {
        if (val !== menuId) void navigate(`${route}/ratio-liquidez`)
        setMenuId(4)
        break
      }
      case 5: {
        if (val !== menuId) void navigate(`${route}/jobs-desatendidos`)
        setMenuId(5)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('calcular-margen')) setMenuId(0)
    if (path.includes('ficheros-sgt')) setMenuId(1)
    if (path.includes('importar-plvar')) setMenuId(2)
    if (path.includes('historico-benchmarks')) setMenuId(3)
    if (path.includes('ratio-liquidez')) setMenuId(4)
    if (path.includes('jobs-desatendidos')) setMenuId(5)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Calcular margen financiero</MenuItem>
                  <MenuItem value={1}>Importación ficheros SGT</MenuItem>
                  <MenuItem value={2}>Importar PL & VAR</MenuItem>
                  <MenuItem value={3}>Histórico de Benckmarks</MenuItem>
                  <MenuItem value={4}>Ratio de Liquidez</MenuItem>
                  <MenuItem value={5}>Jobs Desatendidos</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Paper elevation={1} sx={paperStyle}>
            <Outlet />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}
