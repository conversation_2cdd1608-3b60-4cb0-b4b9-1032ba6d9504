import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class FicherosRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/cargaficheros'

  readonly coberturasJob = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/coberturas/${this.attachDate()}`
    )

  readonly divisaJob = () =>
    this.api.client.post<{ message: string }>(`${this.route}/divisa`)

  readonly ficheroCoberturasJob = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/ficheroCoberturas/${this.attachDate()}`
    )

  readonly flujosJob = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/flujos/${this.attachDate()}`
    )

  readonly historizaPLDivisaJob = () =>
    this.api.client.post<{ message: string }>(
      `${this.route}/historiza/${this.attachDate()}`
    )

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
