/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable sonarjs/no-nested-conditional */
/* eslint-disable @typescript-eslint/no-misused-spread */
/* eslint-disable sonarjs/no-nested-functions */
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { PaginationRounded } from '@c/components/PaginationRounded'
import { TitleSubtitle } from '@c/components/TitleSubtitle'
import {
  Box,
  IconButton,
  type SxProps,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography
} from '@mui/material'
import type { Type } from 'arktype'
import { piped } from 'rambdax'
import type { FC } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { ThemeMode } from '~/types/resources'

import {
  getCellConfig,
  getValueFormat,
  useGenericTable
} from './use-generictable'

export const GenericTable: FC<{
  data: Record<string, unknown>[]
  emptyMessage?: string
  entries: Type<object>
  isDeleteAction?: boolean
  isEditAction?: boolean
  isSelected?: boolean
  metadata: Set<{ header: string; key: string }>
  noTotal?: boolean
  onDeleteClick?: (index: number) => void
  onEditClick?: (index: number) => void
  onRowClick?: (index: number) => void
  paginationSize?: number
  sx?: SxProps
  tipoPercent?: boolean
  title?: string
}> = ({
  data,
  emptyMessage = 'No hay datos que mostrar',
  entries,
  isDeleteAction = false,
  isEditAction = false,
  isSelected = false,
  metadata,
  noTotal = true,
  onDeleteClick,
  onEditClick,
  onRowClick,
  paginationSize = 50,
  sx,
  tipoPercent = false,
  title
}) => {
  const {
    paginationActual,
    paginationCount,
    realIndex,
    setPaginationActual,
    slicedData,
    tableMetadata,
    totalRows
  } = useGenericTable(
    data,
    entries,
    metadata,
    paginationSize,
    isDeleteAction,
    isEditAction
  )

  const [selectIndex, setSelectedIndex] = useState<number | null>(null)

  const { mode } = useContext(ResourceContext)

  return (
    <>
      {title !== undefined && (
        <TitleSubtitle sxProps={{ ml: 3 }} title={title} />
      )}
      {totalRows === 0 ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            my: 2,
            width: '100%'
          }}
        >
          <Typography sx={{ fontStyle: 'italic' }} variant='subtitle1'>
            {emptyMessage}
          </Typography>
        </Box>
      ) : (
        <Table size='small' sx={sx}>
          <TableHead
            sx={{
              bgcolor: mode === ThemeMode.DARK ? '#424242' : null,
              borderColor: mode === ThemeMode.DARK ? '#212121' : null
            }}
          >
            <TableRow
              sx={{
                color: mode === ThemeMode.DARK ? '#fff' : '#004B36',
                whiteSpace: 'nowrap'
              }}
            >
              {tableMetadata.map((metadata, index) =>
                piped(getCellConfig(metadata, false, true), config => (
                  <TableCell
                    align={config.align}
                    key={index}
                    sx={{
                      ...config.sx,
                      color: mode === ThemeMode.DARK ? '#fff' : '#004B36'
                    }}
                  >
                    {metadata.title}
                  </TableCell>
                ))
              )}
            </TableRow>
          </TableHead>
          <TableBody
            sx={{ bgcolor: mode === ThemeMode.DARK ? ' #212121' : 'white' }}
          >
            {slicedData.map((row, rowIndex) => (
              <TableRow
                key={rowIndex}
                onClick={() => {
                  if (isSelected) setSelectedIndex(rowIndex)
                  onRowClick?.(realIndex(rowIndex))
                }}
                sx={
                  !noTotal &&
                  paginationActual === paginationCount &&
                  rowIndex === slicedData.length - 1
                    ? {
                        backgroundColor:
                          mode === ThemeMode.DARK ? '#3b4240' : '#B2C8C2',
                        fontWeight: 'bold'
                      }
                    : {
                        backgroundColor:
                          isSelected && selectIndex === rowIndex
                            ? '#B2C8C2'
                            : null
                      }
                }
              >
                {tableMetadata.map((metadata, index) => {
                  const value =
                    metadata.key === 'actions'
                      ? 'ACTIONABLE_MF2'
                      : (row[metadata.key] ?? 0)
                  const config = getCellConfig(
                    metadata,
                    metadata.type === 'number' &&
                      typeof value === 'number' &&
                      value < 0,
                    rowIndex === slicedData.length - 1 && !noTotal,
                    tipoPercent
                  )
                  return metadata.key === 'actions' ? (
                    <TableCell>
                      {isEditAction && (
                        <IconButton
                          onClick={() => onEditClick?.(realIndex(rowIndex))}
                        >
                          <EditOutlined />
                        </IconButton>
                      )}
                      {isDeleteAction && (
                        <IconButton
                          onClick={() => onDeleteClick?.(realIndex(rowIndex))}
                        >
                          <DeleteOutlined />
                        </IconButton>
                      )}
                    </TableCell>
                  ) : (
                    <TableCell align={config.align} key={index} sx={config.sx}>
                      {
                        getValueFormat(value, config.isPercent) as
                          | boolean
                          | number
                          | string
                      }
                    </TableCell>
                  )
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
      {paginationCount >= 2 && (
        <PaginationRounded
          count={paginationCount}
          onChange={setPaginationActual}
          total={totalRows}
        />
      )}
    </>
  )
}
