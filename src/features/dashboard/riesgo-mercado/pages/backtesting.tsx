import { DownOutlined } from '@ant-design/icons'
import { AppChart } from '@d/common/components/Chart'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import { backtestingChartOpts } from '@d/riesgo-mercado/data/backtesting-chart'
import {
  backtesting,
  backtestingMetadata
} from '@d/riesgo-mercado/models/backtesting'
import { exced, excedMetadata } from '@d/riesgo-mercado/models/exced'
import { BacktestingRepository } from '@d/riesgo-mercado/repositories/backtesting-repository'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Container,
  Grid,
  MenuItem,
  Paper,
  Select,
  type SelectChangeEvent
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useRef, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

const areaData = ['TOTAL', 'Mercado Monetario', 'Renta', 'Fija', 'COAP']
const productoData = [
  'TOTAL',
  'Bond',
  'Bond Future',
  'CashflowMatching',
  'Equity',
  'FRN',
  'FX',
  'FXplazo',
  'Index Linked Bond',
  'Inflation Swap',
  'Pagare',
  'Swap',
  'Treasury Bill'
]

const containerStyle = {
  alignItems: 'center',
  display: 'flex',
  justifyContent: 'space-between'
}

const barStyle = {
  borderRadius: 1,
  height: '15px',
  width: '100px'
}

const Incumplimientos = () => (
  <Grid item xs={3}>
    <Box sx={containerStyle}>
      <Box sx={{ ...barStyle, backgroundColor: 'green' }}></Box>
      Hasta 4 incumplimientos
    </Box>
    <Box sx={containerStyle}>
      <Box sx={{ ...barStyle, backgroundColor: 'yellow' }}></Box>
      De 5 a 9 incumplimientos
    </Box>
    <Box sx={containerStyle}>
      <Box sx={{ ...barStyle, backgroundColor: 'red' }}></Box>
      10 o mas incumplimientos
    </Box>
  </Grid>
)

export default () => {
  const day = calendarStore.actualDay.use()

  const { getBacktesting, getExcedInv, getExcedNeg } = container.get(
    BacktestingRepository
  )

  const invSeries = useRef<ApexAxisChartSeries>([])
  const invOptions = useRef(backtestingChartOpts)
  const negSeries = useRef<ApexAxisChartSeries>([])
  const negOptions = useRef(backtestingChartOpts)

  const [areaSelectInv, setAreaSelectInv] = useState('TOTAL')
  const [productoSelectInv, setProductoSelectInv] = useState('TOTAL')
  const [areaSelectNeg, setAreaSelectNeg] = useState('TOTAL')
  const [productoSelectNeg, setProductoSelectNeg] = useState('TOTAL')

  const {
    data: getExcedInvData,
    error: getExcedInvError,
    isError: isGetExcedInvError,
    isFetching: isGetExcedInvPending,
    refetch: refetchGetExcedInv
  } = useQuery({
    queryFn: getExcedInv,
    queryKey: ['getExcedInv']
  })

  const {
    data: getExcedNegData,
    error: getExcedNegError,
    isError: isGetExcedNegError,
    isFetching: isGetExcedNegPending,
    refetch: refetchGetExcedNeg
  } = useQuery({
    queryFn: getExcedNeg,
    queryKey: ['getExcedNeg']
  })

  const {
    data: getBackTestInvData,
    error: getBackTestInvError,
    isError: isGetBackTestInvError,
    isFetching: isGetBackTestInvPending,
    refetch: refetchGetBackTestInv
  } = useQuery({
    queryFn: () =>
      getBacktesting(areaSelectInv, productoSelectInv).then(data => {
        invOptions.current = {
          ...invOptions.current,
          xaxis: {
            ...invOptions.current.xaxis,
            categories: data.map(el => el.fecha)
          }
        }
        invSeries.current = [
          { data: data.map(el => el.cleanPL), name: 'CleanPL' },
          { data: data.map(el => el.vaR), name: 'VaR' }
        ]
        return data
      }),
    queryKey: ['getBacktestingInv']
  })

  const {
    data: getBackTestNegData,
    error: getBackTestNegError,
    isError: isGetBackTestNegError,
    isFetching: isGetBackTestNegPending,
    refetch: refetchGetBackTestNeg
  } = useQuery({
    queryFn: () =>
      getBacktesting(areaSelectNeg, productoSelectNeg, 'Negociacion').then(
        data => {
          negOptions.current = {
            ...negOptions.current,
            xaxis: {
              ...negOptions.current.xaxis,
              categories: data.map(el => el.fecha)
            }
          }
          negSeries.current = [
            { data: data.map(el => el.cleanPL), name: 'CleanPL' },
            { data: data.map(el => el.vaR), name: 'VaR' }
          ]
          return data
        }
      ),
    queryKey: ['getBacktestingNeg']
  })

  const selectInvChange = (e: SelectChangeEvent, isArea: boolean) => {
    const { value } = e.target
    isArea ? setAreaSelectInv(value) : setProductoSelectInv(value)
  }

  const selectNegChange = (e: SelectChangeEvent, isArea: boolean) => {
    const { value } = e.target
    isArea ? setAreaSelectNeg(value) : setProductoSelectNeg(value)
  }

  const refetch = () => {
    void refetchGetBackTestInv()
    void refetchGetBackTestNeg()
    void refetchGetExcedInv()
    void refetchGetExcedNeg()
  }

  useEffect(() => {
    void refetchGetBackTestInv()
  }, [areaSelectInv, productoSelectInv])

  useEffect(() => {
    void refetchGetBackTestNeg()
  }, [areaSelectNeg, productoSelectNeg])

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ mb: 3 }} xs={12}>
            <Accordion defaultExpanded>
              <AccordionSummary
                aria-controls='panel1-content'
                expandIcon={<DownOutlined />}
                id='panel1-header'
              >
                <h3>Cartera Disponible</h3>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        width: '100%'
                      }}
                    >
                      <Grid item sx={{ display: 'flex' }} xs={3}>
                        <p style={{ marginRight: '20px' }}>Área: </p>
                        <Select
                          id='select1'
                          onChange={e => selectInvChange(e, true)}
                          value={areaSelectInv}
                        >
                          {areaData.map((value, index) => (
                            <MenuItem key={index} value={value}>
                              {value}
                            </MenuItem>
                          ))}
                        </Select>
                        <p style={{ marginLeft: 40, marginRight: 10 }}>
                          Producto:
                        </p>
                        <Select
                          id='select2'
                          onChange={e => selectInvChange(e, false)}
                          value={productoSelectInv}
                        >
                          {productoData.map((value, index) => (
                            <MenuItem key={index} value={value}>
                              {value}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>
                    </div>
                  </Grid>
                  <Grid item xs={9}>
                    <Fetching
                      errorMessage={getExcedInvError?.message}
                      isError={isGetExcedInvError}
                      isFetching={isGetExcedInvPending}
                    >
                      <Paper
                        elevation={10}
                        sx={{
                          ...paperStyle,
                          maxHeight: '400px',
                          overflowY: 'auto'
                        }}
                      >
                        <GenericTable
                          data={getExcedInvData ?? []}
                          entries={exced}
                          metadata={excedMetadata}
                          title='Incumplimiento últimos 250 días'
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={3}>
                    <Incumplimientos />
                  </Grid>
                  <Grid item xs={6}>
                    <Fetching
                      errorMessage={getBackTestInvError?.message}
                      isError={isGetBackTestInvError}
                      isFetching={isGetBackTestInvPending}
                    >
                      <Paper
                        elevation={10}
                        sx={{
                          ...paperStyle,
                          maxHeight: '350px',
                          overflowY: 'auto'
                        }}
                      >
                        <GenericTable
                          data={getBackTestInvData ?? []}
                          entries={backtesting}
                          metadata={backtestingMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper elevation={1} sx={paperStyle}>
                      <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                        <Fetching
                          errorMessage={getBackTestInvError?.message}
                          isError={isGetBackTestInvError}
                          isFetching={isGetBackTestInvPending}
                        >
                          <AppChart
                            height={305}
                            options={invOptions.current}
                            series={invSeries.current}
                            type='line'
                            width={450}
                          />
                        </Fetching>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item sx={{ mb: 3 }} xs={12}>
            <Accordion>
              <AccordionSummary
                aria-controls='panel1-content'
                expandIcon={<DownOutlined />}
                id='panel1-header'
              >
                <h3>Cartera Negociación</h3>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        width: '100%'
                      }}
                    >
                      <Grid item sx={{ display: 'flex' }} xs={3}>
                        <p style={{ marginRight: '20px' }}>Área: </p>
                        <Select
                          id='select3'
                          onChange={e => selectNegChange(e, true)}
                          value={areaSelectNeg}
                        >
                          {areaData.map((value, index) => (
                            <MenuItem key={index} value={value}>
                              {value}
                            </MenuItem>
                          ))}
                        </Select>
                        <p style={{ marginLeft: 40, marginRight: 10 }}>
                          Producto:
                        </p>
                        <Select
                          id='select4'
                          onChange={e => selectNegChange(e, false)}
                          value={productoSelectNeg}
                        >
                          {productoData.map((value, index) => (
                            <MenuItem key={index} value={value}>
                              {value}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>
                    </div>
                  </Grid>
                  <Grid item xs={9}>
                    <Fetching
                      errorMessage={getExcedNegError?.message}
                      isError={isGetExcedNegError}
                      isFetching={isGetExcedNegPending}
                    >
                      <Paper
                        elevation={10}
                        sx={{
                          ...paperStyle,
                          maxHeight: '400px',
                          overflowY: 'auto'
                        }}
                      >
                        <GenericTable
                          data={getExcedNegData ?? []}
                          entries={exced}
                          metadata={excedMetadata}
                          title='Incumplimiento últimos 250 días'
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={3}>
                    <Incumplimientos />
                  </Grid>
                  <Grid item xs={6}>
                    <Fetching
                      errorMessage={getBackTestNegError?.message}
                      isError={isGetBackTestNegError}
                      isFetching={isGetBackTestNegPending}
                    >
                      <Paper
                        elevation={10}
                        sx={{
                          ...paperStyle,
                          maxHeight: '350px',
                          overflowY: 'auto'
                        }}
                      >
                        <GenericTable
                          data={getBackTestNegData ?? []}
                          entries={backtesting}
                          metadata={backtestingMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper elevation={1} sx={paperStyle}>
                      <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                        <Fetching
                          errorMessage={getBackTestNegError?.message}
                          isError={isGetBackTestNegError}
                          isFetching={isGetBackTestNegPending}
                        >
                          <AppChart
                            height={305}
                            options={negOptions.current}
                            series={negSeries.current}
                            type='line'
                            width={450}
                          />
                        </Fetching>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
