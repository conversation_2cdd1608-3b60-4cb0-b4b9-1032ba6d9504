/* eslint-disable unicorn/prefer-global-this */
import type { ApexOptions } from 'apexcharts'
import { Suspense } from 'react'

const Chart = lazy(() => import('react-apexcharts'))

export const AppChart: FC<{
  height?: number | string
  options: ApexOptions
  series: ApexAxisChartSeries | ApexNonAxisChartSeries
  type:
    | 'area'
    | 'bar'
    | 'boxPlot'
    | 'bubble'
    | 'candlestick'
    | 'donut'
    | 'heatmap'
    | 'line'
    | 'pie'
    | 'polarArea'
    | 'radar'
    | 'radialBar'
    | 'rangeBar'
    | 'scatter'
    | 'treemap'
  width?: number | string
}> = props => {
  if (typeof window === 'undefined') return null
  return (
    <Suspense fallback={<div></div>}>
      <Chart {...props} />
    </Suspense>
  )
}
