/* eslint-disable sonarjs/use-type-alias */
import type { Productos } from '@d/admin/models/productos'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class ProductosRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/producto'

  readonly deleteProductos = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getProductos = () =>
    this.api.client
      .get<Productos[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertProductos = (productos: Productos) =>
    this.api.client.post<{ message: string }>(this.route, productos)

  readonly updateProductos = (id: string, productos: Productos) =>
    this.api.client.put<{ message: string }>(`${this.route}/${id}`, productos)
}
