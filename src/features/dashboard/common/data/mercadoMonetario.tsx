import { FileTextOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const mercadoMonetario: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'mercadoMonetario-margenFinanciero',
          title: <FormattedMessage id='margenFinanciero' />,
          type: 'item',
          url: '/mercado-monetario/margen-financiero'
        },
        {
          id: 'mercadoMonetario-trading',
          title: <FormattedMessage id='trading' />,
          type: 'item',
          url: '/mercado-monetario/trading'
        },
        {
          id: 'mercadoMonetario-carteraInversion',
          title: <FormattedMessage id='carteraInversion' />,
          type: 'item',
          url: '/mercado-monetario/cartera-inversion'
        },
        {
          id: 'mercadoMonetario-carteraVencimiento',
          title: <FormattedMessage id='carteraVencimiento' />,
          type: 'item',
          url: '/mercado-monetario/cartera-vencimiento'
        },
        {
          id: 'mercadoMonetario-coberturas',
          title: <FormattedMessage id='coberturas' />,
          type: 'item',
          url: '/mercado-monetario/coberturas'
        },
        {
          id: 'mercadoMonetario-riesgoMercado',
          title: <FormattedMessage id='riesgoMercado' />,
          type: 'item',
          url: '/mercado-monetario/riesgo-mercado'
        }
      ],
      icon: FileTextOutlined,
      id: 'mercadoMonetario',
      title: <FormattedMessage id='mercadoMonetario' />,
      type: 'collapse'
    }
  ],
  icon: FileTextOutlined,
  id: 'group-mercadoMonetario',
  title: <FormattedMessage id='mercadoMonetario' />,
  type: 'group'
}
