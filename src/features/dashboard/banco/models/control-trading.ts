/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const controlTrading = type({
  areaID: 'number',
  dayTotal: 'number',
  monthTotal: 'number',
  posEquiv: 'number',
  titulo: 'string',
  yearTotal: 'number'
})

export const controlTradingMetadata = new Set([
  {
    header: 'Cartera',
    key: 'titulo'
  },
  {
    header: 'Día €',
    key: 'dayTotal'
  },
  {
    header: 'Mes €',
    key: 'monthTotal'
  },
  {
    header: 'Año €',
    key: 'yearTotal'
  }
])

export type ControlTrading = typeof controlTrading.infer
export const controlTradingValidate = (attempt?: ControlTrading) =>
  controlTrading(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
