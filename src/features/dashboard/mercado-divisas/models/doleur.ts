/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

const fiatMetadata = new Set([
  {
    header: 'InstrumentID',
    key: 'instrumentID'
  },
  {
    header: 'Currency',
    key: 'currencyID'
  },
  {
    header: 'Counterparty ID',
    key: 'counterpartyID'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Position',
    key: 'modo'
  },
  {
    header: 'Start Date',
    key: 'startDateAdj'
  },
  {
    header: 'Expiry Date',
    key: 'expiryDate'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Int Dia',
    key: 'intDia'
  },
  {
    header: 'Margen',
    key: 'margen'
  },
  {
    header: 'Acumulado',
    key: 'acumulado'
  },
  {
    header: 'Tipo',
    key: 'tipo'
  },
  {
    header: 'Coste Día',
    key: 'costeDia'
  }
])

export const dolEur = type({
  accountingAreaID: 'number',
  acumulado: 'number',
  acumuladoEONIA: 'number',
  balance: 'string',
  costeDia: 'number',
  counterpartyID: 'string',
  cupones: 'number',
  currencyID: 'string',
  epigrafe: 'number',
  expiryDate: 'string',
  fechaValoracion: 'string',
  instrumentID: 'string',
  intDia: 'number',
  margen: 'number',
  marketID: 'string',
  modo: 'string',
  pID: 'number',
  producto: 'string',
  saldo: 'number',
  spread: 'number',
  startDateAdj: 'string',
  tipo: 'number',
  tradeGroup: 'string'
})

export const eurMetadata = new Set([
  ...fiatMetadata,
  {
    header: 'Tipo Cambio',
    key: 'acumuladoEONIA'
  }
])

export const dolMetadata = new Set([
  ...fiatMetadata,
  {
    header: 'Benchmark',
    key: 'spread'
  }
])

export type DolEur = typeof dolEur.infer

export const dolEurValidate = (attempt?: DolEur) =>
  dolEur(attempt) instanceof Error ? resetObject(dolEur.infer) : attempt
