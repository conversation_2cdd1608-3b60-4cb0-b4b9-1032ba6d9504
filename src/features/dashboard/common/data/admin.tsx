import { SettingOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const admin: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'admin-calcularMargen',
          title: <FormattedMessage id='calcularMargen' />,
          type: 'item',
          url: '/admin/calcular-margen/calcular-margen'
        },
        {
          id: 'admin-ajustes',
          title: <FormattedMessage id='ajustes' />,
          type: 'item',
          url: '/admin/ajustes'
        },
        {
          id: 'admin-ajustederivados',
          title: <FormattedMessage id='ajusteDerivado' />,
          type: 'item',
          url: '/admin/ajustes-derivados'
        },
        {
          id: 'admin-configuracionManual',
          title: <FormattedMessage id='configuracionManual' />,
          type: 'item',
          url: '/admin/config-manual/ajustes-trading'
        },
        {
          id: 'admin-configuracion',
          title: <FormattedMessage id='configuracion' />,
          type: 'item',
          url: '/admin/configuracion/filtros/trade-group'
        },
        {
          id: 'admin-inputManual',
          title: <FormattedMessage id='inputManual' />,
          type: 'item',
          url: '/admin/input-manual/otros-saldos/banco-espana'
        },
        {
          id: 'admin-cargafichero',
          title: <FormattedMessage id='cargaFichero' />,
          type: 'item',
          url: '/admin/carga-ficheros'
        }
      ],
      icon: SettingOutlined,
      id: 'admin',
      title: <FormattedMessage id='admin' />,
      type: 'collapse'
    }
  ],
  icon: SettingOutlined,
  id: 'group-admin',
  title: <FormattedMessage id='admin' />,
  type: 'group'
}
