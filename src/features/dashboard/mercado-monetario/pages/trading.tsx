import { Reload } from '@d/common/components/Reload'
import { TradingRepository } from '@d/common/repository/trading-repository'
import { TradingTemplate } from '@d/common/templates/trading'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getMercadoMonetarioTrading } = container.get(TradingRepository)

  const {
    data: tradingData,
    error: tradingError,
    isError: isTradingError,
    isFetching: isTradingPending,
    refetch: refetchTrading
  } = useQuery({
    queryFn: () => getMercadoMonetarioTrading('12'),
    queryKey: ['getTradingRF']
  })

  useEffect(() => {
    void refetchTrading()
  }, [day])

  return (
    <Fetching
      errorMessage={tradingError?.message}
      isError={isTradingError}
      isFetching={isTradingPending}
    >
      <Reload onClick={() => refetchTrading()} />
      <TradingTemplate resumen={tradingData ?? []} />
    </Fetching>
  )
}
