import type { ActivoPasivo } from '@d/common/models/activo-pasivo'
import { ActivoPasivoRepository } from '@d/common/repository/activo-pasivo-repository'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'
import { useCallback, useEffect, useRef, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const margenController = () => {
  const { getActivo, getPasivo, showTradeGroupDivisa } = container.get(
    ActivoPasivoRepository
  )

  const day = calendarStore.actualDay.use()

  const balanceRef = useRef('Fuera de Balance DDA')

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDialogActivo1, setOpenDialogActivo1] = useState(false)
  const [openDialogActivo2, setOpenDialogActivo2] = useState({
    balance: balanceRef.current,
    open: false,
    product: '',
    tradeGroup: ''
  })
  const [openDialogPasivo1, setOpenDialogPasivo1] = useState(false)
  const [openDialogPasivo2, setOpenDialogPasivo2] = useState({
    balance: balanceRef.current,
    open: false,
    product: '',
    tradeGroup: ''
  })

  const [dialog1Data, setDialog1Data] = useState<ActivoPasivo[]>([])
  const {
    data: activoData,
    error: activoError,
    isError: isActivoError,
    isFetching: isActivoPending,
    refetch: refetchActivo
  } = useQuery({
    queryFn: () => getActivo('13'),
    queryKey: ['getActivoMercadoDivisas']
  })

  const {
    data: pasivoData,
    error: pasivoError,
    isError: isPasivoError,
    isFetching: isPasivoPending,
    refetch: refetchPasivo
  } = useQuery({
    queryFn: () => getPasivo('13'),
    queryKey: ['getPasivoMercadoDivisas']
  })

  const onRowActivoPasivo1 = useCallback(
    async (
      activoPasivoData: ActivoPasivo[],
      rowIndex: number,
      activoPasivo: boolean
    ) => {
      const activoPasivoRow = nth(rowIndex, activoPasivoData)
      const balance = activoPasivo
        ? 'Fuera de Balance DDA'
        : (activoPasivoRow?.balance ?? 'Fuera de Balance DDA')
      balanceRef.current = balance
      setLoadingDialog(true)
      setDialog1Data(
        await showTradeGroupDivisa(
          balance,
          '13',
          activoPasivoRow?.epigrafeID?.toString() ?? ''
        )
      )

      setLoadingDialog(false)
      activoPasivo ? setOpenDialogActivo1(true) : setOpenDialogPasivo1(true)
    },
    []
  )

  const onRowActivoPasivo2 = (rowIndex: number, activoPasivo: boolean) => {
    const activoPasivoRow = nth(rowIndex, dialog1Data)
    activoPasivo
      ? setOpenDialogActivo2({
          balance: balanceRef.current,
          open: true,
          product: activoPasivoRow?.producto ?? '',
          tradeGroup: activoPasivoRow?.tradeGroup ?? ''
        })
      : setOpenDialogPasivo2({
          balance: balanceRef.current,
          open: true,
          product: activoPasivoRow?.producto ?? '',
          tradeGroup: activoPasivoRow?.tradeGroup ?? ''
        })
  }

  const refetch = useCallback(() => {
    void refetchPasivo()
    void refetchActivo()
  }, [])

  useEffect(() => {
    refetch()
  }, [day])

  return {
    activoData,
    activoError,
    dialog1Data,
    isActivoError,
    isActivoPending,
    isPasivoError,
    isPasivoPending,
    loadingDialog,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    pasivoError,
    refetch,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  }
}
