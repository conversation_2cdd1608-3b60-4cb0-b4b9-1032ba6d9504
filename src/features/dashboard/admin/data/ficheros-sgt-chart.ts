import type { ApexOptions } from 'apexcharts'

import { numberFormat } from '~/utils/format-number'

export const ficherosSgtOpt: ApexOptions = {
  chart: { id: 'apex-nominal' },
  dataLabels: { enabled: false },
  plotOptions: { bar: { borderRadius: 4 } },
  xaxis: {
    categories: [''],
    labels: { rotate: -90, style: { fontSize: '12px' } }
  },
  yaxis: {
    labels: {
      formatter: value => `${numberFormat(value / 1000000000, 3)}G`
    }
  }
}
