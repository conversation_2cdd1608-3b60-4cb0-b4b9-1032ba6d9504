import { MainCard } from '@c/components/MainCard'
import {
  CardMedia,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Stack,
  Typography,
  useMediaQuery
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { type ChangeEvent, useContext, useState } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { menuStore } from '@/common/store/menu-store'
import defaultLayout from '~/assets/default.svg'
import miniMenu from '~/assets/mini-menu.svg'
import { MenuOrientation, ThemeDirection } from '~/types/resources'

export const ThemeLayout = () => {
  const theme = useTheme()
  const downLG = useMediaQuery(theme.breakpoints.down('lg'))

  const { drawerOpen } = menuStore.use()

  const {
    menuOrientation,
    miniDrawer,
    onChangeDirection,
    onChangeMiniDrawer,
    themeDirection
  } = useContext(ResourceContext)

  const initialThemeCalc = useCallback(() => {
    if (miniDrawer) return 'mini'
    if (themeDirection === ThemeDirection.RTL) return 'rtl'
    return 'default'
  }, [miniDrawer, themeDirection])

  const [value, setValue] = useState<string | null>(initialThemeCalc())

  const handleRadioChange = (event: ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value)
    if (event.target.value === 'default') {
      if (themeDirection === ThemeDirection.RTL)
        onChangeDirection(ThemeDirection.LTR)
      if (miniDrawer) onChangeMiniDrawer(false)
      if (!drawerOpen) menuStore.openDrawer(true)
    }
    if (event.target.value === 'mini') {
      onChangeMiniDrawer(true)
      if (drawerOpen) menuStore.openDrawer(false)
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
    if (event.target.value === ThemeDirection.RTL)
      onChangeDirection(ThemeDirection.RTL)
  }

  return (
    <RadioGroup
      aria-label='payment-card'
      name='payment-card'
      onChange={handleRadioChange}
      row
      value={value}
    >
      <Grid container spacing={1.75} sx={{ ml: 0 }}>
        <Grid item>
          <FormControlLabel
            control={<Radio sx={{ display: 'none' }} value='default' />}
            label={
              <MainCard
                border={false}
                content={false}
                sx={{
                  bgcolor:
                    value === 'default'
                      ? 'primary.lighter'
                      : 'secondary.lighter',
                  p: 1
                }}
                {...(value === 'default' && {
                  boxShadow: true,
                  shadow: theme.customShadows.primary
                })}
              >
                <Stack alignItems='center' spacing={1.25}>
                  <CardMedia
                    alt='Vertical'
                    component='img'
                    src={defaultLayout}
                    sx={{ borderRadius: 1, height: 64, width: 64 }}
                  />
                  <Typography variant='caption'>Expandido</Typography>
                </Stack>
              </MainCard>
            }
            sx={{
              '& .MuiFormControlLabel-label': { flex: 1 },
              display: 'flex'
            }}
            value='default'
          />
        </Grid>

        {menuOrientation === MenuOrientation.VERTICAL || downLG ? (
          <Grid item>
            <FormControlLabel
              control={<Radio sx={{ display: 'none' }} value='mini' />}
              label={
                <MainCard
                  border={false}
                  content={false}
                  sx={{
                    bgcolor:
                      value === 'mini'
                        ? 'primary.lighter'
                        : 'secondary.lighter',
                    p: 1
                  }}
                  {...(value === 'mini' && {
                    boxShadow: true,
                    shadow: theme.customShadows.primary
                  })}
                >
                  <Stack alignItems='center' spacing={1.25}>
                    <CardMedia
                      alt='Vertical'
                      component='img'
                      src={miniMenu}
                      sx={{ borderRadius: 1, height: 64, width: 64 }}
                    />
                    <Typography variant='caption'>Minimizado</Typography>
                  </Stack>
                </MainCard>
              }
              sx={{
                '& .MuiFormControlLabel-label': { flex: 1 },
                display: 'flex'
              }}
              value='mini'
            />
          </Grid>
        ) : null}

        {/*

        <Grid item>
          <FormControlLabel
            value="rtl"
            control={<Radio value="rtl" sx={{ display: 'none' }} />}
            sx={{ display: 'flex', '& .MuiFormControlLabel-label': { flex: 1 } }}
            label={
              <MainCard
                content={false}
                sx={{ bgcolor: value === ThemeDirection.RTL ? 'primary.lighter' : 'secondary.lighter', p: 1 }}
                border={false}
                {...(value === ThemeDirection.RTL && { boxShadow: true, shadow: theme.customShadows.primary })}
              >
                <Stack spacing={1.25} alignItems="center">
                  <CardMedia component="img" src={rtlLayout} alt="Vertical" sx={{ borderRadius: 1, width: 64, height: 64 }} />
                  <Typography variant="caption">RTL</Typography>
                </Stack>
              </MainCard>
            }
          />
        </Grid>
        */}
      </Grid>
    </RadioGroup>
  )
}
