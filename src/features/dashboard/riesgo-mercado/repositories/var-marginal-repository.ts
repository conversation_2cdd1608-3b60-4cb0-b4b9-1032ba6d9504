import type { Bce } from '@d/riesgo-mercado/models/bce'
import type { <PERSON><PERSON> } from '@d/riesgo-mercado/models/cartera'
import type { VarMarginal } from '@d/riesgo-mercado/models/var-marginal'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

type Area =
  | 'COAP'
  | 'Derivados'
  | 'Divisa'
  | 'Mercado Monetario'
  | 'Renta Fija'
  | 'TOTAL'

@injectable()
export class VarMarginalRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/varmarginal'

  readonly getBce = (tradeGroup: string, area: Area) =>
    this.api.client
      .get<Bce[]>(
        `${this.route}/bce/${this.attachDate()}/${tradeGroup}/${area}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getBceNeg = (tradeGroup: string, area: Area) =>
    this.api.client
      .get<Bce[]>(
        `${this.route}/bceneg/${this.attachDate()}/${tradeGroup}/${area}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getCarteras = (cartera: 'Inversion' | 'Negociacion') =>
    this.api.client
      .get<Cartera[]>(`${this.route}/${this.attachDate()}/${cartera}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getVarMarginal = (
    cartera: 'Inversión' | 'Negociación' = 'Inversión',
    area: Area = 'TOTAL',
    producto: 'Marginal' | 'Producto' = 'Marginal'
  ) =>
    this.api.client
      .get<VarMarginal[]>(
        `${this.route}/${this.attachDate()}/${cartera}/${area}/${producto}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
