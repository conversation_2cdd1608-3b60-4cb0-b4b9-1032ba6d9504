import { DownOutlined } from '@ant-design/icons'
import { AppChart } from '@d/common/components/Chart'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import {
  anSensilChartOpts,
  anSensilDurationChartOpts
} from '@d/riesgo-mercado/data/an-sensil-chart'
import {
  sensitivityArea,
  sensitivityAreaIntMetadata,
  sensitivityAreaProdMetadata
} from '@d/riesgo-mercado/models/sensitivity-area'
import {
  sensitivityDuration,
  sensitivityDurationMetadata
} from '@d/riesgo-mercado/models/sensitivity-duration'
import { AnalisisSensibilidadesRepository } from '@d/riesgo-mercado/repositories/analisis-sensibilidades'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Container,
  Grid,
  Paper
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useRef } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const {
    getSensitivityDuration,
    getSensitivityIntArea,
    getSensitivityProdArea
  } = container.get(AnalisisSensibilidadesRepository)

  const invSeries = useRef<ApexAxisChartSeries>([])
  const invOptions = useRef(anSensilChartOpts)
  const negSeries = useRef<ApexAxisChartSeries>([])
  const negOptions = useRef(anSensilChartOpts)
  const durationSeries = useRef<ApexAxisChartSeries>([])
  const durationOptions = useRef(anSensilDurationChartOpts)

  const {
    data: sentivityAreaInv,
    error: sentivityAreaInvError,
    isError: isSentivityAreaInvError,
    isFetching: isSentivityAreaInvPending,
    refetch: refetchSentivityAreaInv
  } = useQuery({
    queryFn: () => getSensitivityIntArea(),
    queryKey: ['getSensitivityIntArea']
  })

  const {
    data: sentivityAreaNeg,
    error: sentivityAreaNegError,
    isError: isSentivityAreaNegError,
    isFetching: isSentivityAreaNegPending,
    refetch: refetchSentivityAreaNeg
  } = useQuery({
    queryFn: () => getSensitivityIntArea('Neg'),
    queryKey: ['getSensitivityIntAreaNeg']
  })

  const {
    data: sentivityAreaProdInv,
    error: sentivityAreaProdInvError,
    isError: isSentivityAreaProdInvError,
    isFetching: isSentivityAreaProdInvPending,
    refetch: refetchSentivityAreaProdInv
  } = useQuery({
    queryFn: () =>
      getSensitivityProdArea().then(data => {
        invOptions.current = {
          ...invOptions.current,
          xaxis: {
            ...invOptions.current.xaxis,
            categories: data.map(el => el.producto)
          }
        }
        invSeries.current = [{ data: data.map(el => el.parDelta ?? 0) }]
        return data
      }),
    queryKey: ['getSensitivityProdAreaInv']
  })

  const {
    data: sentivityAreaProdNeg,
    error: sentivityAreaProdNegError,
    isError: isSentivityAreaProdNegError,
    isFetching: isSentivityAreaProdNegPending,
    refetch: refetchSentivityAreaProdNeg
  } = useQuery({
    queryFn: () =>
      getSensitivityProdArea('Neg').then(data => {
        negOptions.current = {
          ...negOptions.current,
          xaxis: {
            ...negOptions.current.xaxis,
            categories: data.map(el => el.producto)
          }
        }
        negSeries.current = [{ data: data.map(el => el.parDelta ?? 0) }]
        return data
      }),
    queryKey: ['getSensitivityProdAreaNeg']
  })

  const {
    data: durationArea,
    error: durationAreaError,
    isError: isDurationAreaError,
    isFetching: isDurationAreaPending,
    refetch: refetchDurationArea
  } = useQuery({
    queryFn: () =>
      getSensitivityDuration().then(data => {
        durationOptions.current = {
          ...durationOptions.current,
          xaxis: {
            ...durationOptions.current.xaxis,
            categories: data.map(el => el.titulo)
          }
        }
        durationSeries.current = [
          { data: data.map(el => el.sensitivity ?? 0), name: 'Sensitivity' },
          {
            data: data.map(el => el.modifiedDuration ?? 0),
            name: 'Modified Duration'
          }
        ]
        return data
      }),
    queryKey: ['getSensitivityDuration']
  })

  const refetch = () => {
    void refetchSentivityAreaInv()
    void refetchSentivityAreaNeg()
    void refetchSentivityAreaProdInv()
    void refetchSentivityAreaProdNeg()
    void refetchDurationArea()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Accordion defaultExpanded>
              <AccordionSummary
                aria-controls='panel1-content'
                expandIcon={<DownOutlined />}
                id='panel1-header'
              >
                <h3>Cartera Disponible</h3>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={4}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3> Sensibilidad Tipos Interés</h3>
                    </div>
                    <Fetching
                      errorMessage={sentivityAreaInvError?.message}
                      isError={isSentivityAreaInvError}
                      isFetching={isSentivityAreaInvPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={sentivityAreaInv ?? []}
                          entries={sensitivityArea}
                          metadata={sensitivityAreaIntMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={4}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3> Sensibilidad Productos</h3>
                    </div>
                    <Fetching
                      errorMessage={sentivityAreaProdInvError?.message}
                      isError={isSentivityAreaProdInvError}
                      isFetching={isSentivityAreaProdInvPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={sentivityAreaProdInv ?? []}
                          entries={sensitivityArea}
                          metadata={sensitivityAreaProdMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={4}>
                    <Box sx={{ m: 7 }}></Box>
                    <Paper elevation={1} sx={paperStyle}>
                      <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                        <Fetching
                          errorMessage={sentivityAreaProdInvError?.message}
                          isError={isSentivityAreaProdInvError}
                          isFetching={isSentivityAreaProdInvPending}
                        >
                          <AppChart
                            height={305}
                            options={invOptions.current}
                            series={invSeries.current}
                            type='bar'
                          />
                        </Fetching>
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3> Duración Áreas</h3>
                    </div>
                    <Fetching
                      errorMessage={durationAreaError?.message}
                      isError={isDurationAreaError}
                      isFetching={isDurationAreaPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={durationArea ?? []}
                          entries={sensitivityDuration}
                          metadata={sensitivityDurationMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ m: 7 }}></Box>
                    <Paper elevation={1} sx={paperStyle}>
                      <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                        <Fetching
                          errorMessage={durationAreaError?.message}
                          isError={isDurationAreaError}
                          isFetching={isDurationAreaPending}
                        >
                          <AppChart
                            height={305}
                            options={durationOptions.current}
                            series={durationSeries.current}
                            type='bar'
                          />
                        </Fetching>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
            <div style={{ marginTop: '20px', width: '100%' }}></div>
            <Accordion>
              <AccordionSummary
                aria-controls='panel1-content'
                expandIcon={<DownOutlined />}
                id='panel1-header'
              >
                <h3>Cartera Negociación</h3>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={4}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3> Sensibilidad Tipos Interés</h3>
                    </div>
                    <Fetching
                      errorMessage={sentivityAreaNegError?.message}
                      isError={isSentivityAreaNegError}
                      isFetching={isSentivityAreaNegPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={sentivityAreaNeg ?? []}
                          entries={sensitivityArea}
                          metadata={sensitivityAreaIntMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={4}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3> Sensibilidad Productos</h3>
                    </div>
                    <Fetching
                      errorMessage={sentivityAreaProdNegError?.message}
                      isError={isSentivityAreaProdNegError}
                      isFetching={isSentivityAreaProdNegPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={sentivityAreaProdNeg ?? []}
                          entries={sensitivityArea}
                          metadata={sensitivityAreaProdMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={4}>
                    <Box sx={{ m: 7 }}></Box>
                    <Paper elevation={1} sx={paperStyle}>
                      <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                        <Fetching
                          errorMessage={sentivityAreaProdNegError?.message}
                          isError={isSentivityAreaProdNegError}
                          isFetching={isSentivityAreaProdNegPending}
                        >
                          <AppChart
                            height={305}
                            options={negOptions.current}
                            series={negSeries.current}
                            type='bar'
                          />
                        </Fetching>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
