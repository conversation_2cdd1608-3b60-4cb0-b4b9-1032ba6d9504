import type { ActivoPasivo } from '@d/common/models/activo-pasivo'
import { Container, Grid, Paper } from '@mui/material'
import type { FC } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { paperStyle } from '~/resources/config/paper'

import { TableMargenFinanciero } from './margen-financiero-components'

export const MargenFinanciero: FC<{
  activo: ActivoPasivo[]
  onActivoClick?: (index: number) => void
  onPasivoClick?: (index: number) => void
  pasivo: ActivoPasivo[]
}> = ({ activo, onActivoClick, onPasivoClick, pasivo }) => (
  <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TitleSubtitle title='Activo' />
        <Paper elevation={10} sx={paperStyle}>
          <TableMargenFinanciero data={activo} onRowClick={onActivoClick} />
        </Paper>
      </Grid>
      <Grid item xs={12}>
        <TitleSubtitle title='Pasivo' />
        <Paper elevation={10} sx={paperStyle}>
          <TableMargenFinanciero data={pasivo} onRowClick={onPasivoClick} />
        </Paper>
      </Grid>
    </Grid>
  </Container>
)
