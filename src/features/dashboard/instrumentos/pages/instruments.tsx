import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import Paper from '@mui/material/Paper'
import Select from '@mui/material/Select'
import { type } from 'arktype'

//import { calendarStore } from '@/common/store/calendar-store'

const paperStyle = {
  alignItems: 'center',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

export default () => (
  // const day = calendarStore.actualDay.use()

  //const [menuId, setMenuId] = useState(0)

  /* const handleChange = (event: SelectChangeEvent) => {
    console.log(event.target.value as string)
  }*/
  <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Paper elevation={10} sx={paperStyle}>
          <GenericTable
            data={[]}
            entries={type({ test: 'string' })}
            metadata={new Set([{ header: 'Test', key: 'test' }])}
          />
        </Paper>
      </Grid>

      <Grid item xs={12}>
        <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
          <Box sx={{ minWidth: 150 }}>
            <FormControl fullWidth>
              <InputLabel id='simple-select-label'>Menu</InputLabel>
              <Select
                id='simple-select'
                label='Menu'
                labelId='simple-select-label'
                value={'0'}
              >
                <MenuItem value={0}>TIR ACT / 365</MenuItem>
                <MenuItem value={1}>Metodología</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </div>
        <br></br>
        <Paper elevation={10} sx={paperStyle}>
          <img
            alt='instrument chart'
            height='32'
            src='https://www.w3schools.com/images/lamp.jpg'
            width='32'
          />
        </Paper>
      </Grid>
    </Grid>
  </Container>
)
