import { FileExcelOutlined } from '@ant-design/icons'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import {
  spotAdaptiv,
  spotAdaptivMetadata
} from '@d/mercado-divisas/models/spot-adaptiv'
import { MercadoDivisasRepository } from '@d/mercado-divisas/repository/mercado-divisas-repository'
import { Button, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { exportToCSV } from '~/utils/csv'

export default () => {
  const { getTotal } = container.get(MercadoDivisasRepository)

  const day = calendarStore.actualDay.use()

  const {
    data: getTotalData,
    error: getTotalError,
    isError: isGetTotalError,
    isFetching: isGetTotalPending,
    refetch: refetchGetTotal
  } = useQuery({
    queryFn: getTotal,
    queryKey: ['getTotal']
  })

  useEffect(() => {
    void refetchGetTotal()
  }, [day])

  return (
    <Fetching
      errorMessage={getTotalError?.message}
      fullHeight
      isError={isGetTotalError}
      isFetching={isGetTotalPending}
    >
      <Reload onClick={() => refetchGetTotal()} />
      <Paper elevation={10} sx={paperStyle}>
        <GenericTable
          data={getTotalData ?? []}
          entries={spotAdaptiv}
          metadata={spotAdaptivMetadata}
        />
        <Grid container spacing={1}>
          <Grid item sx={{ display: 'flex', justifyContent: 'start' }} xs={3}>
            <Button
              onClick={() => exportToCSV(getTotalData ?? [], 'users.csv', ';')}
              startIcon={<FileExcelOutlined />}
            />
          </Grid>
        </Grid>
      </Paper>
    </Fetching>
  )
}
