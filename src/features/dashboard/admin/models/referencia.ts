/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const referencia = type({
  comentario: 'string',
  fecha: 'string',
  modo: 'string',
  spread: 'number',
  tasa: 'number'
})

export type Referencia = typeof referencia.infer

export const referenciaMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Modo',
    key: 'modo'
  },
  {
    header: 'Tasa',
    key: 'tasa'
  },
  {
    header: 'Spread',
    key: 'spread'
  },
  {
    header: 'Comentario',
    key: 'comentario'
  }
])
