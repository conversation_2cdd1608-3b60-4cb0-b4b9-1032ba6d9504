import type { Trading } from '@d/common/models/trading'
import type { DolEur } from '@d/mercado-divisas/models/doleur'
import { MesaContado } from '@d/mercado-divisas/models/mesa-contado'
import { PlFechas } from '@d/mercado-divisas/models/pl-fechas'
import { SpotAdaptiv } from '@d/mercado-divisas/models/spot-adaptiv'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class MercadoDivisasRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/mercadodivisas'

  readonly getDolar = (producto: string, tradeGroup: string, balance: string) =>
    this.api.client
      .get<DolEur[]>(
        `${this.route}/showAllDepositosDivisas/${this.attachDate()}/${producto}/${tradeGroup}/${balance}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getEuro = (producto: string, tradeGroup: string, balance: string) =>
    this.api.client
      .get<DolEur[]>(
        `${this.route}/showAllDepositosDivisasEuros/${this.attachDate()}/${producto}/${tradeGroup}/${balance}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMesaContado = () =>
    this.api.client
      .get<MesaContado[]>(
        `${this.route}/PLFXSPOTADAPTIVContado/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMesaPlazo = () =>
    this.api.client
      .get<SpotAdaptiv[]>(
        `${this.route}/PLFXSPOTADAPTIVPlazo/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getPlFechas = (startDate: string, endDate: string) =>
    this.api.client
      .get<PlFechas[]>(
        `${this.route}/informeFXPLTOTALESFechas/${startDate}/${endDate}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getPlFechasHistorico = () =>
    this.api.client
      .get<PlFechas[]>(`${this.route}/informeFXPLTOTALESHist`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getPlMonedaExtranjera = () =>
    this.api.client
      .get<Trading[]>(`${this.route}/PLMonedaExtranjera/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getTotal = () =>
    this.api.client
      .get<SpotAdaptiv[]>(
        `${this.route}/PLFXSPOTADAPTIVTotal/${this.attachDate()}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
