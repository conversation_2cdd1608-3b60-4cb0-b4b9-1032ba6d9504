/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const sgtGroups = type({
  amortizacionDia: 'number',
  cuponCobradoDia: 'number',
  cuponCorrido: 'number',
  cuponCorrido_Last: 'number',
  cuponPeriodificado: 'number',
  cuponPeriodificado_Last: 'number',
  efectivoCritico: 'number',
  efectivoCritico_Last: 'number',
  efectivoHistor: 'number',
  efectivoHistor_Last: 'number',
  fecha: 'string',
  fechaVto: 'string',
  iD: 'number',
  intAcc: 'number',
  nominal: 'number',
  nominal_Last: 'number',
  precioCierre: 'number',
  referencia: 'string',
  resultadosVentasDia: 'number',
  tIR: 'number',
  tradeGroup: 'string',
  valoracionRepercutida: 'number',
  valoracionRepercutida_Last: 'number'
})

export const sgtGroupsMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Referencia',
    key: 'referencia'
  },
  {
    header: 'Vencimiento',
    key: 'fechaVto'
  },
  {
    header: 'Precio Cierre',
    key: 'precioCierre'
  },
  {
    header: 'TIR',
    key: 'tIR'
  },
  {
    header: 'Nominal',
    key: 'nominal'
  },
  {
    header: 'Nominal Last',
    key: 'nominal_Last'
  },
  {
    header: 'Efectivo Histor',
    key: 'efectivoHistor'
  },
  {
    header: 'Efectivo Crítico',
    key: 'efectivoCritico'
  },
  {
    header: 'Cupon Corrido',
    key: 'cuponCorrido'
  },
  {
    header: 'Valoracion Repercutida',
    key: 'valoracionRepercutida'
  },
  {
    header: 'Int Acc',
    key: 'intAcc'
  },
  {
    header: 'Ventas Dia',
    key: 'resultadosVentasDia'
  }
])

export type SGTGroups = typeof sgtGroups.infer

export const sgtGroupsValidate = (attempt: SGTGroups) =>
  sgtGroups(attempt) instanceof type.errors ? resetObject(attempt) : attempt
