/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const logsCalcMargen = type({
  fechaHora: 'string',
  lOGID: 'number',
  logText: 'string',
  nombreUsuario: 'string',
  severityLevel: 'number?'
})

export type LogsCalcMargen = typeof logsCalcMargen.infer

export const logsCalcMargenMetadata = new Set([
  {
    header: 'Logs',
    key: 'lOGID'
  },
  {
    header: '<PERSON><PERSON> hora',
    key: 'fechaHora'
  },
  {
    header: 'Log text',
    key: 'logText'
  },
  {
    header: 'Severidad',
    key: 'severityLevel'
  },
  {
    header: 'Usuario',
    key: 'nombreUsuario'
  }
])
