/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const fondoArcano = type({
  fecha: 'string',
  nParticipaciones: 'number',
  pL: 'number',
  valorLiquidativo: 'number'
})

export const fondoArcanoMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Nº Títulos',
    key: 'nParticipaciones'
  },
  {
    header: 'Valor Liquidativo',
    key: 'valorLiquidativo'
  },
  {
    header: 'PL',
    key: 'pL'
  }
])

export type FondoArcano = typeof fondoArcano.infer

export const fondoArcanoDefault: FondoArcano = {
  fecha: '',
  nParticipaciones: 0,
  pL: 0,
  valorLiquidativo: 0
}

export const fondoArcanoValidate = (attempt?: FondoArcano) =>
  fondoArcano(attempt) instanceof type.errors ? resetObject(attempt) : attempt
