import { App<PERSON>hart } from '@d/common/components/Chart'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import { expoBarChartOps } from '@d/riesgo-mercado/data/expo-bar-chart'
import { expoPieChartOps } from '@d/riesgo-mercado/data/expo-pie-chart'
import {
  type ExposicionGroups,
  exposicionGroups,
  exposicionGroupsMetadata
} from '@d/riesgo-mercado/models/exposicion-groups'
import {
  type ExposicionProducts,
  exposicionProducts,
  exposicionProductsMetadata
} from '@d/riesgo-mercado/models/exposicion-products'
import { ExposicionRepository } from '@d/riesgo-mercado/repositories/exposicion-repository'
import { Box, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useRef } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getGroups, getProducts } = container.get(ExposicionRepository)

  const groupsInvSeries = useRef<number[]>([])
  const groupsInvOptions = useRef(expoPieChartOps)
  const groupsNegSeries = useRef<number[]>([])
  const groupsNegOptions = useRef(expoPieChartOps)
  const prodInvSeries = useRef<ApexAxisChartSeries>([])
  const prodInvOptions = useRef(expoBarChartOps)
  const prodNegSeries = useRef<ApexAxisChartSeries>([])
  const prodNegOptions = useRef(expoBarChartOps)

  const seriesGroupInvGen = (data: ExposicionGroups[]) => {
    groupsInvOptions.current = {
      ...expoPieChartOps,
      labels: data.map(item => item.titulo)
    }
    groupsInvSeries.current = data.map(item => Math.abs(item.pV) / 10000)
  }

  const seriesGroupNegGen = (data: ExposicionGroups[]) => {
    groupsNegOptions.current = {
      ...expoPieChartOps,
      labels: data.map(item => item.titulo)
    }
    groupsNegSeries.current = data.map(item => Math.abs(item.pV) / 100000)
  }

  const seriesProdInvGen = (data: ExposicionProducts[]) => {
    prodInvOptions.current = {
      ...expoBarChartOps,
      xaxis: { categories: data.map(item => item.product) }
    }
    prodInvSeries.current = [
      { data: data.map(item => item.delta) },
      { data: data.map(item => item.pV) }
    ]
  }

  const seriesProdNegGen = (data: ExposicionProducts[]) => {
    prodNegOptions.current = {
      ...expoBarChartOps,
      xaxis: { categories: data.map(item => item.product) }
    }
    prodNegSeries.current = [
      { data: data.map(item => item.delta) },
      { data: data.map(item => item.pV) }
    ]
  }

  const {
    data: productsInvData,
    error: productsInvError,
    isError: isProductsInvError,
    isFetching: isproductsInvPending,
    refetch: refetchProductsInv
  } = useQuery({
    queryFn: () =>
      getProducts().then(data => {
        seriesProdInvGen(data)
        return data
      }),
    queryKey: ['getProductsInvExposicion']
  })

  const {
    data: productsNegData,
    error: productsNegError,
    isError: isProductsNegError,
    isFetching: isproductsNegPending,
    refetch: refetchProductsNeg
  } = useQuery({
    queryFn: () =>
      getProducts('Neg').then(data => {
        seriesProdNegGen(data)
        return data
      }),
    queryKey: ['getProductsNegExposicion']
  })

  const {
    data: groupInvData,
    error: groupInvError,
    isError: isGroupInvError,
    isFetching: isgroupInvPending,
    refetch: refetchGroupInv
  } = useQuery({
    queryFn: () =>
      getGroups().then(data => {
        seriesGroupInvGen(data)
        return data
      }),
    queryKey: ['getGroupsInvExposicion']
  })

  const {
    data: groupNegData,
    error: groupNegError,
    isError: isGroupNegError,
    isFetching: isgroupNegPending,
    refetch: refetchGroupNeg
  } = useQuery({
    queryFn: () =>
      getGroups('Neg').then(data => {
        seriesGroupNegGen(data)
        return data
      }),
    queryKey: ['getGroupsNegExposicion']
  })

  const refetch = () => {
    void refetchGroupInv()
    void refetchGroupNeg()
    void refetchProductsInv()
    void refetchProductsNeg()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera disponible / Exposición áreas</h3>
            </div>
            <Fetching
              errorMessage={groupInvError?.message}
              isError={isGroupInvError}
              isFetching={isgroupInvPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={groupInvData ?? []}
                  entries={exposicionGroups}
                  metadata={exposicionGroupsMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Áreas</h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent', width: '100%' }}>
                <Fetching
                  errorMessage={groupInvError?.message}
                  isError={isGroupInvError}
                  isFetching={isgroupInvPending}
                >
                  <AppChart
                    height={350}
                    options={groupsInvOptions.current}
                    series={groupsInvSeries.current}
                    type='donut'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera disponible / Exposición productos</h3>
            </div>
            <Fetching
              errorMessage={productsInvError?.message}
              isError={isProductsInvError}
              isFetching={isproductsInvPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={productsInvData ?? []}
                  entries={exposicionProducts}
                  metadata={exposicionProductsMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>

          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> </h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <Fetching
                errorMessage={productsInvError?.message}
                isError={isProductsInvError}
                isFetching={isproductsInvPending}
              >
                <AppChart
                  height={350}
                  options={prodInvOptions.current}
                  series={prodInvSeries.current}
                  type='bar'
                />
              </Fetching>
            </Paper>
          </Grid>
          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera Negociación / Exposición áreas</h3>
            </div>
            <Fetching
              errorMessage={groupNegError?.message}
              isError={isGroupNegError}
              isFetching={isgroupNegPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={groupNegData ?? []}
                  entries={exposicionGroups}
                  metadata={exposicionGroupsMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>
          <Grid item xs={4}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3>Áreas</h3>
            </div>
            <Paper elevation={10} sx={paperStyle}>
              <Box id='chart' sx={{ bgcolor: 'transparent', width: '100%' }}>
                <Fetching
                  errorMessage={groupNegError?.message}
                  isError={isGroupNegError}
                  isFetching={isgroupNegPending}
                >
                  <AppChart
                    height={350}
                    options={groupsNegOptions.current}
                    series={groupsNegSeries.current}
                    type='donut'
                  />
                </Fetching>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={8}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <h3> Cartera Negociación / Exposición productos</h3>
            </div>
            <Fetching
              errorMessage={productsNegError?.message}
              isError={isProductsNegError}
              isFetching={isproductsNegPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={productsNegData ?? []}
                  entries={exposicionProducts}
                  metadata={exposicionProductsMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </Grid>

          <Grid item xs={4}>
            <Paper elevation={10} sx={paperStyle}>
              <Fetching
                errorMessage={productsNegError?.message}
                isError={isProductsNegError}
                isFetching={isproductsNegPending}
              >
                <AppChart
                  height={350}
                  options={prodNegOptions.current}
                  series={prodNegSeries.current}
                  type='bar'
                />
              </Fetching>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
