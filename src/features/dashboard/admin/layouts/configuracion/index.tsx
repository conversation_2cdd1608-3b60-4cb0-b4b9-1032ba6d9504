/* eslint-disable sonarjs/void-use */
import { FiltroEstadoRepository } from '@d/admin/repositories/filtro-estado-repository'
import { TradeGroupRepository } from '@d/admin/repositories/tradegroup-repository'
import { configStore } from '@d/admin/store/config-store'
import { Paper } from '@mui/material'
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

const route = '/admin/configuracion'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const { getAreas, getProductos } = container.get(TradeGroupRepository)

  const { getFiltroEstado } = container.get(FiltroEstadoRepository)

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/filtros/trade-group`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/organizacion/areas-negocio`)
        setMenuId(1)
        break
      }
    }
  }

  const fetchCommonData = async () => {
    configStore.setAreas(await getAreas())
    configStore.setActivoPasivo(await getFiltroEstado())
    configStore.setProductos(await getProductos())
  }

  useEffect(() => {
    void fetchCommonData()
    const path = location?.pathname ?? ''
    if (path.includes('filtros/trade-group')) setMenuId(0)
    if (path.includes('organizacion/areas-negocio')) setMenuId(1)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Filtros</MenuItem>
                  <MenuItem value={1}>Organización</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Paper elevation={1} sx={paperStyle}>
            <Outlet />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}
