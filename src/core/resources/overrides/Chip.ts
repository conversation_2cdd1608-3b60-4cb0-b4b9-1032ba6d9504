import type { Theme } from '@mui/material/styles'

import { getColors } from '~/resources/helpers/get-colors'
import type { ExtendedStyleProps } from '~/types/extended'

const getColor = ({ color, theme }: ExtendedStyleProps) => {
  const colors = getColors(theme, color)
  const { dark } = colors

  return {
    '&.Mui-focusVisible': {
      outline: `2px solid ${dark}`,
      outlineOffset: 2
    }
  }
}
const getColorStyle = ({ color, theme }: ExtendedStyleProps) => {
  const colors = getColors(theme, color)
  const { light, lighter, main } = colors

  return {
    '& .MuiChip-deleteIcon': {
      '&:hover': { color: light },
      color: main
    },
    backgroundColor: lighter,
    borderColor: light,
    color: main
  }
}

export const Chip = (theme: Theme) => {
  const defaultLightChip = getColorStyle({ color: 'secondary', theme })
  return {
    MuiChip: {
      styleOverrides: {
        combined: {
          border: '1px solid',
          ...defaultLightChip,
          '&.MuiChip-combinedError': getColorStyle({ color: 'error', theme }),
          '&.MuiChip-combinedInfo': getColorStyle({ color: 'info', theme }),
          '&.MuiChip-combinedPrimary': getColorStyle({
            color: 'primary',
            theme
          }),
          '&.MuiChip-combinedSecondary': getColorStyle({
            color: 'secondary',
            theme
          }),
          '&.MuiChip-combinedSuccess': getColorStyle({
            color: 'success',
            theme
          }),
          '&.MuiChip-combinedWarning': getColorStyle({
            color: 'warning',
            theme
          })
        },
        light: {
          ...defaultLightChip,
          '&.MuiChip-lightError': getColorStyle({ color: 'error', theme }),
          '&.MuiChip-lightInfo': getColorStyle({ color: 'info', theme }),
          '&.MuiChip-lightPrimary': getColorStyle({ color: 'primary', theme }),
          '&.MuiChip-lightSecondary': getColorStyle({
            color: 'secondary',
            theme
          }),
          '&.MuiChip-lightSuccess': getColorStyle({ color: 'success', theme }),
          '&.MuiChip-lightWarning': getColorStyle({ color: 'warning', theme })
        },
        root: {
          '&.MuiChip-colorError': getColor({ color: 'error', theme }),
          '&.MuiChip-colorInfo': getColor({ color: 'info', theme }),
          '&.MuiChip-colorPrimary': getColor({ color: 'primary', theme }),
          '&.MuiChip-colorSecondary': getColor({ color: 'secondary', theme }),
          '&.MuiChip-colorSuccess': getColor({ color: 'success', theme }),
          '&.MuiChip-colorWarning': getColor({ color: 'warning', theme }),
          '&:active': {
            boxShadow: 'none'
          },
          borderRadius: 4
        },
        sizeLarge: {
          fontSize: '1rem',
          height: 40
        }
      }
    }
  }
}
