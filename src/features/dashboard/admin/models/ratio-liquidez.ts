/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const ratioLiquidez = type({
  eSTER: 'number',
  fecha: 'string',
  nombre: 'string',
  tipoMedio: 'number'
})

export const ratioLiquidezMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Tipo Medio',
    key: 'tipoMedio'
  },
  {
    header: 'eSTER',
    key: 'eSTER'
  }
])

export type RatioLiquidez = typeof ratioLiquidez.infer

export const ratioLiquidezValidate = (attempt?: RatioLiquidez) =>
  ratioLiquidez(attempt) instanceof type.errors ? resetObject(attempt) : attempt
