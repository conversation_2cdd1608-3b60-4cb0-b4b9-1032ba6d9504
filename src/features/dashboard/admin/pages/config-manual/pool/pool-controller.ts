/* eslint-disable unicorn/no-keyword-prefix */
import { type Pool, poolDefault } from '@d/admin/models/pool'
import { PoolRepository } from '@d/admin/repositories/pool-repository'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay } from 'rambdax'
import { useCallback, useEffect, useRef, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const validate = (pool: Pool) => {
  const errors: Record<string, string> = {}
  if (!pool.fecha) errors['fecha'] = 'Required'
  return errors
}

export const poolController = () => {
  const day = calendarStore.actualDay.use()

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)

  const { calcularPool, deletePool, getPool, insertPool, updatePool } =
    container.get(PoolRepository)

  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(poolDefault)

  const {
    data: poolData,
    isFetching: isPoolPending,
    refetch: refetchPool
  } = useQuery({
    queryFn: getPool,
    queryKey: ['getPool']
  })

  const submitAction = useCallback(
    async (pool: Pool, _: FormikHelpers<Pool>) => {
      setOpenCreateEditDialog(false)
      await delay(150)
      setLoadingDialog(true)

      await (selectedIndex.current === null
        ? insertPool(pool)
        : updatePool(selectedData.current.id.toString(), pool))

      setLoadingDialog(false)
      void refetchPool()
    },
    []
  )

  const deleteAction = useCallback(async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deletePool(selectedData.current.id.toString())
      setLoadingDialog(false)
      void refetchPool()
    }
  }, [])

  useEffect(() => {
    void refetchPool()
  }, [day])

  return {
    calcularPool,
    deleteAction,
    isPoolPending,
    loadingDialog,
    openCreateEditDialog,
    openDeleteDialog,
    poolData,
    refetchPool,
    selectedData,
    selectedIndex,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  }
}
