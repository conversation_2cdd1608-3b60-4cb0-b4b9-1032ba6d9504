/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const areasNegocio = type({
  accountingAreaId: 'string',
  id: 'number',
  nombreInformeGlobal: 'string',
  orden: 'number',
  titulo: 'string'
})

export type AreasNegocio = typeof areasNegocio.infer

export const areasNegocioDefault: AreasNegocio = {
  accountingAreaId: '',
  id: 0,
  nombreInformeGlobal: '',
  orden: 0,
  titulo: ''
}

export const areasNegocioMetadata = new Set([
  {
    header: 'Área ID',
    key: 'id'
  },
  {
    header: 'Titulo',
    key: 'titulo'
  },
  {
    header: 'Nombre Informe Global',
    key: 'nombreInformeGlobal'
  },
  {
    header: 'Accounting Área Id',
    key: 'accountingAreaId'
  },
  {
    header: 'Orden',
    key: 'orden'
  }
])
