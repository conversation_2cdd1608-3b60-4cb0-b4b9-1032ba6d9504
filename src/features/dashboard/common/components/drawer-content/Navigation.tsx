import { menuItems as menuItem } from '@d/common/data'
import { Menu } from '@d/common/data/dashboard'
import { Box, Typography, useMediaQuery } from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { useEffect, useLayoutEffect, useState } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { authStore } from '@/common/store/auth-store'
import { menuStore } from '@/common/store/menu-store'
import { HORIZONTAL_MAX_ITEM } from '~/constants'
import type { NavItemType } from '~/types/menu'
import { MenuOrientation } from '~/types/resources'

import { NavGroup } from './NavGroup'

export const Navigation = () => {
  const theme = useTheme()

  const { user } = authStore.use()

  const downLG = useMediaQuery(theme.breakpoints.down('lg'))

  const { menuOrientation } = useContext(ResourceContext)
  const { drawerOpen } = menuStore.use()
  const [selectedItems, setSelectedItems] = useState<string | undefined>('')
  const [selectedLevel, setSelectedLevel] = useState<number>(0)
  const [menuItems, setMenuItems] = useState<{ items: NavItemType[] }>({
    items: []
  })

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG
  const lastItem = isHorizontal ? HORIZONTAL_MAX_ITEM : null
  let lastItemIndex = menuItems.items.length - 1
  let remItems: NavItemType[] = []
  let lastItemId = ''

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const getMenu = Menu()

  const handlerMenuItem = () => {
    const isFound = menuItem(user).items.some(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      element => element.id === 'group-dashboard'
    )

    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    if (getMenu.id !== undefined && !isFound) {
      menuItem(user).items.splice(0, 0, getMenu)
      setMenuItems(menuItem(user))
    }
  }

  useLayoutEffect(() => {
    setMenuItems(menuItem(user))
  }, [menuItem])

  useEffect(() => {
    handlerMenuItem()
  }, [])

  if (lastItem && lastItem < menuItems.items.length) {
    lastItemId = menuItems.items[lastItem - 1]?.id ?? ''
    lastItemIndex = lastItem - 1
    remItems = menuItems.items.slice(lastItem - 1).map(item => ({
      elements: item.children,
      icon: item.icon,
      title: item.title
    }))
  }

  return (
    <Box
      sx={{
        '& > ul:first-of-type': { mt: 0 },
        display: isHorizontal ? { lg: 'flex', xs: 'block' } : 'block',
        // eslint-disable-next-line sonarjs/no-nested-conditional
        pt: drawerOpen ? (isHorizontal ? 0 : 2) : 0
      }}
    >
      {menuItems.items.slice(0, lastItemIndex + 1).map(item =>
        item.type === 'group' ? (
          <NavGroup
            item={item}
            key={item.id}
            lastItem={lastItem ?? 0}
            lastItemId={lastItemId}
            remItems={remItems}
            selectedItems={selectedItems}
            selectedLevel={selectedLevel}
            setSelectedItems={setSelectedItems}
            setSelectedLevel={setSelectedLevel}
          />
        ) : (
          <Typography align='center' color='error' key={item.id} variant='h6'>
            Fix - Navigation Group
          </Typography>
        )
      )}
    </Box>
  )
}
