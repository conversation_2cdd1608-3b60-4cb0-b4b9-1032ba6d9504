import { reactRouter } from '@react-router/dev/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig, type Plugin } from 'vite'
import babel from 'vite-plugin-babel'
import { chunkSplitPlugin } from 'vite-plugin-chunk-split'
import tsconfigPaths from 'vite-tsconfig-paths'

import { imports, importTypes } from './config/imports'
//import { reactRouterDevTools } from 'react-router-devtools'
//import { vite as million } from '@million/lint'
//import obfuscatorPlugin from 'vite-plugin-javascript-obfuscator'
//import { qrcode } from 'vite-plugin-qrcode'
//import { analyzer } from 'vite-bundle-analyzer'

export default defineConfig({
  plugins: [
    AutoImport({
      dts: 'src/core/types/auto-imports.d.ts',
      eslintrc: { enabled: true },
      imports: ['react', ...imports, ...importTypes],
      include: [/\.[jt]sx?$/]
    }) as Plugin,
    //reactRouterDevTools({ appDir: './src' }),
    //analyzer(),
    //qrcode(),
    //obfuscatorPlugin(),
    chunkSplitPlugin(),
    babel({
      babelConfig: {
        compact: true,
        plugins: [
          ['transform-remove-console', { 'exclude': ['warn'] }],
          'babel-plugin-transform-typescript-metadata',
          ['@babel/plugin-proposal-decorators', { legacy: true }],
          '@babel/plugin-transform-class-properties',
          '@babel/plugin-transform-private-methods',
          'babel-plugin-react-compiler'
        ],
        presets: ['@babel/preset-typescript']
      }
    }),
    //million({ dev: true, telemetry: false }),
    tsconfigPaths(),
    reactRouter()
  ],
  server: { open: true, port: 3000 },
  ssr: {
    noExternal: [
      'apexcharts',
      'react-apexcharts',
      ...(process.env['NODE_ENV'] === 'production'
        ? [
            '@mui/system',
            '@mui/material',
            '@mui/x-date-pickers',
            '@mui/utils',
            '@mui/x-internals',
            '@mui/styled-engine'
          ]
        : [])
    ]
  }
})
