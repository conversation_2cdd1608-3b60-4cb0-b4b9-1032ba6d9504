import 'dayjs/locale/es'

import { CloseCircleOutlined, DownloadOutlined } from '@ant-design/icons'
import { Button, Dialog, DialogActions, DialogTitle } from '@mui/material'
import type { FC } from 'react'

export const DeleteDialog: FC<{
  onClick: (isOk: boolean) => void
  open: boolean
}> = ({ onClick, open }) => (
  <Dialog
    aria-describedby='delete-dialog-description'
    aria-labelledby='delete-dialog-title'
    onClose={() => onClick(false)}
    open={open}
  >
    <DialogTitle id='delete-dialog-title'>
      ¿Está seguro de eliminar este elemento?
    </DialogTitle>
    <DialogActions>
      <Button
        onClick={() => onClick(false)}
        startIcon={<CloseCircleOutlined />}
        variant='dashed'
      >
        Cancelar
      </Button>
      <Button
        autoFocus
        onClick={() => onClick(true)}
        startIcon={<DownloadOutlined />}
        variant='contained'
      >
        Aceptar
      </Button>
    </DialogActions>
  </Dialog>
)
