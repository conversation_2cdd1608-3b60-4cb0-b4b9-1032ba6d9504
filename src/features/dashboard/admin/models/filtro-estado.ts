/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const filtroEstado = type({
  codigo: 'string?',
  comentarios: 'string?',
  estado: 'string?',
  filtroEstadoID: 'number?'
})

export type FiltroEstado = typeof filtroEstado.infer

export const filtroEstadoDefault: FiltroEstado = {
  codigo: '',
  comentarios: '',
  estado: '',
  filtroEstadoID: 0
}

export const filtroEstadoMetadata = new Set([
  {
    header: 'Estado Id',
    key: 'filtroEstadoID'
  },
  {
    header: 'Estado',
    key: 'estado'
  },
  {
    header: 'Comentarios',
    key: 'comentarios'
  },
  {
    header: 'Código',
    key: 'codigo'
  }
])
