import { BookFilled } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const bancaCorporativa: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'bancaCorp-margenFinanciero',
          title: <FormattedMessage id='margenFinanciero' />,
          type: 'item',
          url: '/banca-corporativa/margen'
        },
        {
          id: 'bancaCorp-carteraInversion',
          title: <FormattedMessage id='carteraInversion' />,
          type: 'item',
          url: '/banca-corporativa/cartera-inversion'
        },
        {
          id: 'bancaCorp-coberturas',
          title: <FormattedMessage id='coberturas' />,
          type: 'item',
          url: '/banca-corporativa/coberturas'
        }
      ],
      icon: BookFilled,
      id: 'bancaCorp',
      title: <FormattedMessage id='bancaCorporativa' />,
      type: 'collapse'
    }
  ],
  icon: BookFilled,
  id: 'group-bancaCorporativa',
  title: <FormattedMessage id='bancaCorporativa' />,
  type: 'group'
}
