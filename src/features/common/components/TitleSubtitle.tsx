import { Stack, Typography } from '@mui/material'
import type { FC } from 'react'

export const TitleSubtitle: FC<{
  subtitle?: string
  sxProps?: object
  title: string
}> = ({ subtitle, sxProps, title }) => (
  <Stack
    sx={{
      alignItems: 'baseline',
      display: 'flex',
      flexDirection: 'row',
      margin: '20px 0px',
      ...sxProps
    }}
  >
    <Typography variant='h4'>{title}</Typography>
    {subtitle !== undefined && (
      <Typography color='primary.300' sx={{ whiteSpace: 'pre' }} variant='h4'>
        {' '}
        / {subtitle}
      </Typography>
    )}
  </Stack>
)
