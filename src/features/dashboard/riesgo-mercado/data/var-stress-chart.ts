import type { ApexOptions } from 'apexcharts'

export const varStressChartOpts: ApexOptions = {
  chart: { id: 'apex-bar' },
  dataLabels: { enabled: false },
  plotOptions: { bar: { borderRadius: 4 } },
  xaxis: {
    categories: [''],
    labels: { rotate: -45, style: { fontSize: '12px' } }
  },
  yaxis: {
    labels: {
      formatter: (value: number) => `${(value / 1000000).toString()}M`
    }
  }
}
