/* eslint-disable unicorn/no-keyword-prefix */
import { FileExcelOutlined } from '@ant-design/icons'
import {
  ratioLiquidez,
  ratioLiquidezMetadata
} from '@d/admin/models/ratio-liquidez'
import { RatioLiquidezRepository } from '@d/admin/repositories/ratio-liquidez-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import { Box, Button, Container, Grid, Paper, Typography } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { customExportToCSV } from '~/utils/csv'

export default () => {
  const day = calendarStore.formattedDay.use()

  const { getRatioLiquidez } = container.get(RatioLiquidezRepository)

  const [adjustDate, setAdjustDate] = useState(dayjs())

  const {
    data: getRatioLiquidezData,
    isFetching: isRatioLiquidezPending,
    refetch: refetchRatioLiquidez
  } = useQuery({
    queryFn: () => getRatioLiquidez(adjustDate.format('YYYY-MM-DD')),
    queryKey: ['getRatioLiquidez']
  })

  useEffect(() => {
    setAdjustDate(dayjs(day))
  }, [day])

  useEffect(() => {
    void refetchRatioLiquidez()
  }, [adjustDate])

  return isRatioLiquidezPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchRatioLiquidez()} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              mt: 3,
              width: '100%'
            }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'row' }}>
              <Typography component='p' sx={{ marginX: '20px', mt: '5px' }}>
                Fecha Cálculo:
              </Typography>
              <LocalizationProvider
                adapterLocale='es'
                dateAdapter={AdapterDayjs}
              >
                <DatePicker
                  defaultValue={adjustDate}
                  onChange={(newValue: Dayjs | null) =>
                    setAdjustDate(dayjs(newValue))
                  }
                  slotProps={{ textField: { size: 'small' } }}
                  value={adjustDate}
                />
              </LocalizationProvider>
            </Box>
            <Button
              onClick={() => {
                customExportToCSV(
                  [...ratioLiquidezMetadata].map(el => el.header),
                  getRatioLiquidezData ?? [],
                  [...ratioLiquidezMetadata].map(el => el.key),
                  'RatioLiquidez.csv'
                )
              }}
              startIcon={<FileExcelOutlined />}
              variant='contained'
            >
              Descargar excel
            </Button>
          </Box>

          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, width: '100%' }}>
              <GenericTable
                data={getRatioLiquidezData ?? []}
                entries={ratioLiquidez}
                metadata={ratioLiquidezMetadata}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
