/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const trade = type({
  accountingAreaID: 'string?',
  areaID: 'number?',
  description: 'string?',
  excludedFromPLRF: 'boolean?',
  excludedFromVaR: 'boolean?',
  gtm: 'boolean?',
  htm: 'boolean?',
  investment: 'boolean?',
  tradeGroup: 'string',
  tradeGroupID: 'number?'
})

export const tradeMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Descripción',
    key: 'description'
  },
  {
    header: 'Excluded PIRF',
    key: 'excludedFromPLRF'
  },
  {
    header: 'Excluded VaR',
    key: 'excludedFromVaR'
  },
  {
    header: 'Inversión',
    key: 'investment'
  },
  {
    header: 'Htm',
    key: 'htm'
  },
  {
    header: '<PERSON><PERSON><PERSON> título',
    key: 'accountingAreaID'
  }
])

export type Trade = typeof trade.infer

export const tradeDefault: Trade = {
  accountingAreaID: '',
  areaID: 0,
  description: '',
  excludedFromPLRF: false,
  excludedFromVaR: false,
  gtm: false,
  htm: false,
  investment: false,
  tradeGroup: '',
  tradeGroupID: 0
}
