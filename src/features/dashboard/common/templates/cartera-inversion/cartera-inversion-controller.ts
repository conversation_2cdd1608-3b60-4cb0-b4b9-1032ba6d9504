import {
  type CarteraDesglose,
  carteraDesgloseMetadata
} from '@d/common/models/cartera'
import type { SelectChangeEvent } from '@mui/material'
import { keys, nth, piped, prop } from 'rambdax'
import { type ChangeEvent, useCallback, useMemo, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { customExportToCSV } from '~/utils/csv'

export const carteraInversionController = (
  desglose: CarteraDesglose[],
  onClick?: (index: number) => void
) => {
  const [menuId, setMenuId] = useState(0)
  const [filter, setFilter] = useState('')

  const { actualDay } = calendarStore.use()

  const handleChange = useCallback((event: SelectChangeEvent) => {
    setMenuId(Number(event.target.value))
    if (menuId === 0) setFilter('')
  }, [])

  const handleTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFilter(event.currentTarget.value),
    []
  )

  const filterData = useMemo(() => {
    if (!filter.trim()) return desglose

    return desglose.filter(item => {
      const value = prop(
        piped(
          [...carteraDesgloseMetadata].map(el => el.key),
          keys => nth(menuId, keys) as keyof CarteraDesglose
        ),
        item
      )

      if (typeof value === 'string') {
        return value.toLowerCase().includes(filter.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(filter)
      }
      return false
    })
  }, [filter, menuId, desglose, actualDay])

  const onRowClick = useCallback((index: number) => {
    const selectedItem = nth(index, filterData)

    const realIndex = desglose.findIndex(item =>
      keys(selectedItem).every(
        key =>
          item[key as keyof CarteraDesglose] ===
          selectedItem?.[key as keyof CarteraDesglose]
      )
    )

    if (onClick !== undefined && realIndex !== -1) onClick(realIndex)
  }, [])

  const exportDesgloseCsv = useCallback(
    () =>
      customExportToCSV(
        [...carteraDesgloseMetadata].map(el => el.header),
        desglose,
        [...carteraDesgloseMetadata].map(el => el.key),
        'DesgloseActivos.csv'
      ),
    [desglose]
  )

  return {
    exportDesgloseCsv,
    filterData,
    handleChange,
    handleTextChange,
    menuId,
    onRowClick
  }
}
