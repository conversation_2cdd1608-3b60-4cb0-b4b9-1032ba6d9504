/* eslint-disable sonarjs/no-clear-text-protocols */
import { type ApisauceInstance, create } from 'apisauce'
import { injectable } from 'inversify'

//import { ENV } from '~/env'

const headers = {
  'Accept': 'application/json',
  'Content-Type': 'application/json'
}

export const auth = { token: '' }

@injectable()
export class ApiModule {
  readonly adClient: ApisauceInstance

  readonly client: ApisauceInstance

  constructor() {
    this.client = create({
      baseURL: 'http://172.26.245.5:8080',
      headers,
      timeout: 10000
    })
    this.adClient = create({
      baseURL: 'http://172.26.245.22:8083',
      headers,
      timeout: 10000
    })
  }

  apiTokenize(token = '') {
    auth.token = token
    this.client.axiosInstance.interceptors.request.use(config => {
      config.headers.Authorization = `Bearer ${token}`
      return config
    })
  }
}
