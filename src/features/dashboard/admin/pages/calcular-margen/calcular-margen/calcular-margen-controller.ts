import { CalcMargenRepository } from '@d/admin/repositories/calc-margen-repository'
import { CommonRepository } from '@d/common/repository/common-repository'
import type { SelectChangeEvent } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { FormikHelpers } from 'formik'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const calcularMargenController = () => {
  const availableStore = calendarStore.available.use()

  const {
    generarMargenFinanciero,
    getLogs,
    getReferencia,
    getResumenImportacion,
    getUltimaImportacion,
    setFundingDate,
    setFundingDateBase,
    setImportbnd,
    setImportdep,
    setImportDeriv,
    setImportotros,
    setImportPrestamos,
    setImportrep
  } = container.get(CalcMargenRepository)

  const { getFundingRate, putAvailable } = container.get(CommonRepository)

  const [openConfDial, setOpenConfDial] = useState(false)
  const [available, setAvailable] = useState(availableStore ?? false)
  const [showAlert, setShowAlert] = useState(false)
  const [dialogESP, setDialogESP] = useState(false)
  const [selData, setSelData] = useState<{
    logs: boolean
    referencia: boolean
    resumen: boolean
  }>({
    logs: true,
    referencia: false,
    resumen: false
  })
  const [dialogDepos, setDialogDepos] = useState(false)
  const [dialogRepos, setDialogRepos] = useState(false)
  const [dialogBonos, setDialogBonos] = useState(false)
  const [dialogOtros, setDialogOtros] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [menuEsterId, setMenuEsterId] = useState('0')
  const [dialogEster, setDialogEster] = useState(false)
  const [dialogDerivados, setDialogDerivados] = useState(false)
  const [dialogPrestamos, setDialogPrestamos] = useState(false)

  const [componentDate, setComponentDate] = useState<dayjs.Dayjs | null>(
    dayjs()
  )

  const {
    data: ultimaImportacionData,
    isFetching: isUltimaImportacionPending,
    refetch: refetchUltimaImportacion
  } = useQuery({
    queryFn: getUltimaImportacion,
    queryKey: ['getUltimaImportacionFINANCE']
  })

  const { data: fundingRateData, isFetching: isFundingRatePending } = useQuery({
    queryFn: getFundingRate,
    queryKey: ['getFundingRatFINANCE']
  })

  const {
    data: logsData,
    isFetching: isLogsPending,
    refetch: refetchLogs
  } = useQuery({
    queryFn: getLogs,
    queryKey: ['getLogs']
  })

  const {
    data: referenciaData,
    isFetching: isReferenciaPending,
    refetch: refetchReferencia
  } = useQuery({
    queryFn: getReferencia,
    queryKey: ['getReferencia']
  })

  const {
    data: resumenImportacionData,
    isFetching: isResumenImportacionPending,
    refetch: refetchResumenImportacion
  } = useQuery({
    queryFn: getResumenImportacion,
    queryKey: ['getResumenImportacion']
  })

  const setupMargenFinanciero = async () => {
    setIsLoading(true)
    await generarMargenFinanciero()
    setIsLoading(false)
    setShowAlert(true)
    setTimeout(() => setShowAlert(false), 2000)
  }

  const updateAvailable = async (isOk: boolean) => {
    setOpenConfDial(false)
    if (isOk) {
      setIsLoading(true)
      await putAvailable(available)
      setAvailable(!available)
      calendarStore.setAvailable(available)
    }
  }

  const setEsterESP = async (isOk: boolean) => {
    setDialogESP(false)
    if (isOk) {
      setIsLoading(true)
      await setFundingDateBase(
        componentDate?.format('YYYY-MM-DD') ?? dayjs().format('YYYY-MM-DD'),
        fundingRateData ?? 0
      )
      setIsLoading(false)
      setShowAlert(true)
      setTimeout(() => setShowAlert(false), 2000)
    }
  }

  const submitEstrAction = async (
    estr: { spread: string; tasa: string },
    _: FormikHelpers<{ spread: string; tasa: string }>
  ) => {
    setDialogEster(false)
    setIsLoading(true)
    await setFundingDate(
      componentDate?.format('YYYY-MM-DD') ?? dayjs().format('YYYY-MM-DD'),
      estr.tasa,
      estr.spread
    )
    setIsLoading(false)
    setShowAlert(true)
    setTimeout(() => setShowAlert(false), 2000)
  }

  const setEster = (event: SelectChangeEvent) => {
    setMenuEsterId(event.target.value)
    Number(event.target.value) === 1 ? setDialogEster(true) : setDialogESP(true)
  }

  const setupDialogs = async (
    isOk: boolean,
    action: 'bnd' | 'dep' | 'lnd' | 'otros' | 'rep' | 'swp'
  ) => {
    switch (action) {
      case 'bnd': {
        setDialogBonos(false)
        break
      }
      case 'dep': {
        setDialogDepos(false)
        break
      }
      case 'lnd': {
        setDialogPrestamos(false)
        break
      }
      case 'otros': {
        setDialogOtros(false)
        break
      }
      case 'rep': {
        setDialogRepos(false)
        break
      }
      case 'swp': {
        setDialogDerivados(false)
        break
      }
    }
    switch (action) {
      case 'bnd': {
        if (isOk) {
          setIsLoading(true)
          await setImportbnd()
          setIsLoading(false)
          setShowAlert(true)
          setTimeout(() => setShowAlert(false), 2000)
        }
        break
      }
      case 'dep': {
        if (isOk) {
          setIsLoading(true)
          await setImportdep()
          setIsLoading(false)
          setShowAlert(true)
          setTimeout(() => setShowAlert(false), 2000)
        }
        break
      }
      case 'lnd': {
        if (isOk) {
          setIsLoading(true)
          await setImportDeriv()
          setIsLoading(false)
          setShowAlert(true)
          setTimeout(() => setShowAlert(false), 2000)
        }
        break
      }
      case 'otros': {
        if (isOk) {
          setIsLoading(true)
          await setImportotros()
          setIsLoading(false)
          setShowAlert(true)
          setTimeout(() => setShowAlert(false), 2000)
        }
        break
      }
      case 'rep': {
        if (isOk) {
          setIsLoading(true)
          await setImportrep()
          setIsLoading(false)
          setShowAlert(true)
          setTimeout(() => setShowAlert(false), 2000)
        }
        break
      }
      case 'swp': {
        if (isOk) {
          setIsLoading(true)
          await setImportPrestamos()
          setIsLoading(false)
          setShowAlert(true)
          setTimeout(() => setShowAlert(false), 2000)
        }
        break
      }
    }
  }

  const refetch = () => {
    void refetchUltimaImportacion()
    void refetchLogs()
    void refetchReferencia()
    void refetchResumenImportacion()
  }

  return {
    available,
    componentDate,
    dialogBonos,
    dialogDepos,
    dialogDerivados,
    dialogESP,
    dialogEster,
    dialogOtros,
    dialogPrestamos,
    dialogRepos,
    isFundingRatePending,
    isLoading,
    isLogsPending,
    isReferenciaPending,
    isResumenImportacionPending,
    isUltimaImportacionPending,
    logsData,
    menuEsterId,
    openConfDial,
    referenciaData,
    refetch,
    resumenImportacionData,
    selData,
    setComponentDate,
    setDialogBonos,
    setDialogDepos,
    setDialogDerivados,
    setDialogEster,
    setDialogOtros,
    setDialogPrestamos,
    setDialogRepos,
    setEster,
    setEsterESP,
    setOpenConfDial,
    setSelData,
    setupDialogs,
    setupMargenFinanciero,
    showAlert,
    submitEstrAction,
    ultimaImportacionData,
    updateAvailable
  }
}
