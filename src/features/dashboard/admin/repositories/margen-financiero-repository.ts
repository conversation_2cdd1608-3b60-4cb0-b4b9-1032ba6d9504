/* eslint-disable sonarjs/use-type-alias */
import { MargenFinancieroInforme } from '@d/admin/models/margen-financiero-informe'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class MargenFinancieroRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/margenfinanciero'

  readonly ajusteMargen = (margenParams: {
    ajusteDia: string
    ajusteMes: string
    area: string
    balance: string
    ccy: string
    epi: string
    fecha: string
    producto: string
    saldo: string
    tradeGroup: string
  }) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/ajusteMargen`,
      undefined,
      {
        params: margenParams
      }
    )

  readonly aplicaAjusteMargen = (params: { fecha: string; user: string }) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/ajusteMargen`,
      undefined,
      { params }
    )

  readonly getMargenFinancieroAjustes = (fecha: string) =>
    this.api.client
      .get<MargenFinancieroInforme[]>(`${this.route}/${fecha}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getMargenFinancieroInforme = (
    fecha: string,
    accountingAreaID: string,
    balance: string,
    tradeGroup: string,
    producto: string
  ) =>
    this.api.client
      .get<MargenFinancieroInforme[]>(
        `${this.route}/${fecha}/${accountingAreaID === '' ? 'NULL' : accountingAreaID}/${balance === '' ? 'NULL' : balance}/${tradeGroup === '' ? 'NULL' : tradeGroup}/${producto === '' ? 'NULL' : producto}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly setAjusteIntereses = (interesesParams: {
    ajusteDia: string
    ajusteMes: string
    area: string
    balance: string
    ccy: string
    epi: string
    fecha: string
    int: string
    producto: string
    tradeGroup: string
  }) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/ajusteIntereses`,
      undefined,
      {
        params: interesesParams
      }
    )

  readonly setAjusteSaldo = (saldoParams: {
    accountingAreaID: string
    ajusteDia: string
    ajusteMes: string
    balance: string
    ccy: string
    epi: string
    fecha: string
    producto: string
    saldo: string
    tradeGroup: string
  }) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/ajusteSaldo`,
      undefined,
      {
        params: saldoParams
      }
    )
}
