import type { ApexOptions } from 'apexcharts'

import { myLocal } from '~/lang'
import {
  apexAlterPaletteColors,
  apexPaletteColors
} from '~/resources/config/colors'
import { numberFormat } from '~/utils/format-number'

export const negoRentabChartOpts = (
  temp: 'day' | 'month' | 'year',
  isNego = false
): ApexOptions => ({
  chart: {
    defaultLocale: 'es',
    id: `apex-${isNego ? 'neg' : 'margen'}-${temp}`,
    locales: myLocal
  },
  colors: isNego ? apexAlterPaletteColors : apexPaletteColors,
  dataLabels: { enabled: false },
  xaxis: {
    labels: {
      datetimeUTC: false
    },
    type: 'datetime' as const
  },
  yaxis: {
    labels: {
      formatter: value => `${numberFormat(value / 1000, 2)}k`
    }
  }
})
