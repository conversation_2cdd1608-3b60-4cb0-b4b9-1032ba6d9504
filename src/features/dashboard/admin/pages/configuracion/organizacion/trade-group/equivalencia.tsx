import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Equivalencia,
  equivalencia,
  equivalenciaDefault,
  equivalenciaMetadata
} from '@d/admin/models/equivalencia'
import { EquivalenciaRepository } from '@d/admin/repositories/equivalencia-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteEquivalencia,
    getEquivalencia,
    insertEquivalencia,
    updateEquivalencia
  } = container.get(EquivalenciaRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(equivalenciaDefault)

  const {
    data: allEquivalenciaData,
    isFetching: isGetEquivalenciaPending,
    refetch: refetchEquivalencia
  } = useQuery({
    queryFn: getEquivalencia,
    queryKey: ['getEquivalencia']
  })

  const submitAction = async (
    incoming: Equivalencia,
    _: FormikHelpers<Equivalencia>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertEquivalencia(incoming)
      : updateEquivalencia(
          selectedData.current.sgtID?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchEquivalencia()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteEquivalencia(selectedData.current.sgtID?.toString() ?? '0')
      setLoadingDialog(false)
      void refetchEquivalencia()
    }
  }

  return isGetEquivalenciaPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchEquivalencia()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = equivalenciaDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allEquivalenciaData ?? []}
                entries={equivalencia}
                isDeleteAction
                isEditAction
                metadata={equivalenciaMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allEquivalenciaData ?? []) ?? equivalenciaDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Equivalencia>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          nombreLargoSGT: Yup.string().max(50).required('Nombre es requerido'),
          trade: Yup.string().max(20).required('Trade group requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Equivalencia>
              field='nombreLargoSGT'
              label='Nombre largo: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.nombreLargoSGT.toString()}
            />
            <TextFormControl<Equivalencia>
              field='nombreCortoSGT'
              label='Nombre corto: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.nombreCortoSGT?.toString() ?? ''}
            />
            <TextFormControl<Equivalencia>
              field='tradeGroup'
              label='Trade Group: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.tradeGroup.toString()}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
