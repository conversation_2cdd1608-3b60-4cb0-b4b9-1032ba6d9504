import { SearchOutlined } from '@ant-design/icons'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Portfolios,
  portfolios,
  portfoliosDefault,
  portfoliosMetadata
} from '@d/admin/models/portfolio'
import { PortfoliosRepository } from '@d/admin/repositories/portfolio-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  type SelectChangeEvent,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth, piped, prop } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deletePortfolios,
    getPortfolios,
    insertPortfolios,
    updatePortfolios
  } = container.get(PortfoliosRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [menuId, setMenuId] = useState(0)
  const [filterPortfoliosText, setFilterPortfoliosText] = useState('')
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(portfoliosDefault)

  const {
    data: allPortfoliosData,
    isFetching: isGetPortfoliosPending,
    refetch: refetchPortfolios
  } = useQuery({
    queryFn: getPortfolios,
    queryKey: ['getPortfolios']
  })

  const handleChange = (event: SelectChangeEvent) => {
    setMenuId(Number(event.target.value))
  }

  const handleTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFilterPortfoliosText(event.currentTarget.value),
    []
  )

  const filterPortfolios = useMemo(() => {
    if (!filterPortfoliosText.trim()) return allPortfoliosData ?? []

    return (allPortfoliosData ?? []).filter(item => {
      const value = prop(
        piped(
          [...portfoliosMetadata].map(el => el.key),
          keys => nth(menuId, keys) as keyof Portfolios
        ),
        item
      )
      return (
        value?.toLowerCase().includes(filterPortfoliosText.toLowerCase()) ??
        false
      )
    })
  }, [filterPortfoliosText, menuId, allPortfoliosData])

  const submitAction = async (
    incoming: Portfolios,
    _: FormikHelpers<Portfolios>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertPortfolios(incoming)
      : updatePortfolios(
          selectedData.current.portfolioID?.toString() ?? '',
          incoming
        ))
    setLoadingDialog(false)
    void refetchPortfolios()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deletePortfolios(selectedData.current.portfolioID?.toString() ?? '')
      setLoadingDialog(false)
      void refetchPortfolios()
    }
  }

  return isGetPortfoliosPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchPortfolios()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = portfoliosDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          {(allPortfoliosData ?? []).length > 0 && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                height: '70px',
                justifyContent: 'space-between',
                pl: '30px',
                pr: '40px',
                width: '100%'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  mt: 2
                }}
              >
                <Typography sx={{ mt: 1 }} variant='subtitle1'>
                  Buscar en:
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    minWidth: 200,
                    ml: 2
                  }}
                >
                  <FormControl fullWidth>
                    <Select
                      id='demo-simple-select'
                      onChange={handleChange}
                      value={menuId.toString()}
                    >
                      {[...portfoliosMetadata].map((data, index) => (
                        <MenuItem key={index} value={index}>
                          {data.header}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      ml: { md: 1, xs: 0 }
                    }}
                  >
                    <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                      <OutlinedInput
                        aria-describedby='header-search-text'
                        endAdornment={
                          <InputAdornment position='end' sx={{ mr: -0.5 }}>
                            <SearchOutlined />
                          </InputAdornment>
                        }
                        id='header-search'
                        inputProps={{ 'aria-label': 'weight' }}
                        onChange={handleTextChange}
                        placeholder='Buscar'
                      />
                    </FormControl>
                  </Box>
                </Box>
              </Box>
            </Box>
          )}
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={filterPortfolios}
                entries={portfolios}
                isDeleteAction
                isEditAction
                metadata={portfoliosMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allPortfoliosData ?? []) ?? portfoliosDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Portfolios>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          pid: Yup.number().required('PID is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Portfolios>
              field='pid'
              label='PID: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              type='number'
              value={values.pid?.toString() ?? '0'}
            />
            <TextFormControl<Portfolios>
              field='portfolioID'
              label='Portafolio Id: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.portfolioID?.toString() ?? '0'}
            />
            <TextFormControl<Portfolios>
              field='portfolioType'
              label='Portafolio Type: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.portfolioType?.toString() ?? '0'}
            />
            <TextFormControl<Portfolios>
              field='description'
              label='Descripción: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.description?.toString() ?? '0'}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
