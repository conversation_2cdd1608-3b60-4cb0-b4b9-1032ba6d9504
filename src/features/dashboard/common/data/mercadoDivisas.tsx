import { SwapOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const mercadoDivisas: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'mercadoDivisas-margenFinanciero',
          title: <FormattedMessage id='margenFinanciero' />,
          type: 'item',
          url: '/mercado-divisas/margen-financiero'
        },
        {
          id: 'mercadoDivisas-PosMonedaExtrangera',
          title: <FormattedMessage id='posMonedaExtrangera' />,
          type: 'item',
          url: '/mercado-divisas/posmonextr/mesa-contado'
        },
        {
          id: 'mercadoDivisas-PyLMonedaExtrangera',
          title: <FormattedMessage id='pYlMonedaExtrangera' />,
          type: 'item',
          url: '/mercado-divisas/pylmonextr'
        },
        {
          id: 'mercadoDivisas-riesgoMercado',
          title: <FormattedMessage id='riesgoMercado' />,
          type: 'item',
          url: '/mercado-divisas/riesgo-mercado'
        }
      ],
      icon: SwapOutlined,
      id: 'mercadoDivisas',
      title: <FormattedMessage id='mercadoDivisas' />,
      type: 'collapse'
    }
  ],
  icon: SwapOutlined,
  id: 'group-mercadoDivisas',
  title: <FormattedMessage id='mercadoDivisas' />,
  type: 'group'
}
