/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const fechasHistorico = type({
  currency: 'string?',
  fecha: 'string',
  rate1: 'number?',
  spread: 'number?',
  tasa: 'number?'
})

export const fechasHistoricoFchMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  }
])

export const fechasHistoricoDetailMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Rate',
    key: 'rate1'
  },
  {
    header: 'Spread',
    key: 'spread'
  },
  {
    header: 'Currency',
    key: 'currency'
  },
  {
    header: 'Tasa',
    key: 'tasa'
  }
])

export type FechasHistorico = typeof fechasHistorico.infer
