/* eslint-disable @typescript-eslint/init-declarations */
/* eslint-disable sonarjs/cognitive-complexity */
import { ApartmentOutlined, HomeFilled, HomeOutlined } from '@ant-design/icons'
import { Divider, Grid, Typography } from '@mui/material'
import MuiBreadcrumbs from '@mui/material/Breadcrumbs'
import { useTheme } from '@mui/material/styles'
import {
  type CSSProperties,
  type FC,
  type JSX,
  useEffect,
  useState
} from 'react'
import { Link, useLocation } from 'react-router'

import type { NavItemType } from '~/types/menu'
import type { OverrideIcon } from '~/types/root'

import { MainCard } from './MainCard'

export const Breadcrumbs: FC<{
  card?: boolean
  divider?: boolean
  icon?: boolean
  icons?: boolean
  maxItems?: number
  navigation?: { items: NavItemType[] }
  rightAlign?: boolean
  separator?: OverrideIcon
  sx?: CSSProperties & {
    bgcolor?: string
    mb?: string
  }
  title?: boolean
  titleBottom?: boolean
}> = ({
  card,
  divider = true,
  icon,
  icons,
  maxItems,
  navigation,
  rightAlign,
  separator,
  sx,
  title,
  titleBottom,
  ...others
}) => {
  const theme = useTheme()
  const location = useLocation()
  const [main, setMain] = useState<NavItemType | undefined>()
  const [item, setItem] = useState<NavItemType>()

  const iconSX = {
    color: theme.palette.secondary.main,
    height: '1rem',
    marginRight: theme.spacing(0.75),
    marginTop: `-${theme.spacing(0.25)}`,
    width: '1rem'
  }

  let customLocation = location.pathname

  if (customLocation.includes('/components-overview/breadcrumbs')) {
    customLocation = '/apps/kanban/board'
  }

  if (customLocation.includes('/apps/kanban/backlogs')) {
    customLocation = '/apps/kanban/board'
  }

  useEffect(() => {
    if (customLocation.includes('/apps/profiles/user/payment')) {
      setItem(undefined)
    }
  }, [item, customLocation])

  const getCollapse = (menu: NavItemType) => {
    if (menu.children) {
      // eslint-disable-next-line sonarjs/no-ignored-return, @typescript-eslint/no-unnecessary-condition
      menu.children.filter((collapse: NavItemType) => {
        if (collapse.type != null && collapse.type === 'collapse') {
          getCollapse(collapse as { children: NavItemType[]; type?: string })
          if (collapse.url === customLocation) {
            setMain(collapse)
            setItem(collapse)
          }
        } else if (
          collapse.type != null &&
          collapse.type === 'item' &&
          customLocation === collapse.url
        ) {
          setMain(menu)
          setItem(collapse)
        }
        return false
      })
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const SeparatorIcon = separator!
  const separatorIcon = separator ? (
    <SeparatorIcon style={{ fontSize: '0.75rem', marginTop: 2 }} />
  ) : (
    '/'
  )

  useEffect(() => {
    navigation?.items.map(menu => {
      if (menu.type != null && menu.type === 'group') {
        getCollapse(menu as { children: NavItemType[]; type?: string })
      }
      return false
    })
  })

  let mainContent: JSX.Element | undefined
  let itemContent: JSX.Element | undefined
  let breadcrumbContent: JSX.Element = <Typography />
  let itemTitle: NavItemType['title'] = ''
  let CollapseIcon
  let ItemIcon

  // collapse item
  if (main && main.type === 'collapse' && main.breadcrumbs === true) {
    CollapseIcon = main.icon ?? ApartmentOutlined
    mainContent = (
      <Typography
        color='textSecondary'
        component={Link}
        sx={{ textDecoration: 'none' }}
        to={document.location.pathname}
        variant='h6'
      >
        {(icons ?? false) && <CollapseIcon style={iconSX} />}
        {main.title}
      </Typography>
    )
    breadcrumbContent = (
      <MainCard
        border={card}
        sx={
          card === false
            ? { bgcolor: 'transparent', mb: 3, ...sx }
            : { mb: 3, ...sx }
        }
        {...others}
        content={card}
        shadow='none'
      >
        <Grid
          alignItems={(rightAlign ?? false) ? 'center' : 'flex-start'}
          container
          direction={(rightAlign ?? false) ? 'row' : 'column'}
          justifyContent={
            (rightAlign ?? false) ? 'space-between' : 'flex-start'
          }
          spacing={1}
        >
          <Grid item>
            <MuiBreadcrumbs
              aria-label='breadcrumb'
              maxItems={maxItems ?? 8}
              separator={separatorIcon}
            >
              <Typography
                color='textSecondary'
                component={Link}
                sx={{ textDecoration: 'none' }}
                to='/'
                variant='h6'
              >
                {(icons ?? false) && <HomeOutlined style={iconSX} />}
                {(icon ?? false) && !(icons ?? false) && (
                  <HomeFilled style={{ ...iconSX, marginRight: 0 }} />
                )}
                {(!(icon ?? false) || (icons ?? false)) && 'Home'}
              </Typography>
              {mainContent}
            </MuiBreadcrumbs>
          </Grid>
          {(title ?? false) && (titleBottom ?? false) && (
            <Grid item sx={{ mt: card === false ? 0.25 : 1 }}>
              <Typography variant='h2'>{main.title}</Typography>
            </Grid>
          )}
        </Grid>
        {card === false && divider && <Divider sx={{ mt: 2 }} />}
      </MainCard>
    )
  }

  if (item && item.type === 'item') {
    itemTitle = item.title

    ItemIcon = item.icon ?? ApartmentOutlined
    itemContent = (
      <Typography color='textPrimary' variant='subtitle1'>
        {(icons ?? false) && <ItemIcon style={iconSX} />}
        {itemTitle}
      </Typography>
    )

    // main
    if (item.breadcrumbs !== false) {
      breadcrumbContent = (
        <MainCard
          border={card}
          sx={
            card === false
              ? { bgcolor: 'transparent', mb: 3, ...sx }
              : { mb: 3, ...sx }
          }
          {...others}
          content={card}
          shadow='none'
        >
          <Grid
            alignItems={(rightAlign ?? false) ? 'center' : 'flex-start'}
            container
            direction={(rightAlign ?? false) ? 'row' : 'column'}
            justifyContent={
              (rightAlign ?? false) ? 'space-between' : 'flex-start'
            }
            spacing={1}
          >
            {(title ?? false) && !(titleBottom ?? false) && (
              <Grid item>
                <Typography variant='h2'>{item.title}</Typography>
              </Grid>
            )}
            <Grid item>
              <MuiBreadcrumbs
                aria-label='breadcrumb'
                maxItems={maxItems ?? 8}
                separator={separatorIcon}
              >
                <Typography
                  color='textSecondary'
                  component={'div'}
                  sx={{ textDecoration: 'none' }}
                  variant='h6'
                >
                  {(icons ?? false) && <HomeOutlined style={iconSX} />}
                  {(icon ?? false) && !(icons ?? false) && (
                    <HomeFilled style={{ ...iconSX, marginRight: 0 }} />
                  )}
                  {(!(icon ?? false) || (icons ?? false)) && main?.title}
                </Typography>
                {mainContent}
                {itemContent}
              </MuiBreadcrumbs>
            </Grid>
            {(title ?? false) && (titleBottom ?? false) && (
              <Grid item sx={{ mt: card === false ? 0.25 : 1 }}>
                <Typography variant='h2'>{item.title}</Typography>
              </Grid>
            )}
          </Grid>
          {card === false && divider && <Divider sx={{ mt: 2 }} />}
        </MainCard>
      )
    }
  }

  return breadcrumbContent
}
