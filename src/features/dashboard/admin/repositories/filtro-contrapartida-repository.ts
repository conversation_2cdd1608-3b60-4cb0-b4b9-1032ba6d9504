/* eslint-disable sonarjs/use-type-alias */
import type { FiltroContrapartidas } from '@d/admin/models/filtro-contrapartida'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class FiltroContrapartidaRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/contrapartidas'

  readonly deleteFiltroContrapartida = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getFiltroContrapartidas = () =>
    this.api.client
      .get<FiltroContrapartidas[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertFiltroContrapartidas = (
    filtroContrapartidas: FiltroContrapartidas
  ) =>
    this.api.client.post<{ message: string }>(this.route, filtroContrapartidas)

  readonly updateFiltroContrapartidas = (
    id: string,
    filtroContrapartidas: FiltroContrapartidas
  ) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      filtroContrapartidas
    )
}
