import { DeleteOutlined, SearchOutlined } from '@ant-design/icons'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import {
  type FechasHistorico,
  fechasHistorico,
  fechasHistoricoDetailMetadata,
  fechasHistoricoFchMetadata
} from '@d/admin/models/fechas-historico'
import { BenchmarkRepository } from '@d/admin/repositories/benchmark-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import {
  Box,
  Container,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Paper
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { nth, prop } from 'rambdax'
import type { ChangeEvent } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { deleteBenchmark, getAllBenchmarkDates, getBenchmark } =
    container.get(BenchmarkRepository)

  const [dateClicked, setDateClicked] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [benchmarks, setBenchmarks] = useState<FechasHistorico[]>([])
  const [filterText, setFilterText] = useState('')
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)

  const { data: allBenchmarkDatesData, isFetching: allBenchmarkDatesPending } =
    useQuery({
      queryFn: getAllBenchmarkDates,
      queryKey: ['getgetAllBenchmarkDates']
    })

  const isShow = () => dateClicked === ''

  const handleTextChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => setFilterText(event.target.value)

  const filterFechas = useMemo(() => {
    if (filterText.trim() === '') return allBenchmarkDatesData ?? []

    return (allBenchmarkDatesData ?? []).filter(item => {
      const value = prop('fecha', item)
      return value.toLowerCase().includes(filterText.toLowerCase())
    })
  }, [filterText, allBenchmarkDatesData])

  const fetchBenchmarks = async (date: string) => {
    setIsLoading(true)
    setBenchmarks(await getBenchmark(date))
    setIsLoading(false)
  }

  const handleDelete = async () => {
    await deleteBenchmark(dateClicked)
    await fetchBenchmarks(dateClicked)
  }

  useEffect(
    () => () => {
      setDateClicked('')
      setBenchmarks([])
      setFilterText('')
      setOpenDeleteDialog(false)
    },
    []
  )

  return allBenchmarkDatesPending ? (
    <Loading />
  ) : (
    <>
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <TitleSubtitle sxProps={{ ml: 3 }} title='Fechas Historizadas' />
          <Grid item sx={{ alignItems: 'center', display: 'flex' }} xs={12}>
            <Box sx={{ width: '100%' }}>
              <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                <OutlinedInput
                  aria-describedby='header-search-text'
                  endAdornment={
                    <InputAdornment position='end' sx={{ mr: -0.5 }}>
                      <SearchOutlined />
                    </InputAdornment>
                  }
                  id='header-search'
                  inputProps={{ 'aria-label': 'weight' }}
                  onChange={handleTextChange}
                  placeholder='Buscar Fecha'
                />
              </FormControl>
              <IconButton
                color='primary'
                onClick={() => setOpenDeleteDialog(true)}
                sx={{ display: isShow() ? 'none' : null, mb: '2px', ml: 1 }}
              >
                <DeleteOutlined />
              </IconButton>
            </Box>
          </Grid>
          <Grid item sx={{ display: 'flex' }} xs={3}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={filterFechas}
                entries={fechasHistorico}
                isSelected
                metadata={fechasHistoricoFchMetadata}
                onRowClick={async ind => {
                  const date = nth(ind, filterFechas) ?? {
                    fecha: ''
                  }
                  setDateClicked(date.fecha)
                  await fetchBenchmarks(date.fecha)
                }}
              />
            </Paper>
          </Grid>
          <Grid
            item
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}
            xs={9}
          >
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              {isLoading ? (
                <Loading />
              ) : (
                <GenericTable
                  data={benchmarks}
                  emptyMessage='No hay datos, elija una fecha con datos disponibles'
                  entries={fechasHistorico}
                  metadata={fechasHistoricoDetailMetadata}
                />
              )}
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <DeleteDialog onClick={handleDelete} open={openDeleteDialog} />
    </>
  )
}
