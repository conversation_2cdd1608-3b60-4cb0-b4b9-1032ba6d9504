import { InboxOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const saldosTesoreros: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'saldosTesoreros-margenFinanciero',
          title: <FormattedMessage id='margenFinanciero' />,
          type: 'item',
          url: '/saldos-tesoreros/margen'
        }
      ],
      icon: InboxOutlined,
      id: 'saldosTesoreros',
      title: <FormattedMessage id='saldosTesoreros' />,
      type: 'collapse'
    }
  ],
  icon: InboxOutlined,
  id: 'group-saldosTesoreros',
  title: <FormattedMessage id='saldosTesoreros' />,
  type: 'group'
}
