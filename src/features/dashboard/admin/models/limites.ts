/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const limites = type({
  limiteID: 'number',
  nombreCorto: 'string',
  nombreLargo: 'string',
  rrcc: 'number?',
  tipoLimite: 'string'
})

export type Limites = typeof limites.infer

export const limitesDefault: Limites = {
  limiteID: 0,
  nombreCorto: '',
  nombreLargo: '',
  rrcc: 0,
  tipoLimite: ''
}

export const limitesMetadata = new Set([
  {
    header: 'Nombre Largo',
    key: 'nombreLargo'
  },
  {
    header: 'Tipos de Límite',
    key: 'tipoLimite'
  },
  {
    header: 'Rrcc',
    key: 'rrcc'
  }
])
