import { AreaChartOutlined } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'

import type { NavItemType } from '~/types/menu'

export const coap: NavItemType = {
  children: [
    {
      children: [
        {
          id: 'coap-margenFinanciero',
          title: <FormattedMessage id='margenFinanciero' />,
          type: 'item',
          url: '/coap/margen-financiero'
        },
        {
          id: 'coap-trading',
          title: <FormattedMessage id='trading' />,
          type: 'item',
          url: '/coap/trading'
        },
        {
          id: 'coap-carteraInversion',
          title: <FormattedMessage id='carteraInversion' />,
          type: 'item',
          url: '/coap/cartera-inversion'
        },
        {
          id: 'coap-carteraRenta',
          title: <FormattedMessage id='carteraRenta' />,
          type: 'item',
          url: '/coap/cartera-renta'
        },
        {
          id: 'coap-carteraVencimiento',
          title: <FormattedMessage id='carteraVencimiento' />,
          type: 'item',
          url: '/coap/cartera-vencimiento'
        },
        {
          id: 'coap-coberturas',
          title: <FormattedMessage id='coberturas' />,
          type: 'item',
          url: '/coap/coberturas'
        },
        {
          id: 'coap-riesgoMercado',
          title: <FormattedMessage id='riesgoMercado' />,
          type: 'item',
          url: '/coap/riesgo-mercado'
        }
      ],
      icon: AreaChartOutlined,
      id: 'coap',
      title: <FormattedMessage id='coap' />,
      type: 'collapse'
    }
  ],
  icon: AreaChartOutlined,
  id: 'group-coap',
  title: <FormattedMessage id='coap' />,
  type: 'group'
}
