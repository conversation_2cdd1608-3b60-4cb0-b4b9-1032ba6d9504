/* eslint-disable @typescript-eslint/no-empty-function */
import { alertStore } from '@c/store/alert-store'
import { CssB<PERSON>line, StyledEngineProvider, ThemeProvider } from '@mui/material'
import { createContext, type FC, type ReactNode } from 'react'
import { useLocalStorage } from 'usehooks-ts'

import { OverlayAlert } from '@/dashboard/admin/components/alert'
import { resConfig } from '~/constants'
import { useTemplateTheme } from '~/resources/theme/use-template-theme'
import type {
  FontFamily,
  MenuOrientation,
  PresetColor,
  ResourcesConfig,
  ThemeDirection,
  ThemeMode
} from '~/types/resources'

export const ResourceContext = createContext<
  ResourcesConfig & {
    container: boolean
    fontFamily: FontFamily
    i18n: 'en' | 'es'
    menuOrientation: MenuOrientation
    miniDrawer: boolean
    mode: ThemeMode
    onChangeContainer: () => void
    onChangeDirection: (direction: ThemeDirection) => void
    onChangeFontFamily: (fontFamily: FontFamily) => void
    onChangeLocalization: (lang: 'en' | 'es') => void
    onChangeMenuOrientation: (menuOrientation: MenuOrientation) => void
    onChangeMiniDrawer: (miniDrawer: boolean) => void
    onChangeMode: (mode: ThemeMode) => void
    onChangePresetColor: (theme: PresetColor) => void
    presetColor: PresetColor
    themeDirection: ThemeDirection
  }
>({
  ...resConfig,
  onChangeContainer: () => {},
  onChangeDirection: () => {},
  onChangeFontFamily: () => {},
  onChangeLocalization: () => {},
  onChangeMenuOrientation: () => {},
  onChangeMiniDrawer: () => {},
  onChangeMode: () => {},
  onChangePresetColor: () => {}
})
export const ResourceProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [config, setConfig] = useLocalStorage<ResourcesConfig>(
    'local-store',
    resConfig,
    { initializeWithValue: false }
  )

  const theme = useTemplateTheme(
    config.fontFamily,
    config.mode,
    config.themeDirection
  )

  const { message, severity, show } = alertStore.use()

  return (
    <ResourceContext.Provider
      value={{
        ...config,
        onChangeContainer: () =>
          setConfig({ ...config, container: !config.container }),
        onChangeDirection: direction =>
          setConfig({ ...config, themeDirection: direction }),
        onChangeFontFamily: fontFamily => setConfig({ ...config, fontFamily }),
        onChangeLocalization: lang => setConfig({ ...config, i18n: lang }),
        onChangeMenuOrientation: layout =>
          setConfig({ ...config, menuOrientation: layout }),
        onChangeMiniDrawer: miniDrawer => setConfig({ ...config, miniDrawer }),
        onChangeMode: mode => setConfig({ ...config, mode }),
        onChangePresetColor: theme =>
          setConfig({ ...config, presetColor: theme })
      }}
    >
      <StyledEngineProvider injectFirst>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <OverlayAlert
            message={message ?? undefined}
            open={show}
            severity={severity ?? undefined}
          />
          {children}
        </ThemeProvider>
      </StyledEngineProvider>
    </ResourceContext.Provider>
  )
}
