/* eslint-disable unicorn/no-keyword-prefix */
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type OtrosSaldos,
  otrosSaldos,
  otrosSaldosDefault,
  otrosSaldosMetadata
} from '@d/admin/models/otros-saldos'
import { OtrosSaldosRepository } from '@d/admin/repositories/otros-saldos-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteOtrosSaldos,
    getOtrosSaldos,
    insertOtrosSaldos,
    updateOtrosSaldos
  } = container.get(OtrosSaldosRepository)

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)

  const selectedData = useRef(otrosSaldosDefault)
  const selectedIndex = useRef<number | null>(0)

  const {
    data: otrosSaldosData,
    isFetching: isOtrosSaldosPending,
    refetch: refetchOtrosSaldos
  } = useQuery({
    queryFn: getOtrosSaldos,
    queryKey: ['getOtrosSaldos']
  })

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteOtrosSaldos(selectedData.current.osid.toString())
      setLoadingDialog(false)
      void refetchOtrosSaldos()
    }
  }

  const submitAction = async (
    oo: OtrosSaldos,
    _: FormikHelpers<OtrosSaldos>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)

    await (selectedIndex.current === null
      ? insertOtrosSaldos(oo)
      : updateOtrosSaldos(selectedData.current.osid.toString(), oo))

    setLoadingDialog(false)
    void refetchOtrosSaldos()
  }

  return isOtrosSaldosPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchOtrosSaldos()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = otrosSaldosDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Grid item xs={12}>
        <Paper
          elevation={1}
          sx={{ ...paperStyle, maxHeight: '400px', overflowY: 'auto' }}
        >
          <GenericTable
            data={otrosSaldosData ?? []}
            entries={otrosSaldos}
            isDeleteAction
            isEditAction
            metadata={otrosSaldosMetadata}
            onDeleteClick={index => {
              selectedIndex.current = index
              setOpenDeleteDialog(true)
            }}
            onEditClick={index => {
              selectedIndex.current = index
              selectedData.current =
                nth(index, otrosSaldosData ?? []) ?? otrosSaldosDefault
              setOpenCreateEditDialog(true)
            }}
            tipoPercent
          />
        </Paper>
      </Grid>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog open={loadingDialog} />
      <DialogForm<OtrosSaldos>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          fecha: Yup.mixed<Dayjs>().required('Campo Requerido'),
          producto: Yup.string().required('Campo Requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<OtrosSaldos>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<OtrosSaldos>
              field='evento'
              label='Evento:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.evento.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='saldo'
              label='Saldo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.saldo.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='producto'
              label='Producto:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.producto.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='tipo'
              label='Tipo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.tipo.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='balance'
              label='Balance:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.balance.toString()}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
