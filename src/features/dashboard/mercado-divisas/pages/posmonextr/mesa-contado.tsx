import { FileExcelOutlined } from '@ant-design/icons'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import {
  mesaContado,
  mesaContadoMetadata
} from '@d/mercado-divisas/models/mesa-contado'
import { MercadoDivisasRepository } from '@d/mercado-divisas/repository/mercado-divisas-repository'
import { Button, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { exportToCSV } from '~/utils/csv'

export default () => {
  const { getMesaContado } = container.get(MercadoDivisasRepository)

  const day = calendarStore.actualDay.use()

  const {
    data: getMesaContadoData,
    error: getMesaContadoError,
    isError: isGetMesaContadoError,
    isFetching: isGetMesaContadoPending,
    refetch: refetchGetMesaContado
  } = useQuery({
    queryFn: getMesaContado,
    queryKey: ['getMesaContado']
  })

  useEffect(() => {
    void refetchGetMesaContado()
  }, [day])

  return (
    <Fetching
      errorMessage={getMesaContadoError?.message}
      fullHeight
      isError={isGetMesaContadoError}
      isFetching={isGetMesaContadoPending}
    >
      <Reload onClick={() => refetchGetMesaContado()} />
      <Paper elevation={10} sx={paperStyle}>
        <GenericTable
          data={getMesaContadoData ?? []}
          entries={mesaContado}
          metadata={mesaContadoMetadata}
        />
        <Grid container spacing={1}>
          <Grid item sx={{ display: 'flex', justifyContent: 'start' }} xs={3}>
            <Button
              onClick={() =>
                exportToCSV(getMesaContadoData ?? [], 'users.csv', ';')
              }
              startIcon={<FileExcelOutlined />}
            />
          </Grid>
        </Grid>
      </Paper>
    </Fetching>
  )
}
