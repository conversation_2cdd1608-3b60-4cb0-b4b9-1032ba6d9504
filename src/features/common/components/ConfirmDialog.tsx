import { CloseCircleOutlined, DownloadOutlined } from '@ant-design/icons'
import { Button, Dialog, DialogActions, DialogTitle } from '@mui/material'
import type { FC } from 'react'

export const ConfirmDialog: FC<{
  onClick: (isOk: boolean) => void
  open: boolean
  title?: string
}> = ({ onClick, open, title = '¿Está seguro de actualizar el sistema?' }) => (
  <Dialog
    aria-describedby='edit-dialog-description'
    aria-labelledby='edit-dialog-title'
    onClose={() => onClick(false)}
    open={open}
  >
    <DialogTitle id='edit-dialog-title'>{title}</DialogTitle>
    <DialogActions>
      <Button
        onClick={() => onClick(false)}
        startIcon={<CloseCircleOutlined />}
        variant='outlined'
      >
        Cancelar
      </Button>
      <Button
        autoFocus
        onClick={() => onClick(true)}
        startIcon={<DownloadOutlined />}
        variant='contained'
      >
        Aceptar
      </Button>
    </DialogActions>
  </Dialog>
)
