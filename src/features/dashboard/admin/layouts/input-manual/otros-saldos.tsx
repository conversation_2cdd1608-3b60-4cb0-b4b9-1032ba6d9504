/* eslint-disable sonarjs/void-use */
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

const route = '/admin/input-manual/otros-saldos'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/banco-espana`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/exceso`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/otros`)
        setMenuId(2)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('banco-espana')) setMenuId(0)
    if (path.includes('exceso')) setMenuId(1)
    if (path.includes('otros')) setMenuId(2)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Banco de España</MenuItem>
                  <MenuItem value={1}>Banco de España Exceso</MenuItem>
                  <MenuItem value={2}>Otros Saldos</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Outlet />
      </Grid>
    </Container>
  )
}
