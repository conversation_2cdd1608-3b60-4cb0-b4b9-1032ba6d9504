/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const controlDPVRV = type({
  day_PL_Total: 'number',
  month_PL_Total: 'number',
  titulo: 'string',
  valorMercado: 'number',
  year_PL_Total: 'number'
})

export const controlDPVRVMetadata = new Set([
  {
    header: 'Concepto',
    key: 'titulo'
  },
  {
    header: 'Valor Mercado',
    key: 'valorMercado'
  },
  {
    header: 'Día €',
    key: 'day_PL_Total'
  },
  {
    header: 'Mes €',
    key: 'month_PL_Total'
  },
  {
    header: 'Año €',
    key: 'year_PL_Total'
  }
])

export type ControlDPVRV = typeof controlDPVRV.infer

export const controlDPVRVValidate = (attempt?: ControlDPVRV) =>
  controlDPVRV(attempt) instanceof type.errors
    ? resetObject(controlDPVRV)
    : attempt
