import type { EventInput } from '@fullcalendar/common'

export type CalendarProps = {
  actualDay: string
  available: boolean | null
  calendarView: CalendarView
  error: boolean
  events: EventInput[]
  isLoader: boolean
  isModalOpen: boolean
  selectedEventId: string | null
  selectedRange: { end: Date; start: Date } | null
}
export type CalendarView =
  | 'dayGridMonth'
  | 'listWeek'
  | 'timeGridDay'
  | 'timeGridWeek'

export type DateRange = { end: Date | number; start: Date | number }
