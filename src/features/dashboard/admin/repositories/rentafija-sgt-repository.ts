/* eslint-disable sonarjs/use-type-alias */
import type { RentafijaSgt } from '@d/admin/models/rentafija-sgt'
import type { SGTGroups } from '@d/admin/models/sgt-groups'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class RentafijaSGTRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/rentafijasgt'

  readonly deleteRentaFijaSGT = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getRentaFijaSGT = (fecha: string) =>
    this.api.client
      .get<RentafijaSgt[]>(`${this.route}/${fecha}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getRentaFijaSGTGroups = (fecha: string, referencia: string) =>
    this.api.client
      .get<SGTGroups[]>(
        `${this.route}/${fecha}/${referencia === '' ? 'NULL' : referencia}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertRentaFijaSGT = (rt: RentafijaSgt) =>
    this.api.client.post<{ message: string }>(this.route, undefined, {
      params: rt
    })

  readonly updateRentaFijaSGT = (rt: RentafijaSgt) =>
    this.api.client.put<{ message: string }>(this.route, undefined, {
      params: rt
    })
}
