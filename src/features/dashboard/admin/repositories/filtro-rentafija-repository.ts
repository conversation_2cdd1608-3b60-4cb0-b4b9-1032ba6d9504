/* eslint-disable sonarjs/use-type-alias */
import type { FiltroRentaFija } from '@d/admin/models/filtro-rentafija'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class FiltroRentaFijaRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/filtrorentafija'

  readonly deleteFiltroRentaFija = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getFiltroRentaFija = () =>
    this.api.client
      .get<FiltroRentaFija[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertFiltroRentaFija = (filtroRentaFija: FiltroRentaFija) =>
    this.api.client.post<{ message: string }>(this.route, filtroRentaFija)

  readonly updateFiltroRentaFija = (
    id: string,
    filtroRentaFija: FiltroRentaFija
  ) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      filtroRentaFija
    )
}
