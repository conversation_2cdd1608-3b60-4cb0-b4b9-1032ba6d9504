import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

import type {
  VarNewAreas,
  VarNewAreasProductoFactor
} from '../models/var-newareas'

@injectable()
export class InverNegociaRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/vararea'

  readonly getVarNewAreas = (
    portfolio: 'Inversion' | 'Negociacion',
    areaName: string
  ) =>
    this.api.client
      .get<VarNewAreas[]>(
        `${this.route}/varnewareas/${this.attachDate()}/${portfolio}/${areaName}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getVarNewAreasProductoFactor = (
    portfolio: 'Inversion' | 'Negociacion',
    areaName: string,
    factorProducto: 'Factor' | 'Producto'
  ) =>
    this.api.client
      .get<VarNewAreasProductoFactor[]>(
        `${this.route}/varnewareasproductofactor/${this.attachDate()}/${portfolio}/${areaName}/${factorProducto}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
