import { ButtonBase } from '@mui/material'
import type { SxProps } from '@mui/system'
import { type FC, Suspense } from 'react'
import { Link } from 'react-router'

import { APP_DEFAULT_PATH } from '~/constants'

const LogoIcon = lazy(() =>
  import('./LogoIcon').then(comp => ({
    default: comp.LogoIcon
  }))
)

const LogoMain = lazy(() =>
  import('./LogoMain').then(comp => ({
    default: comp.LogoMain
  }))
)

export const LogoIndex: FC<{
  isIcon?: boolean
  reverse?: boolean
  sx?: SxProps
  to?: string
}> = ({ isIcon = false, reverse: _, sx, to }) => (
  <ButtonBase
    component={Link}
    disableRipple
    sx={sx}
    to={to ?? APP_DEFAULT_PATH}
  >
    {isIcon ? (
      <Suspense fallback={<div></div>}>
        <LogoIcon />
      </Suspense>
    ) : (
      <Suspense fallback={<div></div>}>
        <LogoMain />
      </Suspense>
    )}
  </ButtonBase>
)
