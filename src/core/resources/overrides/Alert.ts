import { alpha, type Theme } from '@mui/material/styles'

import { getColors } from '~/resources/helpers/get-colors'
import type { ExtendedStyleProps } from '~/types/extended'

const getColorStyle = ({ color, theme }: ExtendedStyleProps) => {
  const { light, lighter, main } = getColors(theme, color)

  return {
    '& .MuiAlert-icon': {
      color: main
    },
    backgroundColor: lighter,
    borderColor: alpha(light, 0.5)
  }
}

export const Alert = (theme: Theme) => {
  const primaryDashed = getColorStyle({ color: 'primary', theme })

  return {
    MuiAlert: {
      styleOverrides: {
        action: {
          '& .MuiButton-root': {
            fontSize: '0.75rem',
            height: 'auto',
            marginTop: -2,
            padding: 2
          },
          '& .MuiIconButton-root': {
            '& .MuiSvgIcon-root': {
              fontSize: '1rem'
            },
            height: 'auto',
            marginRight: 6,
            padding: 2,
            width: 'auto'
          }
        },
        border: {
          border: '1px solid',
          padding: '10px 16px',
          ...primaryDashed,
          '&.MuiAlert-borderError': getColorStyle({ color: 'error', theme }),
          '&.MuiAlert-borderInfo': getColorStyle({ color: 'info', theme }),
          '&.MuiAlert-borderPrimary': getColorStyle({
            color: 'primary',
            theme
          }),
          '&.MuiAlert-borderSecondary': getColorStyle({
            color: 'secondary',
            theme
          }),
          '&.MuiAlert-borderSuccess': getColorStyle({
            color: 'success',
            theme
          }),
          '&.MuiAlert-borderWarning': getColorStyle({ color: 'warning', theme })
        },
        filled: {
          color: theme.palette.grey[0]
        },
        icon: {
          fontSize: '1rem'
        },
        message: {
          marginTop: 3,
          padding: 0
        },
        root: {
          color: theme.palette.text.primary,
          fontSize: '0.875rem'
        }
      }
    }
  }
}
