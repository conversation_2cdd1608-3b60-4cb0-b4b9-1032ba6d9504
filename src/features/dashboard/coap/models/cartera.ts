/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const cartera = type({
  dayTotal: 'number',
  month_Realised: 'number',
  monthTotal: 'number',
  realised: 'number',
  tradeGroup: 'string',
  year_Realised: 'number',
  yearTotal: 'number'
})

export const carteraMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Día',
    key: 'dayTotal'
  },
  {
    header: 'Mes',
    key: 'monthTotal'
  },
  {
    header: 'Año',
    key: 'yearTotal'
  }
])

export type Cartera = typeof cartera.infer

export const carteraValidate = (attempt?: Cartera) =>
  cartera(attempt) instanceof Error ? resetObject(cartera.infer) : attempt
