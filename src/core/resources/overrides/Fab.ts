/* eslint-disable unicorn/no-useless-switch-case */
import { alpha, type Theme } from '@mui/material/styles'

import { getColors } from '~/resources/helpers/get-colors'
import { getShadow } from '~/resources/helpers/get-shadow'
import type { ExtendedStyleProps } from '~/types/extended'

const getColorStyle = ({ color, theme }: ExtendedStyleProps) => {
  const { contrastText, dark, main } = getColors(theme, color)

  // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
  const buttonShadow = `${color}Button`
  const shadows = getShadow(theme, buttonShadow)

  return {
    '&::after': {
      borderRadius: '50px',
      boxShadow: `0 0 5px 5px ${alpha(main, 0.9)}`
    },
    '&:active::after': {
      borderRadius: '50px',
      boxShadow: `0 0 0 0 ${alpha(main, 0.9)}`
    },
    '&:focus-visible': {
      outline: `2px solid ${dark}`,
      outlineOffset: 2
    },
    '&:hover': {
      backgroundColor: dark,
      boxShadow: 'none'
    },
    backgroundColor: main,
    boxShadow: shadows,
    color: contrastText
  }
}

export const Button = (theme: Theme) => ({
  MuiFab: {
    styleOverrides: {
      root: {
        '&.Mui-disabled': {
          backgroundColor: theme.palette.grey[200]
        },
        '&.Mui-error': getColorStyle({ color: 'error', theme }),
        '&.MuiFab-info': getColorStyle({ color: 'info', theme }),
        '&.MuiFab-primary': getColorStyle({ color: 'primary', theme }),
        '&.MuiFab-secondary': getColorStyle({ color: 'secondary', theme }),
        '&.MuiFab-success': getColorStyle({ color: 'success', theme }),
        '&.MuiFab-warning': getColorStyle({ color: 'warning', theme }),
        '&::after': {
          borderRadius: 4,
          content: '""',
          display: 'block',
          height: '100%',
          left: 0,
          opacity: 0,
          position: 'absolute',
          top: 0,
          transition: 'all 0.5s',
          width: '100%'
        },
        '&:active::after': {
          borderRadius: 4,
          left: 0,
          opacity: 1,
          position: 'absolute',
          top: 0,
          transition: '0s'
        },

        fontWeight: 400
      }
    }
  }
})
