/* eslint-disable sonarjs/use-type-alias */
import type { Subinformes } from '@d/admin/models/subinformes'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class SubinformesRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/tradegroupsbreakdown'

  readonly deleteSubinformes = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getSubinformes = () =>
    this.api.client
      .get<Subinformes[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertSubinformes = (portfolSubinformes: Subinformes) =>
    this.api.client.post<{ message: string }>(this.route, portfolSubinformes)

  readonly updateSubinformes = (id: string, portfolSubinformes: Subinformes) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      portfolSubinformes
    )
}
