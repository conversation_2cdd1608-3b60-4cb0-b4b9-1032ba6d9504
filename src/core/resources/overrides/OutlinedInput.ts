import type { Theme } from '@mui/material/styles'

import { getColors } from '~/resources/helpers/get-colors'
import { getShadow } from '~/resources/helpers/get-shadow'
import type { ColorProps } from '~/types/extended'
import { ThemeMode } from '~/types/resources'

const getColor = ({
  theme,
  variant
}: {
  theme: Theme
  variant: ColorProps
}) => {
  const { light } = getColors(theme, variant)

  // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
  const shadows = getShadow(theme, `${variant}`)

  return {
    '&.Mui-focused': {
      '& .MuiOutlinedInput-notchedOutline': {
        border: `1px solid ${light}`
      },
      boxShadow: shadows
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: light
    }
  }
}

export const OutlinedInput = (theme: Theme) => ({
  MuiOutlinedInput: {
    styleOverrides: {
      colorError: getColor({ theme, variant: 'error' }),
      colorInfo: getColor({ theme, variant: 'info' }),
      colorSecondary: getColor({ theme, variant: 'secondary' }),
      colorSuccess: getColor({ theme, variant: 'success' }),
      colorWarning: getColor({ theme, variant: 'warning' }),
      input: {
        padding: '10.5px 14px 10.5px 12px'
      },
      inputMultiline: {
        padding: 0
      },
      inputSizeSmall: {
        padding: '7.5px 8px 7.5px 12px'
      },
      notchedOutline: {
        borderColor:
          theme.palette.mode === ThemeMode.DARK
            ? theme.palette.grey[200]
            : theme.palette.grey[300]
      },
      root: {
        ...getColor({ theme, variant: 'primary' }),
        '&.Mui-error': {
          ...getColor({ theme, variant: 'error' })
        }
      }
    }
  }
})
