import { store } from '@davstack/store'

import type { Auth, UserProfile } from '~/types/auth'

export const authStore = store<Auth>({
  isInitialized: false,
  isLoggedIn: false,
  user: null
}).actions(store => ({
  login: (user: Partial<UserProfile>) => {
    store.isLoggedIn.set(true)
    store.isInitialized.set(true)
    store.user.set(user)
  },
  logout: () => {
    store.isLoggedIn.set(false)
    store.isInitialized.set(true)
    store.user.set(null)
  }
}))
