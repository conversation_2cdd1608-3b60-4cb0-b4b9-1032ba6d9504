import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Parametros,
  parametros,
  parametrosDefault,
  parametrosMetadata
} from '@d/admin/models/parametros'
import { ParametrosRepository } from '@d/admin/repositories/parametros-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteParametros,
    getParametros,
    insertParametros,
    updateParametros
  } = container.get(ParametrosRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(parametrosDefault)

  const {
    data: allParametrosData,
    isFetching: isGetParametrosPending,
    refetch: refetchParametros
  } = useQuery({
    queryFn: getParametros,
    queryKey: ['getParametros']
  })

  const submitAction = async (
    incoming: Parametros,
    _: FormikHelpers<Parametros>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertParametros(incoming)
      : updateParametros(
          selectedData.current.paramID?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchParametros()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteParametros(selectedData.current.paramID?.toString() ?? '0')
      setLoadingDialog(false)
      void refetchParametros()
    }
  }

  return isGetParametrosPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchParametros()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = parametrosDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allParametrosData ?? []}
                entries={parametros}
                isDeleteAction
                isEditAction
                metadata={parametrosMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allParametrosData ?? []) ?? parametrosDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Parametros>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          nombre: Yup.string().max(5).required('Nombre is required'),
          orden: Yup.number().required('Orden is required'),
          titulo: Yup.string().max(25).required('Titulo is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Parametros>
              field='formatoSaldos'
              label='Formato: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.formatoSaldos?.toString() ?? '0'}
            />
            <TextFormControl<Parametros>
              field='ddbb'
              label='DDBB: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.ddbb?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='servidor'
              label='Servidor: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.servidor?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='usuario'
              label='Usuario: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.usuario?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='password'
              label='Password: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.password?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='tradeGroup'
              label='Trade group: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.password?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='tradeGroup'
              label='Trade group pool: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroupPool?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='costOfCarryIndex'
              label='Cost of carry index: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroupPool?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='poolProductID'
              label='Producto By Pool Product Id: '
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.poolProductID?.toString() ?? '0'}
            />
            <TextFormControl<Parametros>
              field='callMoney'
              label='Call money: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroupPool?.toString() ?? ''}
            />
            <TextFormControl<Parametros>
              field='rrcc'
              label='RRCC: '
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.rrcc?.toString() ?? '0'}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
