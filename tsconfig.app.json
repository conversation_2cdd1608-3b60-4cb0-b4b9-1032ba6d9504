{"compilerOptions": {"allowUnreachableCode": false, "allowUnusedLabels": false, "alwaysStrict": true, "exactOptionalPropertyTypes": false, "noImplicitAny": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noUnusedParameters": true, "noUnusedLocals": true, "strict": true, "strictNullChecks": true, "strictPropertyInitialization": true, "useUnknownInCatchVariables": true}}