/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const patrimonioNeto = type({
  cartera: 'string',
  patrimonioNeto: 'number'
})

export const patrimonioNetoMetadata = new Set([
  {
    header: 'Carter<PERSON>',
    key: 'cartera'
  },
  {
    header: 'Patrimonio Neto',
    key: 'patrimonioNeto'
  }
])

export type PatrimonioNeto = typeof patrimonioNeto.infer

export const patrimonioNetoValidate = (attempt?: PatrimonioNeto) =>
  patrimonioNeto(attempt) instanceof type.errors
    ? resetObject(patrimonioNeto)
    : attempt
