/* eslint-disable sonarjs/no-all-duplicated-branches */
import { TranslationOutlined } from '@ant-design/icons'
import { IconButton } from '@c/components/IconButton'
import { Transitions } from '@c/components/Transitions'
import {
  Box,
  ClickAwayListener,
  Grid,
  List,
  ListItemButton,
  ListItemText,
  Paper,
  Popper,
  Typography,
  useMediaQuery
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { useRef, useState } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { ThemeMode } from '~/types/resources'

export const Localization = () => {
  const theme = useTheme()
  const matchesXs = useMediaQuery(theme.breakpoints.down('md'))

  const { i18n, onChangeLocalization } = useContext(ResourceContext)

  const anchorRef = useRef<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState(false)
  const handleToggle = () => {
    setOpen(prevOpen => !prevOpen)
  }

  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current?.contains(event.target as Node) ?? false) return

    setOpen(false)
  }

  const handleListItemClick = (lang: 'en' | 'es') => {
    onChangeLocalization(lang)
    setOpen(false)
  }

  const iconBackColorOpen =
    theme.palette.mode === ThemeMode.DARK ? 'grey.200' : 'grey.300'
  const iconBackColor =
    theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'grey.100'
  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <IconButton
        aria-controls={open ? 'localization-grow' : undefined}
        aria-haspopup='true'
        aria-label='open localization'
        color='secondary'
        onClick={handleToggle}
        ref={anchorRef}
        sx={{
          bgcolor: open ? iconBackColorOpen : iconBackColor,
          color: 'text.primary'
        }}
        variant='light'
      >
        <TranslationOutlined />
      </IconButton>
      <Popper
        anchorEl={anchorRef.current}
        disablePortal
        open={open}
        placement={matchesXs ? 'bottom-start' : 'bottom'}
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [matchesXs ? 0 : 0, 9]
              }
            }
          ]
        }}
        role={undefined}
        transition
      >
        {({ TransitionProps }) => (
          <Transitions
            in={open}
            position={matchesXs ? 'top-right' : 'top'}
            type='grow'
            {...TransitionProps}
          >
            <Paper sx={{ boxShadow: theme.customShadows.z1 }}>
              <ClickAwayListener onClickAway={handleClose}>
                <List
                  component='nav'
                  sx={{
                    bgcolor: theme.palette.background.paper,
                    borderRadius: 0.5,
                    maxWidth: 290,
                    minWidth: 200,
                    p: 0,
                    [theme.breakpoints.down('md')]: {
                      maxWidth: 250
                    },
                    width: '100%'
                  }}
                >
                  <ListItemButton
                    onClick={() => handleListItemClick('en')}
                    selected={i18n === 'en'}
                  >
                    <ListItemText
                      primary={
                        <Grid container>
                          <Typography color='textPrimary'>English</Typography>
                          <Typography
                            color='textSecondary'
                            sx={{ ml: '8px' }}
                            variant='caption'
                          >
                            (UK)
                          </Typography>
                        </Grid>
                      }
                    />
                  </ListItemButton>
                </List>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  )
}
