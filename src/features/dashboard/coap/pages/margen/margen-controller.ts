import type { ActivoPasivo } from '@d/common/models/activo-pasivo'
import { ActivoPasivoRepository } from '@d/common/repository/activo-pasivo-repository'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'
import { useEffect, useRef, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const margenController = () => {
  const {
    desgloseMFDerivadoCobertura,
    desgloseProductoActivo,
    desgloseProductoPasivo,
    getActivo,
    getPasivo,
    showTradeGroup
  } = container.get(ActivoPasivoRepository)

  const day = calendarStore.actualDay.use()

  const [balance, setBalance] = useState('Activo')

  const [openDialogActivo1, setOpenDialogActivo1] = useState(false)
  const [openDialogActivo2, setOpenDialogActivo2] = useState(false)
  const [openDialogPasivo1, setOpenDialogPasivo1] = useState(false)
  const [openDialogPasivo2, setOpenDialogPasivo2] = useState(false)
  const [openDialogActivo3, setOpenDialogActivo3] = useState(false)

  const selectedActivoPasivo = useRef<{
    activoPasivo: boolean
    activoPasivoData: ActivoPasivo[]
    rowIndex: number
  } | null>(null)

  const selectedDesgloseProducto = useRef<{
    activoPasivo: boolean
    rowIndex: number
  } | null>(null)

  const selectedDesgloseDerivado = useRef<number | null>(null)

  const {
    data: activoData,
    error: activoError,
    isError: isActivoError,
    isFetching: isActivoPending,
    refetch: refetchActivo
  } = useQuery({
    queryFn: () => getActivo('4'),
    queryKey: ['getActivoCOAP']
  })

  const {
    data: pasivoData,
    error: pasivoError,
    isError: isPasivoError,
    isFetching: isPasivoPending,
    refetch: refetchPasivo
  } = useQuery({
    queryFn: () => getPasivo('4'),
    queryKey: ['getPasivoCOAP']
  })

  const {
    data: dialog1Data,
    error: dialog1Error,
    isError: isDialog1Error,
    isFetching: isDialog1Pending,
    refetch: refetchDialog1
  } = useQuery({
    enabled: !!selectedActivoPasivo.current,
    queryFn: async () => {
      if (selectedActivoPasivo.current === null) return []
      const { activoPasivo, activoPasivoData, rowIndex } =
        selectedActivoPasivo.current
      const activoPasivoRow = nth(rowIndex, activoPasivoData)
      const balance = activoPasivo
        ? 'Activo'
        : (activoPasivoRow?.balance ?? 'Pasivo')
      setBalance(balance)
      return showTradeGroup(
        balance,
        '4',
        activoPasivoRow?.epigrafeID?.toString() ?? ''
      ).then(data => {
        activoPasivo ? setOpenDialogActivo1(true) : setOpenDialogPasivo1(true)
        return data
      })
    },
    queryKey: ['showTradeGroupCOAP']
  })

  const {
    data: dialog2Data,
    error: dialog2Error,
    isError: isDialog2Error,
    isFetching: isDialog2Pending,
    refetch: refetchDialog2
  } = useQuery({
    enabled: !!selectedDesgloseProducto.current,
    queryFn: async () => {
      if (selectedDesgloseProducto.current === null) return []
      if (dialog1Data === undefined) return []

      const { activoPasivo, rowIndex } = selectedDesgloseProducto.current
      const desgloseProductoRow = nth(rowIndex, dialog1Data)
      const producto = desgloseProductoRow?.producto ?? ''
      const tradeGroup = desgloseProductoRow?.tradeGroup ?? ''

      return activoPasivo
        ? desgloseProductoActivo(producto, balance, tradeGroup).then(data => {
            setOpenDialogActivo2(true)
            return data.map(el => ({
              ...el,
              expiryDate: el.expiryDate.split('T')[0] ?? '',
              startDateAdj: el.startDateAdj.split('T')[0] ?? ''
            }))
          })
        : desgloseProductoPasivo(producto, balance, tradeGroup).then(data => {
            setOpenDialogPasivo2(true)
            return data.map(el => ({
              ...el,
              expiryDate: el.expiryDate.split('T')[0] ?? '',
              startDateAdj: el.startDateAdj.split('T')[0] ?? ''
            }))
          })
    },
    queryKey: ['desgloseProductoCOAP']
  })

  const {
    data: dialog3Data,
    error: dialog3Error,
    isError: isDialog3Error,
    isFetching: isDialog3Pending,
    refetch: refetchDialog3
  } = useQuery({
    enabled: selectedDesgloseDerivado.current != null,
    queryFn: async () => {
      if (selectedDesgloseDerivado.current === null) return []
      if (dialog2Data === undefined) return []

      const desgloseRow = nth(selectedDesgloseDerivado.current, dialog2Data)
      return desgloseMFDerivadoCobertura(
        desgloseRow?.instrumentID ?? '',
        '4'
      ).then(data => {
        setOpenDialogActivo3(true)
        return data
      })
    },
    queryKey: ['desgloseMFDerivadoCoberturaCOAP']
  })

  const onRowActivoPasivo1 = (
    activoPasivoData: ActivoPasivo[],
    rowIndex: number,
    activoPasivo: boolean
  ) => {
    selectedActivoPasivo.current = {
      activoPasivo,
      activoPasivoData,
      rowIndex
    }
    void refetchDialog1()
  }

  const onRowActivoPasivo2 = (rowIndex: number, activoPasivo: boolean) => {
    selectedDesgloseProducto.current = {
      activoPasivo,
      rowIndex
    }
    void refetchDialog2()
  }

  const onRowActivo3 = (rowIndex: number) => {
    selectedDesgloseDerivado.current = rowIndex
    void refetchDialog3()
  }

  const refetch = () => {
    selectedDesgloseDerivado.current = null
    selectedDesgloseProducto.current = null
    selectedActivoPasivo.current = null
    void refetchPasivo()
    void refetchActivo()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return {
    activoData,
    activoError,
    dialog1Data,
    dialog1Error,
    dialog2Data,
    dialog2Error,
    dialog3Data,
    dialog3Error,
    isActivoError,
    isActivoPending,
    isDialog1Error,
    isDialog1Pending,
    isDialog2Error,
    isDialog2Pending,
    isDialog3Error,
    isDialog3Pending,
    isPasivoError,
    isPasivoPending,
    onRowActivo3,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogActivo3,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    pasivoError,
    refetch,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogActivo3,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  }
}
