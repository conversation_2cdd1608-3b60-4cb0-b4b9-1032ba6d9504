import { <PERSON>ario } from '@d/riesgo-mercado/models/diario'
import { Historico } from '@d/riesgo-mercado/models/historico'
import { Incumplimientos } from '@d/riesgo-mercado/models/incumplimientos'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class InitialMargenRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/initialmargin'

  readonly getHistorico = (contrapartida: string) =>
    this.api.client
      .get<Historico[]>(
        `${this.route}/backTesting/${this.attachDate()}/${contrapartida}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getIncumplimientos = (contrapartida: string) =>
    this.api.client
      .get<Incumplimientos[]>(
        `${this.route}/semaforo/${this.attachDate()}/${contrapartida}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getPost = () =>
    this.api.client
      .get<Diario[]>(`${this.route}/post/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getReceived = () =>
    this.api.client
      .get<Diario[]>(`${this.route}/received/${this.attachDate()}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
