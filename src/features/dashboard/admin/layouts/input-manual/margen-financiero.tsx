/* eslint-disable unicorn/no-keyword-prefix */
import { SearchOutlined } from '@ant-design/icons'
import type { MargenFinancieroInforme } from '@d/admin/models/margen-financiero-informe'
import { MargenFinancieroRepository } from '@d/admin/repositories/margen-financiero-repository'
import { TradeGroupRepository } from '@d/admin/repositories/tradegroup-repository'
import { Loading } from '@d/common/components/Loading'
import { useDebounce } from '@d/common/hooks/useDebounce'
import {
  Box,
  Button,
  ButtonGroup,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  LinearProgress,
  Link,
  MenuItem,
  OutlinedInput,
  Select,
  styled
} from '@mui/material'
import { yellow } from '@mui/material/colors'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import type { FC, JSX } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { ThemeMode } from '~/types/resources'

const ButtonHover = styled(Button)(() => ({
  '&:hover': {
    backgroundColor: yellow[300]
  }
}))

const handleMenuChange = (setMenuId: (data: number) => void, ind: number) =>
  ind > 0 ? setMenuId(ind) : setMenuId(0)

enum AreasMargen {
  act = 'Activo',
  fb = 'Fuera de Balance',
  pas = 'Pasivo'
}

export const MargenFinancieroAjustes: FC<{
  children: (props: {
    date: Dayjs
    isInforme: boolean
    isMargenFinancieroAjustesPending: boolean
    margenFinancieroAjustesData: MargenFinancieroInforme[]
    margenFinancieroInformeData: MargenFinancieroInforme[]
    refetch: () => void
  }) => JSX.Element
}> = ({ children }) => {
  const day = calendarStore.formattedDay.use()

  const [date, setDate] = useState<Dayjs | null>(dayjs(day))
  const { mode } = useContext(ResourceContext)

  const [linkSelected, setLinkSelected] = useState(true)
  const { debVal: trade, setDebVal: setTrade } = useDebounce()
  const [menuIdAreas, setMenuIdAreas] = useState(0)
  const [menuIdProductos, setMenuIdProductos] = useState(0)
  const [activeBalance, setActiveBalance] = useState(AreasMargen.act)

  const { getAreasTitulo, getProductosDesc } =
    container.get(TradeGroupRepository)
  const { getMargenFinancieroAjustes, getMargenFinancieroInforme } =
    container.get(MargenFinancieroRepository)

  const selectedStyle = {
    backgroundColor: mode === ThemeMode.LIGHT ? 'primary.lighter' : 'todo'
  }

  const { data: areasData, isFetching: isAreasPending } = useQuery({
    queryFn: getAreasTitulo,
    queryKey: ['getAreasAjustes']
  })

  const { data: productosData, isFetching: isProductosPending } = useQuery({
    queryFn: getProductosDesc,
    queryKey: ['getProductosAjustes']
  })

  const {
    data: margenFinancieroInformeData,
    isFetching: isMargenFinancieroInformePending,
    refetch: refetchmargenFinancieroInforme
  } = useQuery({
    queryFn: () =>
      getMargenFinancieroInforme(
        date?.format('YYYY-MM-DD') ?? day,
        areasData?.[menuIdAreas - 1] ?? '',
        activeBalance,
        trade,
        productosData?.[menuIdProductos - 1] ?? ''
      ),
    queryKey: ['getmargenFinancieroInforme']
  })

  const {
    data: margenFinancieroAjustesData,
    isFetching: isMargenFinancieroAjustesPending,
    refetch: refetchmargenFinancieroAjustes
  } = useQuery({
    queryFn: () =>
      getMargenFinancieroAjustes(date?.format('YYYY-MM-DD') ?? day),
    queryKey: ['getmargenFinancieroAjustes']
  })

  const refetch = () => {
    void refetchmargenFinancieroInforme()
    void refetchmargenFinancieroAjustes()
  }

  useEffect(() => {
    refetch()
  }, [activeBalance, date, menuIdAreas, menuIdProductos, trade, linkSelected])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 2 }}>
      {(isAreasPending || isProductosPending) && (
        <LinearProgress sx={{ mb: 2 }} />
      )}
      <Grid container spacing={3}>
        <Grid item xs={4}>
          <ButtonGroup variant='outlined'>
            <ButtonHover
              onClick={() => setActiveBalance(AreasMargen.act)}
              sx={activeBalance === AreasMargen.act ? selectedStyle : {}}
            >
              Activo
            </ButtonHover>
            <ButtonHover
              onClick={() => setActiveBalance(AreasMargen.pas)}
              sx={activeBalance === AreasMargen.pas ? selectedStyle : {}}
            >
              Pasivo
            </ButtonHover>
            <ButtonHover
              onClick={() => setActiveBalance(AreasMargen.fb)}
              sx={activeBalance === AreasMargen.fb ? selectedStyle : {}}
            >
              Fuera de balance
            </ButtonHover>
          </ButtonGroup>
        </Grid>
        <Grid item xs={2}>
          <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
            <DatePicker
              defaultValue={date}
              onChange={(newValue: dayjs.Dayjs | null) =>
                newValue ? setDate(newValue) : setDate(dayjs())
              }
              slotProps={{ textField: { size: 'medium' } }}
              value={date}
            />
          </LocalizationProvider>
        </Grid>
        <Grid
          item
          sx={{ display: 'flex', justifyContent: 'space-evenly' }}
          xs={6}
        >
          <Box>
            <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
              <OutlinedInput
                endAdornment={
                  <InputAdornment position='end' sx={{ mr: -0.5 }}>
                    <SearchOutlined />
                  </InputAdornment>
                }
                id='my-label-id'
                onChange={e => setTrade(e.target.value)}
                placeholder='Trade Group'
              />
            </FormControl>
          </Box>
          {!(isAreasPending || isProductosPending) && (
            <>
              <Box>
                <FormControl fullWidth>
                  <Select
                    id='demo-simple-select'
                    onChange={e =>
                      handleMenuChange(setMenuIdAreas, Number(e.target.value))
                    }
                    value={menuIdAreas.toString()}
                  >
                    <MenuItem key={'0Key'} value={0}>
                      Areas
                    </MenuItem>
                    {(areasData ?? []).map((area, index) => (
                      <MenuItem key={index + 1} value={index + 1}>
                        {area}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              <Box sx={{}}>
                <FormControl fullWidth>
                  <Select
                    id='demo-simple-select'
                    onChange={e =>
                      handleMenuChange(
                        setMenuIdProductos,
                        Number(e.target.value)
                      )
                    }
                    value={menuIdProductos.toString()}
                  >
                    <MenuItem key={'0Key'} value={0}>
                      Productos
                    </MenuItem>
                    {(productosData ?? []).map((prod, index) => (
                      <MenuItem key={index + 1} value={index + 1}>
                        {prod}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </>
          )}
        </Grid>
        <Grid item xs={12}>
          <Box
            onClick={e => e.preventDefault()}
            sx={{
              '& > :not(style) ~ :not(style)': {
                ml: 2
              },
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'start',
              typography: 'body1'
            }}
          >
            <Link
              onClick={() => setLinkSelected(true)}
              underline={linkSelected ? 'always' : 'none'}
              variant='h4'
            >
              Informe Margen
            </Link>
            <Link
              onClick={() => setLinkSelected(false)}
              underline={linkSelected ? 'none' : 'always'}
              variant='h4'
            >
              Edición
            </Link>
          </Box>
          <br />
        </Grid>
        {isMargenFinancieroInformePending ? (
          <Loading />
        ) : (
          children({
            date: date ?? dayjs(),
            isInforme: linkSelected,
            isMargenFinancieroAjustesPending,
            margenFinancieroAjustesData: margenFinancieroAjustesData ?? [],
            margenFinancieroInformeData: margenFinancieroInformeData ?? [],
            refetch
          })
        )}
      </Grid>
    </Container>
  )
}
