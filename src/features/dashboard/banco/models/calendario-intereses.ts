/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const calendarioIntereses = type({
  ajustes: 'number',
  area: 'number',
  backOfficeID: 'string?',
  balance: 'string',
  coste: 'number',
  diferencia: 'number?',
  estado: 'string?',
  instrumentID: 'string',
  intereses: 'number',
  interesesSGT: 'number?',
  modo: 'string',
  producto: 'string',
  saldo: 'number',
  tradeGroup: 'string'
})

export const calendarioInteresesMetadata = new Set([
  {
    header: 'TradeGroup',
    key: 'tradeGroup'
  },
  {
    header: 'Area',
    key: 'area'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Ajustes',
    key: 'ajustes'
  },
  {
    header: 'Modo',
    key: 'modo'
  },
  {
    header: 'BackOfficeID',
    key: 'backOfficeID'
  },
  {
    header: 'Coste',
    key: 'coste'
  },
  {
    header: 'Balance',
    key: 'balance'
  },
  {
    header: 'InstrumentID',
    key: 'instrumentID'
  },
  {
    header: 'Intereses',
    key: 'intereses'
  }
])

export const calendarioInteresesCuadreMetadata = new Set([
  ...calendarioInteresesMetadata,
  {
    header: 'InteresesSGT',
    key: 'interesesSGT'
  },
  {
    header: 'Diferencia',
    key: 'diferencia'
  },
  {
    header: 'Estado',
    key: 'estado'
  }
])

export type CalendarioIntereses = typeof calendarioIntereses.infer

export const calendarioInteresesValidate = (attempt?: CalendarioIntereses) =>
  calendarioIntereses(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
