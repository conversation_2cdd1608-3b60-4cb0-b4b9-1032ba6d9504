import { UploadControl } from '@d/admin/components/upload-control'
import {
  Box,
  Button,
  Container,
  Grid,
  LinearProgress,
  Paper
} from '@mui/material'

import { OverlayAlert } from '../components/alert'

const paperStyle = {
  alignItems: 'center',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

export default () => {
  const [showAlert, setShowAlert] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const performCargaAjustes = () => {
    setIsLoading(true)
    setIsLoading(false)
    setShowAlert(true)
    setTimeout(() => setShowAlert(false), 2000)
  }

  return (
    <>
      <OverlayAlert open={showAlert} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            {isLoading && <LinearProgress sx={{ my: 3 }} />}
            <Paper elevation={10} sx={paperStyle}>
              <UploadControl uploadTitle='Subir Fichero Ajustes'>
                <Box display='flex' flexDirection='row' gap={2}>
                  <Button
                    onClick={performCargaAjustes}
                    sx={{
                      background: '#E5F1EE',
                      color: 'green',
                      marginRight: '5px'
                    }}
                    variant='contained'
                  >
                    Carga Ajustes
                  </Button>
                </Box>
              </UploadControl>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
