/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const resumenImportacion = type({
  areas: 'number',
  fecha: 'string',
  instrumentos: 'number',
  productos: 'number',
  tradeGroups: 'number'
})

export type ResumenImportacion = typeof resumenImportacion.infer

export const resumenImportacionMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Áreas',
    key: 'areas'
  },
  {
    header: 'Trade Groups',
    key: 'tradeGroups'
  },
  {
    header: 'Productos',
    key: 'productos'
  },
  {
    header: 'Instrumentos',
    key: 'instrumentos'
  }
])
