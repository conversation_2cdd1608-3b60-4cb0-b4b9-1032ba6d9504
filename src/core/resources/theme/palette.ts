import { presetDarkPalettes, presetPalettes } from '@ant-design/colors'
import { alpha, createTheme } from '@mui/material/styles'

import { ThemeMode } from '~/types/resources'

import { Default } from './default'

export const Palette = (mode: ThemeMode) => {
  const colors = mode === ThemeMode.DARK ? presetDarkPalettes : presetPalettes

  let greyPrimary = [
    '#ffffff',
    '#fafafa',
    '#f5f5f5',
    '#f0f0f0',
    '#d9d9d9',
    '#bfbfbf',
    '#8c8c8c',
    '#595959',
    '#004B36',
    '#141414',
    '#000000'
  ]
  let greyAscent = ['#fafafa', '#bfbfbf', '#434343', '#1f1f1f']
  let greyConstant = ['#fafafb', '#e6ebf1']

  if (mode === ThemeMode.DARK) {
    greyPrimary = [
      '#000000',
      '#141414',
      '#1e1e1e',
      '#595959',
      '#8c8c8c',
      '#bfbfbf',
      '#d9d9d9',
      '#f0f0f0',
      '#f5f5f5',
      '#fafafa',
      '#ffffff'
    ]

    greyAscent = ['#fafafa', '#bfbfbf', '#434343', '#1f1f1f']
    greyConstant = ['#121212', '#d3d8db']
  }

  colors['grey'] = [...greyPrimary, ...greyAscent, ...greyConstant]

  const paletteColor = Default(colors)

  return createTheme({
    palette: {
      common: {
        black: '#000',
        white: '#fff'
      },
      mode,
      ...paletteColor,
      action: {
        disabled: paletteColor.grey[300]
      },
      background: {
        default: paletteColor.grey.A50,
        paper:
          mode === ThemeMode.DARK
            ? paletteColor.grey[100]
            : paletteColor.grey[0]
      },
      darker: '',
      divider:
        mode === ThemeMode.DARK
          ? alpha(paletteColor.grey[900] ?? '#000', 0.05)
          : paletteColor.grey[200],
      lighter: '',
      text: {
        disabled:
          mode === ThemeMode.DARK
            ? alpha(paletteColor.grey[900] ?? '#212121', 0.1)
            : paletteColor.grey[400],
        primary:
          mode === ThemeMode.DARK
            ? alpha(paletteColor.grey[900] ?? '#212121', 0.87)
            : paletteColor.grey[700],
        secondary:
          mode === ThemeMode.DARK
            ? alpha(paletteColor.grey[900] ?? '#212121', 0.45)
            : paletteColor.grey[500]
      }
    }
  })
}
