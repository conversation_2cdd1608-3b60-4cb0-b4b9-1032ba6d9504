/* eslint-disable @typescript-eslint/switch-exhaustiveness-check */
import { store } from '@davstack/store'
import type { EventInput } from '@fullcalendar/common'
import dayjs, { type Dayjs } from 'dayjs'
import { piped } from 'rambdax'

type CalendarView = 'dayGridMonth' | 'listWeek' | 'timeGridDay' | 'timeGridWeek'

export const calendarStore = store<{
  actualDay: Dayjs
  available: boolean | null
  calendarView: CalendarView
  error: boolean
  events: EventInput[]
  isLoader: boolean
  isModalOpen: boolean
  selectedEventId: string | null
  selectedRange: {
    end: Date
    start: Date
  } | null
}>({
  actualDay: piped(dayjs(), day => {
    switch (day.day()) {
      case 1: {
        return day.subtract(4, 'day')
      }
      case 2: {
        return day.subtract(4, 'day')
      }
      case 6: {
        return day.subtract(1, 'day')
      }
      default: {
        return day.subtract(2, 'day')
      }
    }
  }),
  available: null,
  calendarView: 'dayGridMonth',
  error: false,
  events: [],
  isLoader: false,
  isModalOpen: false,
  selectedEventId: null,
  selectedRange: null
})
  .computed(store => ({
    formattedDay: () => dayjs(store.actualDay.use()).format('YYYY-MM-DD')
  }))
  .actions(store => ({
    createEvent: (event: EventInput[]) => {
      store.isLoader.set(false)
      store.isModalOpen.set(false)
      store.events.set(event)
    },
    deleteEvent: (eventId: string) => {
      store.isModalOpen.set(false)
      store.events.set(store.events.get().filter(user => user.id !== eventId))
    },
    selectEvent: (eventId: string) => {
      store.isModalOpen.set(true)
      store.selectedEventId.set(eventId)
    },
    selectRange: (start: Date, end: Date) => {
      store.isModalOpen.set(true)
      store.selectedRange.set({ end, start })
    },
    setActualDay: (actualDay: Dayjs) => store.actualDay.set(actualDay),
    setAvailable: (available: boolean) => store.available.set(available),
    setCalendarView: (calendarView: CalendarView) =>
      store.calendarView.set(calendarView),
    setError: () => {
      store.isLoader.set(false)
      store.error.set(true)
    },
    setEvents: (events: EventInput[]) => {
      store.isLoader.set(false)
      store.events.set(events)
    },
    setLoading: (loading: boolean) => store.isLoader.set(loading),
    toggleModal: () => {
      store.isModalOpen.set(!store.isModalOpen.get())
      if (!store.isModalOpen.get()) {
        store.selectedEventId.set(null)
        store.selectedRange.set(null)
      }
    },
    updateEvent: (event: EventInput[]) => {
      store.isLoader.set(false)
      store.isModalOpen.set(false)
      store.events.set(event)
    }
  }))
