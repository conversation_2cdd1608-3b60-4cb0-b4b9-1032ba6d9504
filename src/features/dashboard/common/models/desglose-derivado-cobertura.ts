/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const desgloseDerivadoCobertura = type({
  book: 'string',
  currencyID: 'string',
  fechaValoracion: 'string',
  instrumentID: 'number',
  intDia: 'number',
  isin: 'string',
  saldo: 'number'
})

export const desgloseDerivadoCoberturaMetadata = new Set([
  {
    header: 'Instrument ID',
    key: 'instrumentID'
  },
  {
    header: 'Moneda',
    key: 'currencyID'
  },
  {
    header: 'Fecha Valoración',
    key: 'fechaValoracion'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Int Día',
    key: 'intDia'
  },
  {
    header: 'Book',
    key: 'book'
  },
  {
    header: 'Isin',
    key: 'isin'
  }
])

export type DesgloseDerivadoCobertura = typeof desgloseDerivadoCobertura.infer

export const desgloseDerivadoCoberturaValidate = (
  attempt: DesgloseDerivadoCobertura
) =>
  desgloseDerivadoCobertura(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
