/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const datosCargadosSGT = type({
  cartera: 'string',
  fecha: 'string',
  nominal: 'number',
  referencia: 'string'
})

export const datosCargadosSGTMetadata = new Set([
  { header: 'Fe<PERSON>', key: 'fecha' },
  { header: '<PERSON><PERSON>', key: 'cartera' },
  { header: 'Referencia', key: 'referencia' },
  { header: 'Nominal', key: 'nominal' }
])

export type DatosCargadosSGT = typeof datosCargadosSGT.infer
