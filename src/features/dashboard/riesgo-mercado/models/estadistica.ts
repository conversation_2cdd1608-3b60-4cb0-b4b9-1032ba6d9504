/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const estadistica = type({
  areaID: 'string',
  avgVaRmonth: 'number',
  avgVaRyear: 'number',
  maxVaRmonth: 'number',
  maxVaRyear: 'number',
  minVaRmonth: 'number',
  minVaRyear: 'number',
  portfolioID: 'string',
  referenceDate: 'string'
})

export const estadisticaMetadata = new Set([
  {
    header: 'Area',
    key: 'areaID'
  },
  {
    header: 'Min VaR Mes',
    key: 'minVaRmonth'
  },
  {
    header: 'Avg VaR Mes',
    key: 'avgVaRmonth'
  },
  {
    header: 'Max VaR Mes',
    key: 'maxVaRmonth'
  },
  {
    header: 'Min VaR Año',
    key: 'minVaRyear'
  },
  {
    header: 'Avg VaR Año',
    key: 'avgVaRyear'
  },
  {
    header: 'Max VaR <PERSON>ño',
    key: 'maxVaRyear'
  }
])

export type Estadistica = typeof estadistica.infer

export const estadisticaValidate = (attempt?: Estadistica) =>
  estadistica(attempt) instanceof type.errors ? resetObject(attempt) : attempt
