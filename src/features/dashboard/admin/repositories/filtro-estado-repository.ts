/* eslint-disable sonarjs/use-type-alias */
import type { FiltroEstado } from '@d/admin/models/filtro-estado'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class FiltroEstadoRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/filtroestado'

  readonly deleteFiltroEstado = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getFiltroEstado = () =>
    this.api.client
      .get<FiltroEstado[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertFiltroEstado = (filtroEstado: FiltroEstado) =>
    this.api.client.post<{ message: string }>(this.route, filtroEstado)

  readonly updateFiltroEstado = (id: string, filtroEstado: FiltroEstado) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      filtroEstado
    )
}
