import { Spread } from '@d/riesgo-mercado/models/spread'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class SpreadRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/spread'

  readonly getArea = (portfolio: 'Inv' | 'Neg' = 'Inv') =>
    this.api.client
      .get<Spread[]>(
        `${this.route}/SpreadSensitivityArea/${this.attachDate()}/${portfolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getTerm = (portfolio: 'Inv' | 'Neg' = 'Inv') =>
    this.api.client
      .get<Spread[]>(
        `${this.route}/SpreadSensitivityTerm/${this.attachDate()}/${portfolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
