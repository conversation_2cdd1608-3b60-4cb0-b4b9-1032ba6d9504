/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const varMarginal = type({
  area: 'string',
  bond: 'number',
  cartera: 'string',
  equity: 'number',
  fechaReferencia: 'string',
  fRA: 'number?',
  fRN: 'number?',
  indexLinkedBond: 'number',
  inflationSwap: 'number',
  pagare: 'number?',
  swap: 'number',
  tOTAL: 'number',
  treasuryBill: 'number?'
})

export const varMarginalMetadata = new Set([
  {
    header: 'Area',
    key: 'area'
  },
  {
    header: 'Cartera',
    key: 'cartera'
  },
  {
    header: 'TOTAL',
    key: 'tOTAL'
  },
  {
    header: 'Index Linked Bond',
    key: 'indexLinkedBond'
  },
  {
    header: 'Inflation Swap',
    key: 'inflationSwap'
  },
  {
    header: 'Bond',
    key: 'bond'
  },
  {
    header: 'Swap',
    key: 'swap'
  },
  {
    header: 'Equity',
    key: 'equity'
  },
  {
    header: 'FRA',
    key: 'fRA'
  },
  {
    header: 'FRN',
    key: 'fRN'
  },
  {
    header: 'Pagare',
    key: 'pagare'
  },
  {
    header: 'Treasury Bill',
    key: 'treasuryBill'
  }
])

export type VarMarginal = typeof varMarginal.infer

export const varMarginalValidate = (attempt?: VarMarginal) =>
  varMarginal(attempt) instanceof type.errors ? resetObject(attempt) : attempt
