import { store } from '@davstack/store'

import type { NavItemType } from '~/types/menu'

export const menuStore = store<{
  componentDrawerOpen: boolean
  drawerOpen: boolean
  menu: NavItemType
  openComponent: string
  openItem: string[]
  selectedID: string | null
}>({
  componentDrawerOpen: true,
  drawerOpen: false,
  //error: null,
  menu: {},
  openComponent: 'buttons',
  openItem: ['dashboard'],
  selectedID: null
}).actions(store => ({
  activeComponent: (openComponent: string) =>
    store.openComponent.set(openComponent),
  activeID: (selectedID: string | null) => store.selectedID.set(selectedID),
  activeItem: (openItem: string[]) => store.openItem.set(openItem),
  openComponentDrawer: (componentDrawerOpen: boolean) =>
    store.componentDrawerOpen.set(componentDrawerOpen),
  openDrawer: (drawerOpen: boolean) => store.drawerOpen.set(drawerOpen)
  //hasError: (error: string | null) => store.error.set(error)
}))
