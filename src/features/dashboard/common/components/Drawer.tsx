import { Box, Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>, useMediaQuery } from '@mui/material'
import {
  type CSSObject,
  styled,
  type Theme,
  useTheme
} from '@mui/material/styles'
import { useMemo } from 'react'

import { menuStore } from '@/common/store/menu-store'
import { DRAWER_WIDTH } from '~/constants'
import { ThemeMode } from '~/types/resources'

import { DrawerContent } from './drawer-content'
import { DrawerHeader } from './drawer-content/DrawerHeader'

const openedMixin = (theme: Theme): CSSObject => ({
  borderRight: `1px solid ${theme.palette.divider}`,
  boxShadow:
    theme.palette.mode === ThemeMode.DARK ? theme.customShadows.z1 : 'none',
  overflowX: 'hidden',
  transition: theme.transitions.create('width', {
    duration: theme.transitions.duration.enteringScreen,
    easing: theme.transitions.easing.sharp
  }),
  width: DRAWER_WIDTH
})

const closedMixin = (theme: Theme): CSSObject => ({
  borderRight: 'none',
  boxShadow: theme.customShadows.z1,
  overflowX: 'hidden',
  transition: theme.transitions.create('width', {
    duration: theme.transitions.duration.leavingScreen,
    easing: theme.transitions.easing.sharp
  }),
  width: theme.spacing(7.5)
})

const container = () => globalThis.window.document.body

export const MiniDrawerStyled = styled(MuiDrawer, {
  shouldForwardProp: prop => prop !== 'open'
})(({ open, theme }) => ({
  boxSizing: 'border-box',
  flexShrink: 0,
  whiteSpace: 'nowrap',
  width: DRAWER_WIDTH,
  ...((open ?? false) && {
    ...openedMixin(theme),
    '& .MuiDrawer-paper': openedMixin(theme)
  }),
  ...(!(open ?? false) && {
    ...closedMixin(theme),
    '& .MuiDrawer-paper': closedMixin(theme)
  })
}))

export const Drawer = () => {
  const theme = useTheme()
  const matchDownMD = useMediaQuery(theme.breakpoints.down('lg'))

  const { drawerOpen } = menuStore.use()

  const drawerContent = useMemo(() => <DrawerContent />, [])
  const drawerHeader = useMemo(
    () => <DrawerHeader open={drawerOpen} />,
    [drawerOpen]
  )

  return (
    <Box
      aria-label='mailbox folders'
      component='nav'
      sx={{ flexShrink: { md: 0 }, zIndex: 1200 }}
    >
      {matchDownMD ? (
        <MuiDrawer
          container={container}
          ModalProps={{ keepMounted: true }}
          onClose={() => menuStore.openDrawer(!drawerOpen)}
          open={drawerOpen}
          sx={{
            '& .MuiDrawer-paper': {
              backgroundImage: 'none',
              borderRight: `1px solid ${theme.palette.divider}`,
              boxShadow: 'inherit',
              boxSizing: 'border-box',
              width: DRAWER_WIDTH
            },
            display: { lg: 'none', xs: 'block' }
          }}
          variant='temporary'
        >
          {drawerHeader}
          {drawerContent}
        </MuiDrawer>
      ) : (
        <MiniDrawerStyled open={drawerOpen} variant='permanent'>
          {drawerHeader}
          {drawerContent}
        </MiniDrawerStyled>
      )}
    </Box>
  )
}
