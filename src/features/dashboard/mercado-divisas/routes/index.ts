import { layout, route } from '@react-router/dev/routes'

export const mercadoDivisasRouter = [
  route(
    'mercado-divisas/pylmonextr',
    'features/dashboard/mercado-divisas/pages/pylmonextr.tsx'
  ),
  route(
    'mercado-divisas/margen-financiero',
    'features/dashboard/mercado-divisas/pages/margen/margen.tsx'
  ),
  route(
    'mercado-divisas/riesgo-mercado',
    'features/dashboard/mercado-divisas/pages/riesgo-mercado.tsx'
  ),
  layout('features/dashboard/mercado-divisas/layout/posmonextr.tsx', [
    route(
      'mercado-divisas/posmonextr/mesa-contado',
      'features/dashboard/mercado-divisas/pages/posmonextr/mesa-contado.tsx'
    ),
    route(
      'mercado-divisas/posmonextr/mesa-plazo',
      'features/dashboard/mercado-divisas/pages/posmonextr/mesa-plazo.tsx'
    ),
    route(
      'mercado-divisas/posmonextr/total',
      'features/dashboard/mercado-divisas/pages/posmonextr/total.tsx'
    ),
    route(
      'mercado-divisas/posmonextr/pylhistorico',
      'features/dashboard/mercado-divisas/pages/posmonextr/pyl-historico.tsx'
    )
  ])
]
