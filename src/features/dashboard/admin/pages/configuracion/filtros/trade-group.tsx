import { SearchOutlined } from '@ant-design/icons'
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type FiltroTradeGroup,
  filtroTradeGroup,
  filtroTradeGroupDefault,
  filtroTradeGroupMetadata
} from '@d/admin/models/filtro-tradegroup'
import { TradeGroupRepository } from '@d/admin/repositories/tradegroup-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Select
} from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, nth, prop } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteFiltroTradeGroup,
    getFecha,
    getTradeGroups,
    insertFiltroTradeGroup,
    updateFiltroTradeGroup
  } = container.get(TradeGroupRepository)

  const { activoPasivo, areas, productos } = configStore.use()

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [menuIdAreas, setMenuIdAreas] = useState<number | undefined>()
  //const [menuIdFecha] = useState(0)
  const [menuIdProducto, setMenuIdProducto] = useState<number | undefined>()
  const [filterText, setFilterText] = useState('')
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(filtroTradeGroupDefault)

  const {
    data: allFiltroTradeGroupData,
    isFetching: isGetFiltroTradeGroupPending,
    refetch: refetchFiltroTradeGroup
  } = useQuery({
    queryFn: () =>
      getTradeGroups({
        areaId:
          menuIdAreas === undefined || menuIdAreas === 0
            ? undefined
            : nth(menuIdAreas, areas)?.areaID.toString(),
        producto:
          menuIdProducto === undefined || menuIdProducto === 0
            ? undefined
            : nth(menuIdProducto, productos)?.productID.toString()
      }),
    queryKey: ['getTradeGroups']
  })

  const { data: getFechaData, isFetching: isGetFechaPending } = useQuery({
    queryFn: getFecha,
    queryKey: ['getFecha']
  })

  const filterTradeGroup = useMemo(
    () =>
      filterText.trim() === ''
        ? (allFiltroTradeGroupData ?? [])
        : (allFiltroTradeGroupData ?? []).filter(item =>
            prop('tradeGroup', item)
              .toLowerCase()
              .includes(filterText.toLowerCase())
          ),
    [filterText, allFiltroTradeGroupData]
  )

  const submitAction = async (
    incoming: FiltroTradeGroup,
    _: FormikHelpers<FiltroTradeGroup>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertFiltroTradeGroup(incoming)
      : updateFiltroTradeGroup(
          selectedData.current.iD?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchFiltroTradeGroup()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteFiltroTradeGroup(selectedData.current.iD?.toString() ?? '0')
      setLoadingDialog(false)
      void refetchFiltroTradeGroup()
    }
  }
  useEffect(() => {
    void refetchFiltroTradeGroup()
  }, [menuIdAreas, menuIdProducto])

  return isGetFiltroTradeGroupPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchFiltroTradeGroup()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = filtroTradeGroupDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ alignItems: 'center', display: 'flex' }} xs={12}>
            <Box sx={{ width: '100%' }}>
              <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                <OutlinedInput
                  aria-describedby='header-search-text'
                  endAdornment={
                    <InputAdornment position='end' sx={{ mr: -0.5 }}>
                      <SearchOutlined />
                    </InputAdornment>
                  }
                  id='header-search'
                  inputProps={{ 'aria-label': 'weight' }}
                  onChange={e => setFilterText(e.target.value)}
                  placeholder='Buscar por Trade Group'
                />
              </FormControl>
            </Box>
            <Box sx={{ minWidth: 200 }}>
              <FormControl fullWidth>
                <Select
                  id='simple-select'
                  onChange={e => setMenuIdAreas(Number(e.target.value))}
                  value={menuIdAreas?.toString() ?? '0'}
                >
                  <MenuItem key={'aKey'} value={0}>
                    Areas
                  </MenuItem>
                  {areas.map((value, index) => (
                    <MenuItem key={index} value={index + 1}>
                      {value.titulo}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            {!isGetFechaPending && (
              <Box sx={{ marginX: '10px', minWidth: 200 }}>
                <FormControl fullWidth>
                  <Select id='simple-select' value={'0'}>
                    <MenuItem key={0} value={0}>
                      {getFechaData?.[0]?.fecha ?? ''}
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            )}
            <Box sx={{ minWidth: 200 }}>
              <FormControl fullWidth>
                <Select
                  id='simple-select'
                  onChange={e => setMenuIdProducto(Number(e.target.value))}
                  value={menuIdProducto?.toString() ?? '0'}
                >
                  <MenuItem key={'aKeyb'} value={0}>
                    Producto
                  </MenuItem>
                  {productos.map((value, index) => (
                    <MenuItem key={index} value={index + 1}>
                      {value.descripcionCorta}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker slotProps={{ textField: { size: 'small' } }} />
            </LocalizationProvider>
            <Button
              sx={{ background: '#E5F1EE', color: 'green', marginX: '5px' }}
              variant='contained'
            >
              Copiar filtro a fecha
            </Button>
          </Grid>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={filterTradeGroup}
                entries={filtroTradeGroup}
                isDeleteAction
                isEditAction
                metadata={filtroTradeGroupMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allFiltroTradeGroupData ?? []) ??
                    filtroTradeGroupDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<FiltroTradeGroup>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          codigo: Yup.string().max(20).required('El codigo es requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<FiltroTradeGroup>
              field='fecha'
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<FiltroTradeGroup>
              field='tradeGroup'
              label='Trade Group: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroup?.toString() ?? ''}
            />
            <SelectFormControl<FiltroTradeGroup>
              field='areaID'
              label='Area: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.areaID?.toString() ?? ''}
            >
              {areas.map((area, ind) => (
                <MenuItem key={ind} value={ind}>
                  {area.titulo}
                </MenuItem>
              ))}
            </SelectFormControl>
            <TextFormControl<FiltroTradeGroup>
              field='producto'
              label='Producto: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.producto?.toString() ?? ''}
            />
            <SelectFormControl<FiltroTradeGroup>
              field='activoID'
              label='Activo: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.activoID?.toString() ?? ''}
            >
              {activoPasivo.map((act, ind) => (
                <MenuItem key={ind} value={ind}>
                  {act.estado}
                </MenuItem>
              ))}
            </SelectFormControl>
            <SelectFormControl<FiltroTradeGroup>
              field='pasivoID'
              label='Pasivo: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.activoID?.toString() ?? ''}
            >
              {activoPasivo.map((act, ind) => (
                <MenuItem key={ind} value={ind}>
                  {act.estado}
                </MenuItem>
              ))}
            </SelectFormControl>
            <SelectFormControl<FiltroTradeGroup>
              field='epigrafeActivo'
              label='Epigrafe Activo: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.epigrafeActivo?.toString() ?? ''}
            >
              {/*activoPasivo.map((act, ind) => (
                <MenuItem key={ind} value={ind}>
                  {act.estado}
                </MenuItem>
              ))*/}
            </SelectFormControl>
            <SelectFormControl<FiltroTradeGroup>
              field='epigrafePasivo'
              label='Epigrafe Pasivo: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.epigrafePasivo?.toString() ?? ''}
            >
              {/*activoPasivo.map((act, ind) => (
                <MenuItem key={ind} value={ind}>
                  {act.estado}
                </MenuItem>
              ))*/}
            </SelectFormControl>
          </>
        )}
      </DialogForm>
    </>
  )
}
