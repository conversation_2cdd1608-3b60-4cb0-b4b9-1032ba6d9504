import { useMediaQuery, useTheme } from '@mui/material'

import { SimpleBarScroll } from '@/common/components/SimpleBar'
import { menuStore } from '@/common/store/menu-store'

import { NavCard } from './NavCard'
import { Navigation } from './Navigation'

export const DrawerContent = () => {
  const theme = useTheme()
  const matchDownMD = useMediaQuery(theme.breakpoints.down('lg'))

  const { drawerOpen } = menuStore.use()

  return (
    <SimpleBarScroll
      sx={{
        '& .simplebar-content': {
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <Navigation />
      {drawerOpen && !matchDownMD && <NavCard />}
    </SimpleBarScroll>
  )
}
