{"compilerOptions": {"experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "jsx": "preserve", "jsxImportSource": "react", "isolatedModules": true, "module": "ESNext", "moduleResolution": "bundler", "noEmit": true, "paths": {"@/*": ["./src/features/*"], "~/*": ["./src/core/*"], "@c/*": ["./src/features/common/*"], "@d/*": ["./src/features/dashboard/*"]}, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "types": ["vite/client"], "target": "ESNext"}, "rootDirs": [".", "./.react-router/types"], "plugins": [{"name": "@react-router/dev"}], "extends": "./tsconfig.app.json", "include": ["src", "config", "vite.config.ts"]}