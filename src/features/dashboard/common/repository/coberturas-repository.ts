import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

import { Bonos, Derivados } from '../models/coberturas'

@injectable()
export class CoberturasRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/saldostesoreros'
  readonly getBonos = (areaId: string) =>
    this.api.client
      .get<Bonos[]>(
        `${this.route}/bonosCubiertos/${this.attachDate()}/${areaId}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getDerivados = (areaId: string, isin: string, book: string) =>
    this.api.client
      .get<Derivados[]>(
        `${this.route}/derivadosDeCobertura/${this.attachDate()}/${areaId}/${isin}/${book}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
