import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type FiltroCuentas,
  filtroCuentas,
  filtroCuentasDefault,
  filtroCuentasMetadata
} from '@d/admin/models/filtro-cuentas'
import {
  type FiltroCuentasBalance,
  filtroCuentasBalance,
  filtroCuentasBalanceMetadata
} from '@d/admin/models/filtro-cuentas-balance'
import { FiltroCuentasRepository } from '@d/admin/repositories/filtro-cuentas-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  But<PERSON>,
  Checkbox,
  Container,
  FormControlLabel,
  Grid,
  MenuItem,
  Paper
} from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteFiltroCuentas,
    getFiltroCuentas,
    getFiltroCuentasBalance,
    insertFiltroCuentas,
    updateFiltroCuentas
  } = container.get(FiltroCuentasRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const [loadingBalance, setLoadingBalance] = useState(false)
  const { areas } = configStore.use()

  const filtroCuentasBalanceData = useRef<FiltroCuentasBalance[]>(null)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(filtroCuentasDefault)

  const {
    data: allFiltroCuentasData,
    isFetching: isGetFiltroCuentasPending,
    refetch: refetchFiltroCuentas
  } = useQuery({
    queryFn: getFiltroCuentas,
    queryKey: ['getFiltroCuentas']
  })

  const submitAction = async (
    incoming: FiltroCuentas,
    _: FormikHelpers<FiltroCuentas>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertFiltroCuentas(incoming)
      : updateFiltroCuentas(
          selectedData.current.ordenID?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchFiltroCuentas()
  }

  const selectedBalance = async (index: number) => {
    const selected = nth(index, allFiltroCuentasData ?? [])
    if (selected === undefined) return
    setLoadingBalance(true)
    filtroCuentasBalanceData.current = await getFiltroCuentasBalance(
      selected.tradeGroup?.toString() ?? ''
    )
    setLoadingBalance(false)
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteFiltroCuentas(selectedData.current.ordenID?.toString() ?? '0')
      setLoadingDialog(false)
      void refetchFiltroCuentas()
    }
  }

  return isGetFiltroCuentasPending ? (
    <Loading />
  ) : (
    <>
      <Reload
        onClick={() => {
          filtroCuentasBalanceData.current = null
          void refetchFiltroCuentas()
        }}
      >
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = filtroCuentasDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={allFiltroCuentasData ?? []}
                entries={filtroCuentas}
                isDeleteAction
                isEditAction
                isSelected
                metadata={filtroCuentasMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allFiltroCuentasData ?? []) ??
                    filtroCuentasDefault
                  setOpenCreateEditDialog(true)
                }}
                onRowClick={selectedBalance}
              />
            </Paper>
          </Grid>
        </Grid>
        <Grid
          item
          sx={{ alignItems: 'center', display: 'flex', ml: '20px', my: 4 }}
          xs={12}
        >
          <FormControlLabel
            control={<Checkbox />}
            label='Revaluación TradeGroup (Swaps)'
          />
          <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
            <DatePicker slotProps={{ textField: { size: 'small' } }} />
          </LocalizationProvider>
        </Grid>
        <Grid item xs={12}>
          {loadingBalance ? (
            <Loading />
          ) : (
            <Paper elevation={1} sx={paperStyle}>
              <GenericTable
                data={filtroCuentasBalanceData.current ?? []}
                entries={filtroCuentasBalance}
                metadata={filtroCuentasBalanceMetadata}
              />
            </Paper>
          )}
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<FiltroCuentas>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          codigo: Yup.string().max(20).required('El codigo es requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <SelectFormControl<FiltroCuentas>
              field='areaID'
              label='Area: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.areaID?.toString() ?? ''}
            >
              {areas.map((area, ind) => (
                <MenuItem key={ind} value={ind}>
                  {area.titulo}
                </MenuItem>
              ))}
            </SelectFormControl>
            <TextFormControl<FiltroCuentas>
              field='tradeGroup'
              label='Trade Group: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroup?.toString() ?? ''}
            />
            <TextFormControl<FiltroCuentas>
              field='producto'
              label='Producto: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.producto?.toString() ?? ''}
            />
            <DateFormControl<FiltroCuentas>
              field='fecha'
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<FiltroCuentas>
              field='comentarios'
              label='Comentarios: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.comentarios?.toString() ?? ''}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
