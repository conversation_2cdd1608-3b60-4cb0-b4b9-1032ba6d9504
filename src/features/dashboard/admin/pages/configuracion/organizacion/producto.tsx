import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Productos,
  productos,
  productosDefault,
  productosMetadata
} from '@d/admin/models/productos'
import { ProductosRepository } from '@d/admin/repositories/org-producto-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { deleteProductos, getProductos, insertProductos, updateProductos } =
    container.get(ProductosRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(productosDefault)

  const {
    data: allProductosData,
    isFetching: isGetProductosPending,
    refetch: refetchProductos
  } = useQuery({
    queryFn: getProductos,
    queryKey: ['getProductos']
  })

  const submitAction = async (
    incoming: Productos,
    _: FormikHelpers<Productos>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertProductos(incoming)
      : updateProductos(selectedData.current.productID.toString(), incoming))
    setLoadingDialog(false)
    void refetchProductos()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteProductos(selectedData.current.productID.toString())
      setLoadingDialog(false)
      void refetchProductos()
    }
  }

  return isGetProductosPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchProductos()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = productosDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allProductosData ?? []}
                entries={productos}
                isDeleteAction
                isEditAction
                metadata={productosMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allProductosData ?? []) ?? productosDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Productos>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          nombre: Yup.string().max(5).required('Nombre is required'),
          orden: Yup.number().required('Orden is required'),
          titulo: Yup.string().max(25).required('Titulo is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Productos>
              field='product'
              label='Producto: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.product.toString()}
            />
            <TextFormControl<Productos>
              field='descripcionLarga'
              label='Descripción larga: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.descripcionLarga.toString()}
            />
            <TextFormControl<Productos>
              field='descripcionCorta'
              label='Descripción corta: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.descripcionCorta.toString()}
            />
            <TextFormControl<Productos>
              field='descripcionCorta'
              label='Descripción corta: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.descripcionCorta.toString()}
            />
            <TextFormControl<Productos>
              field='nombreActivo'
              label='Nombre Activo: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.nombreActivo.toString()}
            />
            <TextFormControl<Productos>
              field='nombrePasivo'
              label='Nombre Pasivo: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.nombrePasivo.toString()}
            />
            <TextFormControl<Productos>
              field='orden'
              label='Orden: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.orden?.toString() ?? ''}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
