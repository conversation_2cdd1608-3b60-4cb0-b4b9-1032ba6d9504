import type { AjusteTrading } from '@d/admin/models/ajuste-trading'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class AjusteTradingRepository {
  private readonly ajustesRoute = '/ajustes'

  @inject(ApiModule)
  private readonly api!: ApiModule
  private readonly coapRoute = '/coap'

  readonly createAjusteManual = (
    accountingAreaID: string,
    fecha: string,
    importe: number,
    concepto: string,
    comentarios = ''
  ) =>
    this.api.client.post<{ message: string }>(this.ajustesRoute, {
      accountingAreaID,
      comentarios,
      concepto,
      fecha,
      importe
    })

  readonly deleteAjusteManual = (
    accId: string,
    fecha: string,
    concepto: string
  ) =>
    this.api.client.delete<{ message: string }>(
      `${this.ajustesRoute}/${fecha}/${accId.toString()}/${concepto}`
    )

  readonly getAjusteManual = (accId: string, concepto: string) =>
    this.api.client
      .get<AjusteTrading[]>(
        `${this.coapRoute}/ajusteManual/${accId.toString()}/${concepto}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly updateAjusteManual = (
    accId: string,
    fecha: string,
    importe: number,
    concepto: string,
    comentarios = ''
  ) =>
    this.api.client.put<{ message: string }>(
      `${this.ajustesRoute}/${fecha}/${importe.toString()}/${accId.toString()}/${comentarios}/${concepto}`
    )
}
