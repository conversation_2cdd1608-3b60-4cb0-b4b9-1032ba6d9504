import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const logsBatch = type({
  fechaHora: 'string',
  lOGID: 'number',
  logText: 'string',
  nombreUsuario: 'string'
})

export const logsBatchMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fechaHora'
  },
  {
    header: 'Log Text',
    key: 'logText'
  }
])

export type LogsBatch = typeof logsBatch.infer

export const logsBatchValidate = (attempt?: LogsBatch) =>
  logsBatch(attempt) instanceof type.errors ? resetObject(attempt) : attempt
