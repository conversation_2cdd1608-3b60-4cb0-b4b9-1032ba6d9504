import { FileExcelOutlined } from '@ant-design/icons'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography
} from '@mui/material'
import type { Type } from 'arktype'
import { type FC, useMemo } from 'react'

import { customExportToCSV } from '~/utils/csv'

import { GenericTable } from './generic-table/GenericTable'

export const DialogTable: FC<{
  data: Record<string, number | string>[]
  entries: Type<object>
  excel?: boolean
  metadata: Set<{ header: string; key: string }>
  onRowClick?: (index: number) => void
  open: boolean
  rowsHeaders?: string[]
  setOpen: (open: boolean) => void
  title?: string
}> = ({
  data,
  entries,
  excel = false,
  metadata,
  onRowClick,
  open,
  rowsHeaders,
  setOpen,
  title = ''
}) => {
  const dataFiltered = useMemo(() => {
    if (
      (excel && data.length > 0 && rowsHeaders?.includes('StartDateAdj')) ??
      false
    ) {
      return data.map(el => ({
        ...el,
        'ExpiryDate':
          el['ExpiryDate'] === undefined
            ? ''
            : new Date(el['ExpiryDate'] as string).toISOString().split('T')[0],
        'StartDateAdj':
          el['StartDateAdj'] === undefined
            ? ''
            : new Date(el['StartDateAdj'] as string).toISOString().split('T')[0]
      })) as Record<string, number | string>[]
    }
    return data
  }, [data])

  return (
    <Dialog
      aria-describedby='alert-dialog-description'
      aria-labelledby='alert-dialog-title'
      maxWidth='xl'
      onClose={() => setOpen(false)}
      open={open}
      sx={{ '& .MuiPaper-root': { resize: 'horizontal' } }}
    >
      <DialogTitle id='alert-dialog-title'>
        <Typography variant='h4'>{title}</Typography>
      </DialogTitle>
      <DialogContent>
        {dataFiltered.length > 0 ? (
          <GenericTable
            data={dataFiltered}
            entries={entries}
            metadata={metadata}
            onRowClick={onRowClick}
          />
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              my: 2,
              width: '100%'
            }}
          >
            <Typography sx={{ fontStyle: 'italic' }} variant='subtitle1'>
              No hay datos que mostrar
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        {excel && (
          <Button
            onClick={() =>
              customExportToCSV(
                [...metadata].map(el => el.header),
                dataFiltered,
                [...metadata].map(el => el.key),
                title ? `${title}.csv` : 'data.csv',
                ';'
              )
            }
            startIcon={<FileExcelOutlined />}
            variant='contained'
          >
            Descargar excel
          </Button>
        )}
        <Button onClick={() => setOpen(false)}>Cerrar</Button>
      </DialogActions>
    </Dialog>
  )
}
