/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const ajusteTrading = type({
  accountingAreaID: 'string',
  comentarios: 'string?',
  concepto: 'string',
  fecha: 'string',
  importe: 'number?'
})

export const ajusteTradingMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Concepto',
    key: 'concepto'
  },
  {
    header: 'Importe',
    key: 'importe'
  },
  {
    header: 'Comentarios',
    key: 'comentarios'
  }
])

export type AjusteTrading = typeof ajusteTrading.infer

export const ajusteTradingDefault: AjusteTrading = {
  accountingAreaID: 'TESO',
  comentarios: '',
  concepto: '',
  fecha: '',
  importe: 0
}

export const ajusteTradingValidate = (attempt: AjusteTrading) =>
  ajusteTrading(attempt) instanceof type.errors ? resetObject(attempt) : attempt
