/* eslint-disable sonarjs/void-use */
import { Breadcrumbs } from '@c/components/Breadcrumbs'
import { Drawer } from '@d/common/components/Drawer'
import { Header } from '@d/common/components/Header'
import { HorizontalBar } from '@d/common/components/HorizontalBar'
import { menuItems } from '@d/common/data'
import {
  Box,
  Container,
  LinearProgress,
  Toolbar,
  Typography,
  useMediaQuery
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { useEffect } from 'react'
import { Outlet, useNavigate } from 'react-router'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { authStore } from '@/common/store/auth-store'
import { calendarStore } from '@/common/store/calendar-store'
import { menuStore } from '@/common/store/menu-store'
import { auth } from '~/modules/api-module'
import { MenuOrientation, ThemeMode } from '~/types/resources'

export default () => {
  const theme = useTheme()
  const matchDownXL = useMediaQuery(theme.breakpoints.down('xl'))
  const downLG = useMediaQuery(theme.breakpoints.down('lg'))
  const { user } = authStore.use()
  const { available, isLoader } = calendarStore.use()
  const {
    container: muiContainer,
    menuOrientation,
    miniDrawer,
    mode
  } = useContext(ResourceContext)
  const navigate = useNavigate()

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG

  useEffect(() => {
    if (!miniDrawer) menuStore.openDrawer(!matchDownXL)
  }, [matchDownXL])

  useEffect(() => {
    if (user === null || auth.token === '')
      void navigate('/', { state: { from: '' } })
  }, [])

  return (
    <Box sx={{ display: 'flex', width: '100%' }}>
      <Header />
      {isHorizontal ? <HorizontalBar /> : <Drawer />}
      <Box
        component='main'
        sx={{
          bgcolor: mode === ThemeMode.DARK ? '#212121' : '#f7f7f8',
          flexGrow: 1,
          p: { sm: 3, xs: 2 },
          width: 'calc(100% - 260px)'
        }}
      >
        <Toolbar sx={{ mt: isHorizontal ? 8 : 'inherit' }} />
        {isLoader && available !== true && (
          <LinearProgress color='primary' sx={{ mb: 2 }} />
        )}
        <Container
          maxWidth={muiContainer ? 'xl' : false}
          sx={{
            ...(muiContainer && { px: { sm: 2, xs: 0 } }),
            bgcolor: mode === ThemeMode.DARK ? '#212121' : null,
            display: 'flex',
            flexDirection: 'column',
            marginLeft: '0',
            position: 'relative'
          }}
        >
          <Breadcrumbs
            card={false}
            divider={false}
            navigation={menuItems(user)}
            title
            titleBottom
          />
          {available === true ? (
            <Outlet />
          ) : (
            <Box
              sx={{
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'column',
                height: 'calc(100vh - 260px)',
                justifyContent: 'center',
                width: '100%'
              }}
            >
              <Typography
                sx={{ fontStyle: 'italic', mt: 1 }}
                variant='subtitle1'
              >
                Datos no disponibles, por favor cambie de fecha con datos
                registrados
              </Typography>
            </Box>
          )}
        </Container>
      </Box>
    </Box>
  )
}
