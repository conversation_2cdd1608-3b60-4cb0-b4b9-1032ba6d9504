/* eslint-disable sonarjs/use-type-alias */
import type { FondoArcano } from '@d/admin/models/fondo-arcano'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class FondoArcanoRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/fondoarcano'

  readonly deleteFondoArcano = (fecha: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${fecha}`)

  readonly getFondoArcano = () =>
    this.api.client
      .get<FondoArcano[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertFondoArcano = (
    fecha: string,
    nParticipaciones: string,
    valorLiquidativo: string
  ) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/${fecha}/${nParticipaciones}/${valorLiquidativo}`
    )
}
