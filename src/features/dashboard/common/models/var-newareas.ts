/* eslint-disable perfectionist/sort-objects */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const varNewAreas = type({
  area: 'string',
  cartera: 'string',
  contabilidad: 'string',
  fechaValor: 'string',
  producto: 'string',
  vaR: 'number',
  vaRMarginalArea: 'number'
})

export type VarNewAreas = typeof varNewAreas.infer

export const varNewAreasValidate = (attempt: VarNewAreas) =>
  varNewAreas(attempt) instanceof type.errors ? resetObject(attempt) : attempt

export const varNewAreasProductoFactor = type({
  area: 'string',
  betaMarginalArea: 'number',
  contabilidad: 'string',
  factor: 'string',
  fechaValor: 'string',
  producto: 'string',
  vaR: 'number',
  vaRMarginalArea: 'number'
})

export const historicoParameticoMetadata = new Set([
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'VaR',
    key: 'vaR'
  },
  {
    header: 'VaR Marginal',
    key: 'vaRMarginalArea'
  }
])

export const headersFactoresMetadata = new Set([
  {
    header: 'Factor',
    key: 'factor'
  },
  {
    header: 'VaR',
    key: 'vaR'
  },
  {
    header: 'VaR Marginal',
    key: 'vaRMarginalArea'
  }
])

export type VarNewAreasProductoFactor = typeof varNewAreasProductoFactor.infer

export const varNewAreasProductoFactorValidate = (
  attempt: VarNewAreasProductoFactor
) =>
  varNewAreasProductoFactor(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
