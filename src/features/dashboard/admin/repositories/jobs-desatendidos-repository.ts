/* eslint-disable sonarjs/use-type-alias */
import type { CalAgr } from '@d/admin/models/cal-agr'
import type { CalcEjec } from '@d/admin/models/cal-ejec'
import type { LogsBatch } from '@d/admin/models/logs-batch'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class JobsDesatendidosRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/jobsdesatendidos'

  readonly deleteCalcEjec = (fecha: string) =>
    this.api.client.delete<{ message: string }>(
      `${this.route}/calendarioJobs/${fecha}`
    )

  readonly getCalcAgrp = () =>
    this.api.client
      .get<CalAgr[]>(`${this.route}/calendarioGeneracion`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getCalcEjec = () =>
    this.api.client
      .get<CalcEjec[]>(`${this.route}/calendarioJobs`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getLogsBatch = () =>
    this.api.client
      .get<LogsBatch[]>(`${this.route}/logs`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertCalcAgr = (calcAgr: { dias: string; fecha: string }) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/calendarioGeneracion`,
      calcAgr
    )

  readonly insertCalcEjec = (calcEjec: { fecha: string; peso: string }) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/calendarioJobs`,
      calcEjec
    )

  readonly updateCalcAgr = ({ dias, fecha }: { dias: string; fecha: string }) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/calendarioGeneracion/${fecha}/${dias}`
    )

  readonly updateCalcEjec = (fecha: string, peso: string) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/calendarioJobs/${fecha}/${peso}`
    )
}
