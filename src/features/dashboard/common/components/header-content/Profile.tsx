/* eslint-disable sonarjs/void-use */
import { LogoutOutlined } from '@ant-design/icons'
import { Avatar } from '@c/components/Avatar'
import { IconButton } from '@c/components/IconButton'
import { MainCard } from '@c/components/MainCard'
import { Transitions } from '@c/components/Transitions'
import {
  Box,
  ButtonBase,
  CardContent,
  ClickAwayListener,
  Grid,
  Paper,
  Popper,
  Stack,
  Tooltip,
  Typography
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { useRef, useState } from 'react'
import { useNavigate } from 'react-router'

import { authStore } from '@/common/store/auth-store'
import avatar1 from '~/assets/avatar-1.png'
import { ApiModule } from '~/modules/api-module'
import { container } from '~/modules/di-module'
import { ThemeMode } from '~/types/resources'

export const Profile = () => {
  const theme = useTheme()
  const navigate = useNavigate()
  const { user } = authStore.use()
  const api = container.get(ApiModule)

  const handleLogout = () => {
    authStore.logout()
    api.apiTokenize()
    void navigate('/', { state: { from: '' } })
  }

  const anchorRef = useRef<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState(false)

  const handleToggle = () => setOpen(prevOpen => !prevOpen)
  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current?.contains(event.target as Node) ?? false) return
    setOpen(false)
  }

  const iconBackColorOpen =
    theme.palette.mode === ThemeMode.DARK ? 'grey.200' : 'grey.300'

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <ButtonBase
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup='true'
        aria-label='open profile'
        onClick={handleToggle}
        ref={anchorRef}
        sx={{
          '&:focus-visible': {
            outline: `2px solid ${theme.palette.secondary.dark}`,
            outlineOffset: 2
          },
          '&:hover': {
            bgcolor:
              theme.palette.mode === ThemeMode.DARK
                ? 'secondary.light'
                : 'secondary.lighter'
          },
          bgcolor: open ? iconBackColorOpen : 'transparent',
          borderRadius: 1,
          p: 0.25
        }}
      >
        <Stack alignItems='center' direction='row' spacing={2} sx={{ p: 0.5 }}>
          <Avatar alt='profile user' size='xs' src={avatar1} />
          <Typography variant='subtitle1'>{user?.name}</Typography>
        </Stack>
      </ButtonBase>
      <Popper
        anchorEl={anchorRef.current}
        disablePortal
        open={open}
        placement='bottom-end'
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 9]
              }
            }
          ]
        }}
        role={undefined}
        transition
      >
        {({ TransitionProps }) => (
          <Transitions
            in={open}
            position='top-right'
            type='grow'
            {...TransitionProps}
          >
            <Paper
              sx={{
                boxShadow: theme.customShadows.z1,
                maxWidth: 290,
                minWidth: 240,
                [theme.breakpoints.down('md')]: {
                  maxWidth: 250
                },
                width: 290
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard border={false} content={false} elevation={0}>
                  <CardContent sx={{ pt: 3, px: 2.5 }}>
                    <Grid
                      alignItems='center'
                      container
                      justifyContent='space-between'
                    >
                      <Grid item>
                        <Stack
                          alignItems='center'
                          direction='row'
                          spacing={1.25}
                        >
                          <Avatar
                            alt='profile user'
                            src={avatar1}
                            sx={{ height: 32, width: 32 }}
                          />
                          <Stack>
                            <Typography variant='body2'>
                              {user?.email ?? 'Usuario de BCE'}
                            </Typography>
                          </Stack>
                        </Stack>
                      </Grid>
                      <Grid item>
                        <Tooltip title='Logout'>
                          <IconButton
                            onClick={handleLogout}
                            size='large'
                            sx={{ color: 'text.primary' }}
                          >
                            <LogoutOutlined />
                          </IconButton>
                        </Tooltip>
                      </Grid>
                    </Grid>
                  </CardContent>
                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  )
}
