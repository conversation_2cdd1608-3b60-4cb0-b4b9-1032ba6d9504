/* eslint-disable perfectionist/sort-objects */
/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const activoPasivo = type({
  balance: 'string',
  currencyID: 'string',
  epigrafeID: 'number?',
  fecha: 'string',
  intAnual: 'number',
  intDia: 'number',
  intDia_Anterior: 'number',
  intMes: 'number',
  margen_Anterior: 'number',
  nombre: 'string',
  orden: 'number',
  porcVarIntDia: 'number',
  porcVarRentabilidadDia: 'number',
  producto: 'string',
  rentabilidadAnual: 'number',
  rentabilidadDia: 'number',
  rentabilidadMes: 'number',
  saldo: 'number',
  saldoMes: 'number',
  tipoAnual: 'number',
  tipoMedio: 'number',
  tipoMes: 'number',
  tradeGroup: 'string'
})

export const activoPasivoMetadata = new Set([
  {
    key: 'nombre',
    header: 'Nombre'
  },
  {
    key: 'saldo',
    header: 'Saldo'
  },
  {
    key: 'saldoMes',
    header: '<PERSON><PERSON>'
  },
  {
    key: 'intDia',
    header: 'Int Día'
  },
  {
    key: 'porcVarIntDia',
    header: 'Var. Diaria'
  },
  {
    key: 'intMes',
    header: 'Int Mes'
  },
  {
    key: 'intAnual',
    header: 'Int Anual'
  },
  {
    key: 'tipoMedio',
    header: 'Tipo Medio'
  },
  {
    key: 'tipoMes',
    header: 'Tipo Mes'
  },
  {
    key: 'tipoAnual',
    header: 'Tipo Anual'
  },
  {
    key: 'rentabilidadDia',
    header: 'Margen Día'
  },
  {
    key: 'porcVarRentabilidadDia',
    header: 'Var. Margen Diario'
  },
  {
    key: 'rentabilidadMes',
    header: 'M. Mes'
  },
  {
    key: 'rentabilidadAnual',
    header: 'M. Año'
  }
])

export const activoPasivoDetailMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Cartera Contabilidad',
    key: 'currencyID'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Saldo Mes',
    key: 'saldoMes'
  },
  {
    header: 'Int Día',
    key: 'intDia'
  },
  {
    header: 'Int Mes',
    key: 'intMes'
  },
  {
    header: 'Int Anual',
    key: 'intAnual'
  },
  {
    header: 'Tipo Medio',
    key: 'tipoMedio'
  },
  {
    header: 'Tipo Mes',
    key: 'tipoMes'
  },
  {
    header: 'Tipo Anual',
    key: 'tipoAnual'
  },
  {
    header: 'M. Día',
    key: 'rentabilidadDia'
  },
  {
    header: 'M. Mes',
    key: 'rentabilidadMes'
  },
  {
    header: 'M. Año',
    key: 'rentabilidadAnual'
  }
])

export type ActivoPasivo = typeof activoPasivo.infer

export const activoPasivoValidate = (attempt: ActivoPasivo) =>
  activoPasivo(attempt) instanceof type.errors ? resetObject(attempt) : attempt
