/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const filtroCuentas = type({
  areaID: 'number?',
  comentarios: 'string?',
  fecha: 'string?',
  includeFunding: 'boolean?',
  ordenID: 'number?',
  producto: 'string?',
  tradeGroup: 'string?',
  valoracion: 'string?'
})

export type FiltroCuentas = typeof filtroCuentas.infer

export const filtroCuentasDefault: FiltroCuentas = {
  areaID: 0,
  comentarios: '',
  fecha: '',
  includeFunding: false,
  ordenID: 0,
  producto: '',
  tradeGroup: '',
  valoracion: ''
}

export const filtroCuentasMetadata = new Set([
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Areas',
    key: 'areaID'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Comentarios',
    key: 'comentarios'
  },
  {
    header: 'M. Valoracion',
    key: 'valoracion'
  }
])
