import {
  Checkbox,
  FormControlLabel,
  Grid,
  type SelectChangeEvent
} from '@mui/material'

type Props<T extends Record<string, unknown>> = {
  disabled?: boolean
  field: keyof T
  label: string
  onChange?: (e: SelectChangeEvent) => void
  required?: boolean
  value: boolean
}

export const CheckFormControl = <T extends Record<string, unknown>>({
  disabled = false,
  field,
  label,
  onChange,
  required,
  value
}: Props<T>) => (
  <Grid item sx={{ marginLeft: '-16px' }} xs={12}>
    <FormControlLabel
      control={
        <Checkbox
          disabled={disabled}
          name={field as string}
          onChange={onChange}
          required={required}
          size='small'
          sx={{ width: '200px' }}
          value={value}
        />
      }
      label={label}
      labelPlacement='start'
      sx={{ display: 'flex', justifyContent: 'space-between' }}
    />
  </Grid>
)
