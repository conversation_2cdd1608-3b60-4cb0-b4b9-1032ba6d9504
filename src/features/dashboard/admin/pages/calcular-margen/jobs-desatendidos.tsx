import { PlusCircleOutlined, SearchOutlined } from '@ant-design/icons'
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type CalAgr,
  calAgr,
  calAgrDefault,
  calAgrMetadata
} from '@d/admin/models/cal-agr'
import {
  type CalcEjec,
  calcEjec,
  calcEjecDefault,
  calcEjecMetadata
} from '@d/admin/models/cal-ejec'
import { logsBatch, logsBatchMetadata } from '@d/admin/models/logs-batch'
import { JobsDesatendidosRepository } from '@d/admin/repositories/jobs-desatendidos-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  type SelectChangeEvent,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, nth, piped, prop } from 'rambdax'
import type { ChangeEvent } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteCalcEjec,
    getCalcAgrp,
    getCalcEjec,
    getLogsBatch,
    insertCalcAgr,
    insertCalcEjec,
    updateCalcAgr,
    updateCalcEjec
  } = container.get(JobsDesatendidosRepository)

  const [menuId, setMenuId] = useState(0)
  const [openEjecDeleteDialog, setOpenEjecDeleteDialog] = useState(false)
  const [openCreateEditEjecDialog, setOpenCreateEditEjecDialog] =
    useState(false)
  const [openCreateEditAgrDialog, setOpenCreateEditAgrDialog] = useState(false)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [filterAgr, setFilterAgr] = useState('')

  const {
    data: logsBatchData,
    isFetching: islogsBatchPending,
    refetch: refetchlogsBatch
  } = useQuery({
    queryFn: () =>
      getLogsBatch().then(data =>
        data.map(el => ({
          ...el,
          fechaHora: el.fechaHora.split('T')[0] ?? ''
        }))
      ),
    queryKey: ['getLogsBatch']
  })

  const {
    data: calcAgrpData,
    isFetching: isCalcAgrpPending,
    refetch: refetchCalcAgrp
  } = useQuery({
    queryFn: getCalcAgrp,
    queryKey: ['getCalcAgrp']
  })

  const {
    data: calcEjecData,
    isFetching: isCalcEjecPending,
    refetch: refetchCalcEjec
  } = useQuery({
    queryFn: getCalcEjec,
    queryKey: ['getCalcEjec']
  })

  const selectedEjecData = useRef<CalcEjec>(calcEjecDefault)
  const selectedEjecIndex = useRef<number | null>(0)
  const selectedAgrData = useRef<CalAgr>(calAgrDefault)
  const selectedAgrIndex = useRef<number | null>(0)

  const filterCalAgr = useMemo(() => {
    if (!filterAgr.trim()) return calcAgrpData ?? []

    return (calcAgrpData ?? []).filter(item => {
      const value = prop(
        piped(
          [...calAgrMetadata].map(el => el.key),
          keys => nth(menuId, keys) as keyof CalAgr
        ),
        item
      )
      return value.toLowerCase().includes(filterAgr.toLowerCase())
    })
  }, [menuId, filterAgr, calcAgrpData])

  const deleteEjecAction = async (isOk: boolean) => {
    setOpenEjecDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteCalcEjec(selectedEjecData.current.fecha)
      setLoadingDialog(false)
      void refetchCalcEjec()
    }
  }

  const submitEjecAction = useCallback(
    async (calcEjec: CalcEjec, _: FormikHelpers<CalcEjec>) => {
      setOpenCreateEditAgrDialog(false)
      await delay(150)
      setLoadingDialog(true)
      await (selectedEjecIndex.current === null
        ? insertCalcEjec({
            fecha: calcEjec.fecha,
            peso: calcEjec.peso.toString()
          })
        : updateCalcEjec(calcEjec.fecha, calcEjec.peso.toString()))

      setLoadingDialog(false)
      void refetchCalcEjec()
    },
    []
  )

  const submitAgrAction = useCallback(
    async (calcAgr: CalAgr, _: FormikHelpers<CalAgr>) => {
      setOpenCreateEditAgrDialog(false)
      await delay(150)
      setLoadingDialog(true)
      await (selectedAgrIndex.current === null
        ? insertCalcAgr({
            dias: calcAgr.dias,
            fecha: calcAgr.fecha
          })
        : updateCalcAgr(calcAgr))
      setLoadingDialog(false)
      void refetchCalcAgrp()
    },
    []
  )

  const refetch = () => {
    void refetchlogsBatch()
    void refetchCalcAgrp()
    void refetchCalcEjec()
  }

  const handleChange = (event: SelectChangeEvent) =>
    setMenuId(Number(event.target.value))

  const handleFilterChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => setFilterAgr(event.target.value)

  return (
    <>
      {!(isCalcAgrpPending || isCalcEjecPending || islogsBatchPending) && (
        <Reload onClick={refetch} />
      )}
      <Container maxWidth={false} sx={{ mb: 4, mt: 2 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'grid' }} xs={12}>
            <TitleSubtitle title='Logs Batch' />
            {islogsBatchPending ? (
              <Loading />
            ) : (
              <Paper elevation={1} sx={paperStyle}>
                <GenericTable
                  data={logsBatchData ?? []}
                  entries={logsBatch}
                  metadata={logsBatchMetadata}
                />
              </Paper>
            )}
          </Grid>
          <Grid item sx={{ display: 'grid' }} xs={12}>
            <TitleSubtitle title='Calendario de Ejecución' />
            {isCalcEjecPending ? (
              <Loading />
            ) : (
              <Paper elevation={1} sx={paperStyle}>
                <GenericTable
                  data={calcEjecData ?? []}
                  entries={calcEjec}
                  isDeleteAction
                  isEditAction
                  metadata={calcEjecMetadata}
                  onDeleteClick={index => {
                    selectedEjecIndex.current = index
                    setOpenEjecDeleteDialog(true)
                  }}
                  onEditClick={index => {
                    selectedEjecIndex.current = index
                    selectedEjecData.current =
                      nth(index, calcEjecData ?? []) ?? calcEjecDefault
                    setOpenCreateEditEjecDialog(true)
                  }}
                />
              </Paper>
            )}
          </Grid>
          <Grid item sx={{ display: 'flex', justifyContent: 'end' }} xs={12}>
            <Button
              onClick={() => {
                selectedEjecIndex.current = null
                selectedEjecData.current = calcEjecDefault
                setOpenCreateEditEjecDialog(true)
              }}
              startIcon={<PlusCircleOutlined />}
              variant='contained'
            >
              Nuevo
            </Button>
          </Grid>
          <Grid item sx={{ display: 'grid' }} xs={12}>
            <TitleSubtitle title='Calendario de Agrupación' />
            <Grid
              container
              spacing={1}
              sx={{ alignItems: 'baseline', paddingY: '10px' }}
            >
              {!isCalcAgrpPending && (
                <Grid
                  item
                  sx={{ alignItems: 'center', display: 'flex', ml: '20px' }}
                  xs={8}
                >
                  <Typography
                    sx={{ marginRight: '10px', whiteSpace: 'nowrap' }}
                    variant='subtitle1'
                  >
                    Filtro:{' '}
                  </Typography>
                  <Box sx={{ minWidth: 200 }}>
                    <FormControl fullWidth>
                      <Select
                        id='demo-simple-select'
                        onChange={handleChange}
                        value={menuId.toString()}
                      >
                        {[...calAgrMetadata].map((key, index) => (
                          <MenuItem key={index} value={index}>
                            {key.header}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                  <Box sx={{ ml: { md: 1, xs: 0 }, width: '100%' }}>
                    <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                      <OutlinedInput
                        aria-describedby='header-search-text'
                        endAdornment={
                          <InputAdornment position='end' sx={{ mr: -0.5 }}>
                            <SearchOutlined />
                          </InputAdornment>
                        }
                        id='header-search'
                        inputProps={{
                          'aria-label': 'weight'
                        }}
                        onChange={handleFilterChange}
                        placeholder='Buscar'
                      />
                    </FormControl>
                  </Box>
                </Grid>
              )}
            </Grid>
            {isCalcAgrpPending ? (
              <Loading />
            ) : (
              <Paper elevation={1} sx={paperStyle}>
                <GenericTable
                  data={filterCalAgr}
                  entries={calAgr}
                  isEditAction
                  metadata={calAgrMetadata}
                  onEditClick={index => {
                    selectedAgrIndex.current = index
                    selectedAgrData.current =
                      nth(index, calcAgrpData ?? []) ?? calAgrDefault
                    setOpenCreateEditAgrDialog(true)
                  }}
                />
              </Paper>
            )}
          </Grid>
          <Grid item sx={{ display: 'flex', justifyContent: 'end' }} xs={12}>
            <Button
              onClick={() => {
                selectedAgrIndex.current = null
                selectedAgrData.current = calAgrDefault
                setOpenCreateEditAgrDialog(true)
              }}
              startIcon={<PlusCircleOutlined />}
              variant='contained'
            >
              Nuevo
            </Button>
          </Grid>
        </Grid>
      </Container>
      <DeleteDialog onClick={deleteEjecAction} open={openEjecDeleteDialog} />
      <LoadingDialog open={loadingDialog} />
      <DialogForm<CalcEjec>
        onClose={() => setOpenCreateEditEjecDialog(false)}
        onSubmit={submitEjecAction}
        open={openCreateEditEjecDialog}
        values={selectedEjecData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<CalcEjec>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<CalcEjec>
              field='peso'
              label='Peso:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.peso.toString()}
            />
          </>
        )}
      </DialogForm>
      <DialogForm<CalAgr>
        onClose={() => setOpenCreateEditAgrDialog(false)}
        onSubmit={submitAgrAction}
        open={openCreateEditAgrDialog}
        values={selectedAgrData.current}
      >
        {({ setFieldValue, values }) => (
          <>
            <DateFormControl<CalAgr>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <DateFormControl<CalAgr>
              field='dias'
              label='Días: '
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.dias)}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
