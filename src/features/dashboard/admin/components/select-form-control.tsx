import {
  FormControlLabel,
  Grid,
  Select,
  type SelectChangeEvent
} from '@mui/material'
import type { FocusEvent } from 'react'

type Props<T extends Record<string, unknown>> = {
  children?: ReactNode
  disabled?: boolean
  field: keyof T
  label: string
  multiline?: boolean
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void
  onChange?: (e: SelectChangeEvent) => void
  required?: boolean
  type?: 'number' | 'text'
  value: string
}

export const SelectFormControl = <T extends Record<string, unknown>>({
  children,
  disabled = false,
  field,
  label,
  multiline = false,
  onBlur,
  onChange,
  required,
  type,
  value
}: Props<T>) => (
  <Grid item sx={{ marginLeft: '-16px' }} xs={12}>
    <FormControlLabel
      control={
        <Select
          disabled={disabled}
          multiline={multiline}
          name={field as string}
          onBlur={onBlur}
          onChange={onChange}
          required={required}
          size='small'
          sx={{ width: '200px' }}
          type={type}
          value={value}
        >
          {children}
        </Select>
      }
      label={label}
      labelPlacement='start'
      sx={{ display: 'flex', justifyContent: 'space-between' }}
    />
  </Grid>
)
