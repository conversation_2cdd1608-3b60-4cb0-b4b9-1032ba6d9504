import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

import { DetallesDerivadosCobertura } from '../models/detalles-derivados-cobertura'

@injectable()
export class DetallesDerivdosCoberturaRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/rentafija'

  readonly getDetallesDerivadosCobertura = (isin: string, tradeGroup: string) =>
    this.api.client
      .get<DetallesDerivadosCobertura[]>(
        `${this.route}/DetalleDerivadosCobertura/${this.attachDate()}/${isin}/${tradeGroup}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
