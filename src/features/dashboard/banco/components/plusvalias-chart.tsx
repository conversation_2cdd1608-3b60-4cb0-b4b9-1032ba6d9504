/* eslint-disable import/default */
import { plusvaliasChartOpt } from '@d/banco/data/plusvalias-chart'
import { BancoRepository } from '@d/banco/repository/banco-repository'
import { AppChart } from '@d/common/components/Chart'
import { Loading } from '@d/common/components/Loading'
import { Box, Grid, Paper } from '@mui/material'
import type { ApexOptions } from 'apexcharts'
import { useEffect, useRef, useState } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

const paperStyle = {
  alignItems: 'left',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

export const PlusvaliasChart = () => {
  const day = calendarStore.actualDay.use()
  const [isLoading, setIsLoading] = useState(false)

  const { getControlDPVRF } = container.get(BancoRepository)

  const series = useRef<ApexAxisChartSeries>([])
  const options = useRef<ApexOptions>(plusvaliasChartOpt)

  const fetchChart = async () => {
    setIsLoading(true)

    const plusvalias = await getControlDPVRF()

    series.current = [{ data: plusvalias.map(el => el.plusvalia) }]
    options.current = {
      ...options.current,
      xaxis: {
        ...options.current.xaxis,
        categories: plusvalias.map(el => el.titulo ?? '')
      }
    }
    setIsLoading(false)
  }

  useEffect(() => {
    void fetchChart()
  }, [day])

  return (
    <Grid item xs={6}>
      <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
        <TitleSubtitle title='Plusvalías Latentes DPV Renta Fija' />
      </div>
      <Paper elevation={10} sx={paperStyle}>
        <Box id='chart' sx={{ bgcolor: 'transparent' }}>
          {isLoading ? (
            <Loading />
          ) : (
            <AppChart
              height={250}
              options={options.current}
              series={series.current}
              type='bar'
            />
          )}
        </Box>
      </Paper>
    </Grid>
  )
}
