import { route } from '@react-router/dev/routes'

export const riesgoMercadoRouter = [
  route(
    'riesgo-mercado/exposicion',
    'features/dashboard/riesgo-mercado/pages/exposicion.tsx'
  ),
  route(
    'riesgo-mercado/initial-margin',
    'features/dashboard/riesgo-mercado/pages/initial-margin.tsx'
  ),
  route(
    'riesgo-mercado/spread-credito',
    'features/dashboard/riesgo-mercado/pages/spread-credito.tsx'
  ),
  route(
    'riesgo-mercado/limites',
    'features/dashboard/riesgo-mercado/pages/limites.tsx'
  ),
  route(
    'riesgo-mercado/analisis-sensibilidades',
    'features/dashboard/riesgo-mercado/pages/analisis-sensibilidades.tsx'
  ),
  route(
    'riesgo-mercado/stress',
    'features/dashboard/riesgo-mercado/pages/stress.tsx'
  ),
  route(
    'riesgo-mercado/backtesting',
    'features/dashboard/riesgo-mercado/pages/backtesting.tsx'
  ),
  route(
    'riesgo-mercado/var-marginal',
    'features/dashboard/riesgo-mercado/pages/var-marginal.tsx'
  ),
  route(
    'riesgo-mercado/var-area',
    'features/dashboard/riesgo-mercado/pages/var-area.tsx'
  )
]
