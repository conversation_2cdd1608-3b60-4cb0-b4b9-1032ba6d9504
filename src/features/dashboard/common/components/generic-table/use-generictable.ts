/* eslint-disable sonarjs/anchor-precedence */
/* eslint-disable sonarjs/no-selector-parameter */
import type { SxProps } from '@mui/material'
import type { Type } from 'arktype'
import { piped } from 'rambdax'
import { useCallback, useMemo, useState } from 'react'

import { numberFormat } from '~/utils/format-number'
import { getProps } from '~/utils/models'

type CellConfig = {
  align: 'center' | 'left' | 'right'
  isBold?: boolean
  isPercent?: boolean
  sx?: SxProps
}

type TableMetadata = { key: string; title: string; type: string }

const cellConfig: Record<string, CellConfig> = {
  cobertura: { align: 'center', isBold: true, sx: { fontWeight: 'bold' } },
  eSTER: { align: 'center', isPercent: true, sx: { fontWeight: 'bold' } },
  eSTR: { align: 'center', sx: { fontWeight: 'bold' } },
  instrumentID: { align: 'right' },
  liquidativo: { align: 'right', isBold: true, sx: { fontWeight: 'bold' } },
  tipo: {
    align: 'center',
    isBold: true,
    isPercent: true,
    sx: { fontWeight: 'bold' }
  },
  tipoAnual: { align: 'center', isPercent: true },
  tipoMedio: { align: 'center', isPercent: true },
  tipoMes: { align: 'center', isPercent: true },
  umbralContratacion: { align: 'right' },
  umbralSeguridad: { align: 'right' }
}

export const getCellConfig = (
  tableMetadata: TableMetadata,
  negativeNumber = false,
  lastOne = false,
  tipoPercent = false
): CellConfig => {
  const config = cellConfig[tableMetadata.key]

  if (tipoPercent && tableMetadata.key === 'tipo')
    return { align: 'center', isPercent: true }
  if (config !== undefined) return config
  if (negativeNumber)
    return {
      align: 'right',
      sx: { color: '#ff6363', fontWeight: lastOne ? 'bold' : 'normal' }
    }
  if (tableMetadata.type === 'number')
    return { align: 'right', sx: { fontWeight: lastOne ? 'bold' : 'normal' } }
  return { align: 'left', sx: { fontWeight: lastOne ? 'bold' : 'normal' } }
}

export const getValueFormat = (value: unknown, isPercentage = false) => {
  if (isPercentage) return `${numberFormat(Number(value) * 100, 3)}%`
  else if (typeof value === 'number') return numberFormat(Number(value), 0)
  else return value
}

export const useGenericTable = (
  data: Record<string, unknown>[],
  entries: Type<object>,
  metadata: Set<{ header: string; key: string }>,
  paginationSize: number,
  isDeleteAction: boolean,
  isEditAction: boolean
) => {
  const [paginationActual, setPaginationActual] = useState(1)

  const tableMetadata = useMemo<TableMetadata[]>(() => {
    const realMetadata = [...metadata].flatMap(data =>
      piped(
        getProps(entries.toJSON()).find(el => el.key === data.key),
        entry =>
          entry === undefined
            ? []
            : [{ key: data.key, title: data.header, type: entry.value }]
      )
    )
    if (isEditAction || isDeleteAction)
      realMetadata.push({ key: 'actions', title: 'Acciones', type: 'string' })

    return realMetadata
  }, [])

  const totalRows = useMemo(() => data.length, [data])

  const paginationCount = useMemo(
    () => Math.ceil(totalRows / paginationSize),
    [totalRows]
  )

  const slicedData = useMemo(
    () =>
      data.slice(
        (paginationActual - 1) * paginationSize,
        paginationSize * paginationActual
      ),
    [data, paginationActual]
  )

  const realIndex = useCallback(
    (index: number) =>
      paginationActual * paginationSize - (paginationSize - index),
    [paginationActual]
  )

  return {
    paginationActual,
    paginationCount,
    realIndex,
    setPaginationActual,
    slicedData,
    tableMetadata,
    totalRows
  }
}
