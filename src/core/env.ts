/* eslint-disable sonarjs/no-clear-text-protocols */
import { type } from 'arktype'

export const envData = type({
  DEV: 'boolean = false',
  VITE_AD_SERVER: 'string = "http://172.26.245.22:8083"',
  VITE_API_SERVER: 'string = "http://172.26.245.5:8080"'
})

export const ENV =
  envData(import.meta.env) instanceof type.errors
    ? {
        DEV: false,
        VITE_AD_SERVER: 'http://172.26.245.22:8083',
        VITE_API_SERVER: 'http://172.26.245.5:8080'
      }
    : import.meta.env
