import type { Estadistica } from '@d/riesgo-mercado/models/estadistica'
import type { HistoricoVar } from '@d/riesgo-mercado/models/historico-var'
import type { VarArea } from '@d/riesgo-mercado/models/var-area'
import type { VarParametrico } from '@d/riesgo-mercado/models/var-parametrico'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class VarAreaRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/vararea'

  readonly getEstadisticaInv = () =>
    this.api.client
      .get<Estadistica[]>(`${this.route}/shinv/${this.attachDate()}/Inv`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getEstadisticaNeg = () =>
    this.api.client
      .get<Estadistica[]>(`${this.route}/hineg/${this.attachDate()}/Neg`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getHistoricoVar = (
    portfolio: 'Inversion' | 'Negociacion' = 'Inversion',
    simulacion: 'SH' | 'SP' = 'SH'
  ) =>
    this.api.client
      .get<HistoricoVar[]>(
        `${this.route}/historicalvar/${this.attachDate()}/${portfolio}/${simulacion}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getVarArea = (
    portfolio: 'Inversion' | 'Negociacion' = 'Inversion'
  ) =>
    this.api.client
      .get<VarArea[]>(`${this.route}/varnew/${this.attachDate()}/${portfolio}`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getVarParametrico = (
    portfolio: 'Inversion' | 'Negociacion' = 'Inversion'
  ) =>
    this.api.client
      .get<VarParametrico[]>(
        `${this.route}/factores/${this.attachDate()}/${portfolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
