import { ExposicionGroups } from '@d/riesgo-mercado/models/exposicion-groups'
import { ExposicionProducts } from '@d/riesgo-mercado/models/exposicion-products'
import { inject, injectable } from 'inversify'

import { calendarStore } from '@/common/store/calendar-store'
import { ApiModule } from '~/modules/api-module'

@injectable()
export class ExposicionRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/exposicion'

  readonly getGroups = (portafolio: 'Inv' | 'Neg' = 'Inv') =>
    this.api.client
      .get<ExposicionGroups[]>(
        `${this.route}/groups/${this.attachDate()}/${portafolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getProducts = (portafolio: 'Inv' | 'Neg' = 'Inv') =>
    this.api.client
      .get<ExposicionProducts[]>(
        `${this.route}/products/${this.attachDate()}/${portafolio}`
      )
      .then(apires => apires.data)
      .then(data => data ?? [])

  private readonly attachDate = () => calendarStore.formattedDay.get()
}
