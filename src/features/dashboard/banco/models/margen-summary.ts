/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const margenSummary = type({
  orden: 'number',
  rentabilidadAnual: 'number',
  rentabilidadDia: 'number',
  rentabilidadMes: 'number',
  saldo: 'number',
  titulo: 'string'
})

export const margenSummaryMetadata = new Set([
  {
    header: 'Título',
    key: 'titulo'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Rentab. Día',
    key: 'rentabilidadDia'
  },
  {
    header: 'Rentab. Mes',
    key: 'rentabilidadMes'
  },
  {
    header: 'Rentab. Año',
    key: 'rentabilidadAnual'
  }
])

export type MargenSummary = typeof margenSummary.infer

export const margenSummaryValidate = (attempt?: MargenSummary) =>
  margenSummary(attempt) instanceof type.errors
    ? resetObject(margenSummary)
    : attempt
