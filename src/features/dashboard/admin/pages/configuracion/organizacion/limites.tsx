import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Limites,
  limites,
  limitesDefault,
  limitesMetadata
} from '@d/admin/models/limites'
import { LimitesRepository } from '@d/admin/repositories/org-limites-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { deleteLimites, getLimites, insertLimites, updateLimites } =
    container.get(LimitesRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(limitesDefault)

  const {
    data: allLimitesData,
    isFetching: isGetLimitesPending,
    refetch: refetchLimites
  } = useQuery({
    queryFn: getLimites,
    queryKey: ['getLimites']
  })

  const submitAction = async (incoming: Limites, _: FormikHelpers<Limites>) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertLimites(incoming)
      : updateLimites(selectedData.current.limiteID.toString(), incoming))
    setLoadingDialog(false)
    void refetchLimites()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteLimites(selectedData.current.limiteID.toString())
      setLoadingDialog(false)
      void refetchLimites()
    }
  }

  return isGetLimitesPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchLimites()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = limitesDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allLimitesData ?? []}
                entries={limites}
                isDeleteAction
                isEditAction
                metadata={limitesMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allLimitesData ?? []) ?? limitesDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Limites>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          nombre: Yup.string().max(5).required('Nombre is required'),
          orden: Yup.number().required('Orden is required'),
          titulo: Yup.string().max(25).required('Titulo is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Limites>
              field='nombreCorto'
              label='Nombre Corto: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.nombreCorto.toString()}
            />
            <TextFormControl<Limites>
              field='nombreLargo'
              label='Nombre Largo: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.nombreLargo.toString()}
            />
            <SelectFormControl<Limites>
              field='tipoLimite'
              label='Tipo límite: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.tipoLimite.toString()}
            >
              {/*activoPasivo.map((act, ind) => (
                <MenuItem key={ind} value={ind}>
                  {act.estado}
                </MenuItem>
              ))*/}
            </SelectFormControl>
            <TextFormControl<Limites>
              field='rrcc'
              label='RRCC: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.rrcc?.toString() ?? '0'}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
