import { FormControlLabel, Grid, TextField } from '@mui/material'
import type { ChangeEvent, FocusEvent } from 'react'

type Props<T extends Record<string, unknown>> = {
  disabled?: boolean
  field: keyof T
  label: string
  multiline?: boolean
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void
  required?: boolean
  type?: 'number' | 'text'
  value: string
}

export const TextFormControl = <T extends Record<string, unknown>>({
  disabled = false,
  field,
  label,
  multiline = false,
  onBlur,
  onChange,
  required,
  type,
  value
}: Props<T>) => (
  <Grid item sx={{ marginLeft: '-16px' }} xs={12}>
    <FormControlLabel
      control={
        <TextField
          disabled={disabled}
          multiline={multiline}
          name={field as string}
          onBlur={onBlur}
          onChange={onChange}
          required={required}
          size='small'
          sx={{ width: '200px' }}
          type={type}
          value={value}
        />
      }
      label={label}
      labelPlacement='start'
      sx={{ display: 'flex', justifyContent: 'space-between' }}
    />
  </Grid>
)
