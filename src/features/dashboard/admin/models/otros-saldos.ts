/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'
import dayjs from 'dayjs'

import { resetObject } from '~/utils/models'

export const otrosSaldos = type({
  balance: 'string',
  currencyID: 'string',
  evento: 'string',
  fecha: 'string',
  osid: 'number',
  producto: 'string',
  saldo: 'number',
  tipo: 'number',
  tradeGroup: 'string'
})

export const otrosSaldosMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Evento',
    key: 'evento'
  },
  {
    header: 'Currency',
    key: 'currencyID'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Tipo',
    key: 'tipo'
  },
  {
    header: 'Balance',
    key: 'balance'
  }
])

export type OtrosSaldos = typeof otrosSaldos.infer

export const otrosSaldosDefault: OtrosSaldos = {
  balance: '',
  currencyID: '',
  evento: '',
  fecha: dayjs().format('YYYY-MM-DD'),
  osid: 0,
  producto: '',
  saldo: 0,
  tipo: 0,
  tradeGroup: ''
}

export const otrosSaldosValidate = (attempt?: OtrosSaldos) =>
  otrosSaldos(attempt) instanceof type.errors ? resetObject(attempt) : attempt
