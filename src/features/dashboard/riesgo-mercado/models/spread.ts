/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const spread = type({
  curveID: 'string?',
  delta: 'number',
  titulo: 'string?'
})

export const spreadAreaMetadata = new Set([
  {
    header: 'titulo',
    key: '<PERSON><PERSON><PERSON><PERSON>'
  },
  {
    header: 'delta',
    key: 'Delta'
  }
])

export const spreadSectoresMetadata = new Set([
  {
    header: 'curveID',
    key: 'Curve ID'
  },
  {
    header: 'delta',
    key: 'Delta'
  }
])

export type Spread = typeof spread.infer

export const spreadValidate = (attempt?: Spread) =>
  spread(attempt) instanceof type.errors ? resetObject(attempt) : attempt
