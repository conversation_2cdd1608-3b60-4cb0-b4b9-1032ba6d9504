import { AnimateButton } from '@c/components/AnimateButton'
import { MainCard } from '@c/components/MainCard'
import { Button, CardMedia, Stack, Typography } from '@mui/material'
import { useNavigate } from 'react-router'

import avatar from '~/assets/avatar-group.png'

export const NavCard = () => {
  const nav = useNavigate()

  return (
    <MainCard sx={{ bgcolor: 'grey.50', m: 3 }}>
      <Stack alignItems='center' spacing={2.5}>
        <CardMedia component='img' image={avatar} />
        <Stack alignItems='center'>
          <Typography variant='h5'>¿Necesita Ayuda?</Typography>
        </Stack>
        <AnimateButton>
          <Button
            onClick={() => nav('/docs')}
            size='small'
            type='button'
            variant='shadow'
          >
            Documentación
          </Button>
        </AnimateButton>
      </Stack>
    </MainCard>
  )
}
