export enum Role {
  ADMIN = 'admin',
  USER = 'user'
}

export const roleFromString = (role: string): Role => {
  switch (role) {
    case 'admin': {
      return Role.ADMIN
    }
    // eslint-disable-next-line unicorn/no-useless-switch-case
    case 'user':
    default: {
      return Role.USER
    }
  }
}

export type Auth = {
  isInitialized: boolean
  isLoggedIn: boolean
  token?: string
  user: Partial<UserProfile> | null
}

export type UserProfile = {
  avatar: string
  email: string
  id: string
  image: string
  name: string
  role: Role
  tier: string
}
