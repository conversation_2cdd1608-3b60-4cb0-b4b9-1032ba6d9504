{"name": "frontend-mf2", "version": "1.0.0", "type": "module", "private": true, "scripts": {"build": "react-router build", "dev": "react-router dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "postinstall": "bun run typegen", "prettier": "prettier --write .", "prettier-fix": "prettier --write .", "start": "NODE_ENV=production bun run ./build/server/index.js", "tsc": "tsc --noEmit -p . --pretty", "typegen": "react-router typegen", "unused": "knip --max-issues 1"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@davstack/store": "^1.4.6", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/common": "^5.11.5", "@mcansh/remix-fastify": "^4.1.0", "@mui/material": "5.17.1", "@mui/x-date-pickers": "^7.29.4", "@react-router/node": "7.6.2", "@react-router/serve": "7.6.2", "@tanstack/react-query": "^5.80.7", "apexcharts": "^4.7.0", "apisauce": "^3.1.1", "arktype": "^2.1.20", "dayjs": "^1.11.13", "formik": "^2.4.6", "framer-motion": "^12.18.1", "inversify": "^7.5.2", "isbot": "^5.1.28", "rambdax": "^11.3.1", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-device-detect": "^2.2.3", "react-dom": "^19.1.0", "react-intl": "7.1.11", "react-router": "7.6.2", "react-tabs": "^6.1.0", "reflect-metadata": "^0.2.2", "simplebar": "^6.3.1", "simplebar-react": "^3.3.1", "usehooks-ts": "^3.1.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@million/lint": "^1.0.14", "@react-router/dev": "7.6.2", "@types/eslint": "9.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "babel-plugin-react-compiler": "^19.0.0-beta-21e868a-20250216", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-typescript-metadata": "^0.3.2", "eslint": "8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-functional": "6.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.14.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.0.0-beta-21e868a-20250216", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-reactotron": "^0.1.7", "eslint-plugin-security": "^3.0.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-toplevel": "^1.2.0", "eslint-plugin-unicorn": "56.0.1", "knip": "^5.61.1", "prettier": "^3.5.3", "prettier-plugin-pkg": "^0.21.1", "prettier-plugin-sh": "^0.17.4", "react-router-devtools": "5.0.6", "typescript": "^5.8.3", "unplugin-auto-import": "^19.3.0", "vite-bundle-analyzer": "^0.22.2", "vite-plugin-babel": "^1.3.1", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-compression2": "^2.0.1", "vite-plugin-javascript-obfuscator": "^3.1.0", "vite-plugin-qrcode": "^0.2.4", "vite-tsconfig-paths": "^5.1.4"}}