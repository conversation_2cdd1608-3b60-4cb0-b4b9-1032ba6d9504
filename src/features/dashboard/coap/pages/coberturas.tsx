import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import type { Derivados } from '@d/common/models/coberturas'
import { CoberturasRepository } from '@d/common/repository/coberturas-repository'
import { Coberturas } from '@d/common/templates/coberturas/coberturas'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getBonos, getDerivados } = container.get(CoberturasRepository)
  const [isLoading, setIsLoading] = useState(false)
  const [derivadosData, setDerivadosData] = useState<Derivados[]>([])

  const {
    data: bonosData,
    isFetching: isBonosPending,
    refetch: refetchBonos
  } = useQuery({
    queryFn: () => getBonos('4'),
    queryKey: ['getBonos']
  })

  const fetchDerivados = async (isin: string, book: string) => {
    setDerivadosData([])
    setIsLoading(true)
    setDerivadosData(await getDerivados('4', isin, book))
    setIsLoading(false)
  }

  const refetch = () => {
    void refetchBonos()
    setDerivadosData([])
  }

  useEffect(() => {
    refetch()
  }, [day])

  return isBonosPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={refetch} />
      <Coberturas
        bonosData={bonosData ?? []}
        derivadosData={derivadosData}
        onBonosClick={fetchDerivados}
      />
      <LoadingDialog open={isLoading} />
    </>
  )
}
