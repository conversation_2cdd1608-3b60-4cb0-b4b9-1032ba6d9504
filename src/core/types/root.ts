import type { SvgIconTypeMap } from '@mui/material'
import type { OverridableComponent } from '@mui/material/OverridableComponent'
import type { ComponentClass, FunctionComponent } from 'react'

import type { CalendarProps } from './calendar'
import type { MenuProps } from './menu'

export type GenericCardProps = {
  color?: string
  content?: string
  dateTime?: string
  iconPrimary?: OverrideIcon
  image?: string
  primary?: number | string
  secondary?: string
  size?: string
  title?: string
}

export type OverrideIcon =
  | ComponentClass<unknown>
  | FunctionComponent<unknown>
  // eslint-disable-next-line sonarjs/no-useless-intersection
  | (OverridableComponent<SvgIconTypeMap<object>> & {
      muiName: string
    })

export type RootStateProps = {
  auth: Record<string, unknown>
  calendar: CalendarProps
  menu: MenuProps
}
