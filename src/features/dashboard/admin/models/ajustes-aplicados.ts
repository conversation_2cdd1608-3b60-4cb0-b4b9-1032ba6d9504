/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const ajusteAplicados = type({
  accountingAreaID: 'string',
  ajusteDia: 'boolean',
  ajusteIntDia: 'number',
  ajusteMargen: 'number',
  ajusteMes: 'boolean',
  balance: 'string',
  currencyID: 'string',
  epigrafeID: 'number',
  fecha: 'string',
  horaAjuste: 'string',
  mfAID: 'number',
  nombre: 'string',
  nombreInformeGlobal: 'string',
  orden: 'number',
  peso: 'number',
  producto: 'string',
  tradeGroup: 'string',
  usuario: 'string'
})

export const ajusteAplicadosMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Nombre',
    key: 'nombre'
  },
  {
    header: 'Hora Ajuste',
    key: 'horaAjuste'
  },
  {
    header: 'Usuario',
    key: 'usuario'
  },
  {
    header: 'Accounting Area ID',
    key: 'accountingAreaID'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Balance',
    key: 'balance'
  },
  {
    header: 'Ajuste Dia',
    key: 'ajusteDia'
  },
  {
    header: 'Ajuste Mes',
    key: 'ajusteMes'
  },
  {
    header: 'Ajuste Saldo',
    key: 'ajusteSaldo'
  },
  {
    header: 'Ajuste Int Dia',
    key: 'ajusteIntDia'
  },
  {
    header: 'Ajuste Margen',
    key: 'ajusteMargen'
  },
  {
    header: 'Nombre Informe Global',
    key: 'nombreInformeGlobal'
  }
])

export const ajusteAplicadosAnoMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Producto',
    key: 'producto'
  },
  {
    header: 'Ajuste Dia',
    key: 'ajusteDia'
  },
  {
    header: 'Ajuste Mes',
    key: 'ajusteMes'
  },
  {
    header: 'Balance',
    key: 'balance'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Ajuste Saldo',
    key: 'ajusteSaldo'
  },
  {
    header: 'Ajuste Int Dia',
    key: 'ajusteIntDia'
  },
  {
    header: 'Ajuste Margen',
    key: 'ajusteMargen'
  },
  {
    header: 'Accounting Area ID',
    key: 'accountingAreaID'
  },
  {
    header: 'Currency ID',
    key: 'currencyID'
  }
])

export type AjusteAplicados = typeof ajusteAplicados.infer

export const ajusteAplicadosValidate = (attempt: AjusteAplicados) =>
  ajusteAplicados(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
