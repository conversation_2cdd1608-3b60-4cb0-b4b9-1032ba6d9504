import 'reflect-metadata'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import {
  Links,
  type LinksFunction,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration
} from 'react-router'
import simplebar from 'simplebar-react/dist/simplebar.min.css?url'

import { Locales } from '@/common/components/Locales'
//import { Locales } from '@/common/components/Locales'
import { AuthProvider } from '@/common/contexts/AuthContext'
import { ResourceProvider } from '@/common/contexts/ResourceContext'

export const links: LinksFunction = () => [
  { href: simplebar, rel: 'stylesheet' }
]

export default () => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: { queries: { staleTime: 60 * 1000 } }
      })
  )

  return (
    <html lang='es'>
      <head>
        <meta charSet='utf-8' />
        <meta content='width=device-width, initial-scale=1' name='viewport' />
        <link href='/LogoIcon.png' rel='icon' type='image/png' />
        <Meta />
        <Links />
      </head>
      <body>
        <main>
          <ResourceProvider>
            <AuthProvider>
              <Locales>
                <QueryClientProvider client={queryClient}>
                  <Outlet />
                </QueryClientProvider>
              </Locales>
            </AuthProvider>
          </ResourceProvider>
        </main>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}
