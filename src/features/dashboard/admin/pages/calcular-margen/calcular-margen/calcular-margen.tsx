import { CalculatorOutlined, DeleteOutlined } from '@ant-design/icons'
import { OverlayAlert } from '@d/admin/components/alert'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import { calcMargen, calcMargenMetadata } from '@d/admin/models/calc-margen'
import {
  logsCalcMargen,
  logsCalcMargenMetadata
} from '@d/admin/models/logs-calcmargen'
import { referencia, referenciaMetadata } from '@d/admin/models/referencia'
import {
  resumenImportacion,
  resumenImportacionMetadata
} from '@d/admin/models/resumen-importacion'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import {
  alpha,
  Box,
  Button,
  ButtonGroup,
  Container,
  FormControl,
  FormControlLabel,
  Grid,
  LinearProgress,
  Link,
  MenuItem,
  Paper,
  Select,
  styled,
  Switch,
  Typography
} from '@mui/material'
import { green, yellow } from '@mui/material/colors'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import dayjs from 'dayjs'
import * as Yup from 'yup'

import { ConfirmDialog } from '@/common/components/ConfirmDialog'
import { paperStyle } from '~/resources/config/paper'

import { calcularMargenController } from './calcular-margen-controller'

const ButtonOpts = styled(Button)(() => ({
  '&:hover': { backgroundColor: yellow[300] }
}))

const GreenSwitch = styled(Switch)(({ theme }) => ({
  '& .MuiSwitch-switchBase.Mui-checked': {
    '&:hover': {
      backgroundColor: alpha(green[600], theme.palette.action.hoverOpacity)
    },
    color: green[600]
  },
  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
    backgroundColor: green[600]
  }
}))

export default () => {
  const {
    available,
    componentDate,
    dialogBonos,
    dialogDepos,
    dialogDerivados,
    dialogESP,
    dialogEster,
    dialogOtros,
    dialogPrestamos,
    dialogRepos,
    isFundingRatePending,
    isLoading,
    isLogsPending,
    isReferenciaPending,
    isResumenImportacionPending,
    isUltimaImportacionPending,
    logsData,
    menuEsterId,
    openConfDial,
    referenciaData,
    refetch,
    resumenImportacionData,
    selData,
    setComponentDate,
    setDialogBonos,
    setDialogDepos,
    setDialogDerivados,
    setDialogEster,
    setDialogOtros,
    setDialogPrestamos,
    setDialogRepos,
    setEster,
    setEsterESP,
    setOpenConfDial,
    setSelData,
    setupDialogs,
    setupMargenFinanciero,
    showAlert,
    submitEstrAction,
    ultimaImportacionData,
    updateAvailable
  } = calcularMargenController()

  return (
    <>
      <Reload onClick={refetch} />
      <OverlayAlert open={showAlert} />
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        {isLoading && <LinearProgress sx={{ mb: '15px' }} />}
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={8}>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker
                onChange={e => setComponentDate(e ?? dayjs())}
                slotProps={{ textField: { size: 'small' } }}
                sx={{ mt: '4px' }}
                value={componentDate}
              />
            </LocalizationProvider>
            <Box sx={{ marginX: '5px', minWidth: 20 }}>
              <FormControl fullWidth>
                <Select
                  disabled={isFundingRatePending}
                  id='auto-select'
                  onChange={setEster}
                  value={menuEsterId}
                >
                  <MenuItem value={0}>Automático</MenuItem>
                  <MenuItem value={1}>Manual</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>
          <Grid item sx={{ display: 'flex', justifyContent: 'end' }} xs={4}>
            <FormControlLabel
              checked={available}
              control={<GreenSwitch onClick={() => setOpenConfDial(true)} />}
              label='Desactivar/Activar: '
              labelPlacement='start'
            />
          </Grid>
          <Grid item xs={8}>
            <ButtonGroup aria-label='Margen button group' variant='outlined'>
              <ButtonOpts onClick={() => setDialogDepos(true)}>
                Depos
              </ButtonOpts>
              <ButtonOpts onClick={() => setDialogRepos(true)}>
                Repos
              </ButtonOpts>
              <ButtonOpts onClick={() => setDialogBonos(true)}>
                Bonos
              </ButtonOpts>
              <ButtonOpts onClick={() => setDialogOtros(true)}>
                Otros saldos
              </ButtonOpts>
              <ButtonOpts onClick={() => setDialogDerivados(true)}>
                Derivados
              </ButtonOpts>
              <ButtonOpts onClick={() => setDialogPrestamos(true)}>
                Préstamos valores
              </ButtonOpts>
            </ButtonGroup>
          </Grid>
          <Grid item sx={{ display: 'flex', justifyContent: 'end' }} xs={4}>
            <Button
              onClick={setupMargenFinanciero}
              startIcon={<CalculatorOutlined />}
              sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
              variant='contained'
            >
              Calcular Margen
            </Button>
            <Button
              startIcon={<DeleteOutlined />}
              sx={{ background: '#E5F1EE', color: 'green' }}
              variant='contained'
            >
              Eliminar
            </Button>
          </Grid>
          <Grid item xs={12}>
            {isUltimaImportacionPending ? (
              <LinearProgress />
            ) : (
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={ultimaImportacionData ?? []}
                  entries={calcMargen}
                  metadata={calcMargenMetadata}
                  noTotal={false}
                />
              </Paper>
            )}
          </Grid>
          <Grid item xs={4}></Grid>
          <Grid item xs={12}>
            <Box
              onClick={e => e.preventDefault()}
              sx={{
                '& > :not(style) ~ :not(style)': { ml: 2 },
                display: 'flex',
                justifyContent: 'center'
              }}
            >
              <Link
                href='#'
                onClick={() =>
                  setSelData({
                    logs: true,
                    referencia: false,
                    resumen: false
                  })
                }
                underline={selData.logs ? 'always' : 'hover'}
              >
                <Typography variant='h6'>Logs</Typography>
              </Link>
              <Link
                href='#'
                onClick={() =>
                  setSelData({
                    logs: false,
                    referencia: false,
                    resumen: true
                  })
                }
                underline={selData.resumen ? 'always' : 'hover'}
              >
                <Typography variant='h6'>Resumen importación</Typography>
              </Link>
              <Link
                href='#'
                onClick={() =>
                  setSelData({
                    logs: false,
                    referencia: true,
                    resumen: false
                  })
                }
                underline={selData.referencia ? 'always' : 'hover'}
              >
                <Typography variant='h6'>
                  Tasa de referencia aplicada
                </Typography>
              </Link>
            </Box>
          </Grid>
          <Grid item xs={12}>
            {selData.logs && isLogsPending && (
              <Loading title='Cargando... Por favor espere, la consulta es compleja' />
            )}
            {selData.referencia && isReferenciaPending && <Loading />}
            {selData.resumen && isResumenImportacionPending && <Loading />}
            {selData.logs && !isLogsPending && (
              <GenericTable
                data={logsData ?? []}
                entries={logsCalcMargen}
                metadata={logsCalcMargenMetadata}
              />
            )}
            {selData.referencia && !isReferenciaPending && (
              <GenericTable
                data={referenciaData ?? []}
                entries={referencia}
                metadata={referenciaMetadata}
              />
            )}
            {selData.resumen && !isResumenImportacionPending && (
              <GenericTable
                data={resumenImportacionData ?? []}
                entries={resumenImportacion}
                metadata={resumenImportacionMetadata}
              />
            )}
          </Grid>
        </Grid>
      </Container>
      <ConfirmDialog onClick={updateAvailable} open={openConfDial} />
      <ConfirmDialog
        onClick={setEsterESP}
        open={dialogESP}
        title='¿Está seguro que desea utilizar la Tasa de Referencia €STR + 8.5 pb?'
      />
      <ConfirmDialog
        onClick={isOk => setupDialogs(isOk, 'dep')}
        open={dialogDepos}
        title='¿Desea importar los depósitos (Csh) de Adativ en la fecha seleccionada ?'
      />
      <ConfirmDialog
        onClick={isOk => setupDialogs(isOk, 'rep')}
        open={dialogRepos}
        title='¿Desea importar los Repos de Adativ en la fecha seleccionada?'
      />
      <ConfirmDialog
        onClick={isOk => setupDialogs(isOk, 'bnd')}
        open={dialogBonos}
        title='¿Desea importar los Bonos de Adativ en la fecha seleccionada?'
      />
      <ConfirmDialog
        onClick={isOk => setupDialogs(isOk, 'otros')}
        open={dialogOtros}
        title='¿Desea importar otros Saldos de Adativ en la fecha seleccionada?'
      />
      <ConfirmDialog
        onClick={isOk => setupDialogs(isOk, 'swp')}
        open={dialogDerivados}
        title='¿Desea importar los Derivado de Adativ en la fecha seleccionada?'
      />
      <ConfirmDialog
        onClick={isOk => setupDialogs(isOk, 'lnd')}
        open={dialogPrestamos}
        title='¿Desea importar los Prestamos de Adativ en la fecha seleccionada?'
      />
      <DialogForm<{ spread: string; tasa: string }>
        onClose={() => setDialogEster(false)}
        onSubmit={submitEstrAction}
        open={dialogEster}
        title='Modo Manual'
        validationSchema={Yup.object().shape({
          spread: Yup.number(),
          tasa: Yup.number()
        })}
        values={{ spread: '0', tasa: '0' }}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<{ spread: string; tasa: string }>
              field='tasa'
              label='Tasa (%): '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              type='number'
              value={values.tasa}
            />
            <TextFormControl<{ spread: string; tasa: string }>
              field='spread'
              label='Spread (Puntos Básicos): '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              type='number'
              value={values.spread}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
