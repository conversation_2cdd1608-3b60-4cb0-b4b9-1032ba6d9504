/* eslint-disable sonarjs/use-type-alias */
import type { AjusteVar } from '@d/admin/models/ajuste-var'
import type { LogAjusteVar } from '@d/admin/models/log-ajustevar'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class ImportarPLyVarRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/importplvar'

  readonly conversionDatosVaR = () =>
    this.api.client
      .get<AjusteVar[]>(`${this.route}/conversionDatosVaR`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly getLogAjusteVaR = () =>
    this.api.client
      .get<LogAjusteVar[]>(`${this.route}/logAjusteVaR`)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly statAjusteVarSPSH = (
    fechaOrigen: string,
    fechaDestino: string,
    simPortfolio: string
  ) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/ajusteVarSPSH/${fechaOrigen}/${fechaDestino}/${simPortfolio}`
    )

  readonly statEliminarPlEq = (fecha: string) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/eliminarPLEQ/${fecha}`
    )

  readonly statImportVaR = (fecha: string) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/importVaR/${fecha}`
    )

  readonly statPLRFBatchDiario = (fecha: string) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/PLRFBatchDiario/${fecha}`
    )

  readonly statPLRFEliminarResultados = (fecha: string) =>
    this.api.client.post<{ message: string }>(
      `${this.route}/PLRFEliminarResultados/${fecha}`
    )
}
