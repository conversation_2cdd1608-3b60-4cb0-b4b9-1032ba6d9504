/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const portfolios = type({
  description: 'string?',
  pid: 'string?',
  portfolioID: 'string?',
  portfolioType: 'string?'
})

export type Portfolios = typeof portfolios.infer

export const portfoliosDefault: Portfolios = {
  description: '',
  pid: '',
  portfolioID: '',
  portfolioType: ''
}

export const portfoliosMetadata = new Set([
  {
    header: 'Portfolio ID',
    key: 'portfolioID'
  },
  {
    header: 'Portfolio Type',
    key: 'portfolioType'
  },
  {
    header: 'Descripción',
    key: 'description'
  }
])
