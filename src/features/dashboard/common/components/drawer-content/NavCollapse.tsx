/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable sonarjs/void-use */
/* eslint-disable sonarjs/cognitive-complexity */
import {
  BorderOutlined,
  DownOutlined,
  RightOutlined,
  UpOutlined
} from '@ant-design/icons'
import { Dot } from '@c/components/Dot'
import { SimpleBarScroll } from '@c/components/SimpleBar'
import { Transitions } from '@c/components/Transitions'
import { menuStore } from '@c/store/menu-store'
import {
  Box,
  ClickAwayListener,
  Collapse,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Paper,
  Popper,
  Typography,
  useMediaQuery
} from '@mui/material'
import { styled, useTheme } from '@mui/material/styles'
import {
  type Dispatch,
  type FC,
  type MouseEvent,
  type SetStateAction,
  useEffect,
  useMemo,
  useState
} from 'react'
import { useLocation, useNavigate } from 'react-router'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import type { NavItemType } from '~/types/menu'
import { MenuOrientation, ThemeMode } from '~/types/resources'

import { NavItem } from './NavItem'

type VirtualElement = {
  contextElement?: Element
  getBoundingClientRect: () => DOMRect
}

const PopperStyled = styled(Popper)(({ theme }) => ({
  '&:before': {
    backgroundColor: theme.palette.background.paper,
    borderBottom: `1px solid ${theme.palette.grey.A800 ?? ''}`,
    borderLeft: `1px solid ${theme.palette.grey.A800 ?? ''}`,
    content: '""',
    display: 'block',
    height: 10,
    left: -5,
    position: 'absolute',
    top: 38,
    transform: 'translateY(-50%) rotate(45deg)',
    width: 10,
    zIndex: 120
  },
  minWidth: 180,
  overflow: 'visible',
  zIndex: 1202
}))

export const NavCollapse: FC<{
  level: number
  menu: NavItemType
  parentId: string
  selectedItems: string | undefined
  selectedLevel: number
  setSelectedItems: Dispatch<SetStateAction<string | undefined>>
  setSelectedLevel: Dispatch<SetStateAction<number>>
}> = ({
  level,
  menu,
  parentId,
  selectedItems,
  selectedLevel,
  setSelectedItems,
  setSelectedLevel
}) => {
  const theme = useTheme()

  const downLG = useMediaQuery(theme.breakpoints.down('lg'))

  const { drawerOpen } = menuStore.use()
  const { menuOrientation } = useContext(ResourceContext)
  const Navigation = useNavigate()

  const [open, setOpen] = useState(false)
  const [selected, setSelected] = useState<string | null>(null)
  const [anchorEl, setAnchorEl] = useState<
    VirtualElement | (() => VirtualElement) | null
  >(null)

  const handleClick = (
    event:
      | React.MouseEvent<HTMLAnchorElement>
      | React.MouseEvent<HTMLDivElement>
      | undefined
  ) => {
    setAnchorEl(null)
    setSelectedLevel(level)
    if (drawerOpen) {
      setOpen(!open)
      setSelected(selected == null ? (menu.id ?? '') : null)
      setSelectedItems(selected == null ? (menu.id ?? '') : '')
      if (menu.url != null) void Navigation(menu.url)
    } else {
      setAnchorEl(event?.currentTarget as VirtualElement)
    }
  }

  const handlerIconLink = () => {
    // eslint-disable-next-line sonarjs/void-use
    if (!drawerOpen && menu.url != null) void Navigation(menu.url)
    // setSelected(menu.id);
  }

  const handleHover = (
    event:
      | MouseEvent<HTMLAnchorElement>
      | MouseEvent<HTMLDivElement>
      | undefined
  ) => {
    setAnchorEl(event?.currentTarget as VirtualElement)
    if (!drawerOpen) setSelected(menu.id ?? '')
  }

  const miniMenuOpened = Boolean(anchorEl)

  const handleClose = () => {
    setOpen(false)
    if (!miniMenuOpened && menu.url == null) setSelected(null)
    setAnchorEl(null)
  }

  useMemo(() => {
    if ((selected ?? '') === (selectedItems ?? '')) {
      if (level === 1) setOpen(true)
    } else {
      if (level === selectedLevel) {
        setOpen(false)
        if (!miniMenuOpened && !drawerOpen && selected == null) {
          setSelected(null)
        }
        if (drawerOpen) {
          setSelected(null)
        }
      }
    }
  }, [
    selectedItems,
    level,
    selected,
    miniMenuOpened,
    drawerOpen,
    selectedLevel
  ])

  const { pathname } = useLocation()

  useEffect(() => {
    if (pathname === menu.url) setSelected(menu.id ?? '')
    // eslint-disable-next-line
  }, [pathname])

  const checkOpenForParent = (child: NavItemType[], id: string) => {
    for (const item of child) {
      if (item.url === pathname) {
        setOpen(true)
        setSelected(id)
      }
    }
  }

  useEffect(() => {
    setOpen(false)
    if (!miniMenuOpened) setSelected(null)
    if (miniMenuOpened) setAnchorEl(null)
    if (menu.children) {
      for (const item of menu.children) {
        if ((item.children?.length ?? 0) > 0) {
          checkOpenForParent(item.children ?? [], menu.id ?? '')
        }
        if (
          pathname.includes('product-details') &&
          (item.url?.includes('product-details') ?? false)
        ) {
          setSelected(menu.id ?? '')
          setOpen(true)
        }
        if (item.url === pathname) {
          setSelected(menu.id ?? '')
          setOpen(true)
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, menu.children])

  useEffect(() => {
    if (menu.url === pathname) {
      menuStore.activeItem([menu.id ?? ''])
      setSelected(menu.id ?? '')
      setAnchorEl(null)
      setOpen(true)
    }
  }, [pathname, menu])

  const navCollapse = menu.children?.map(item => {
    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check
    switch (item.type) {
      case 'collapse': {
        return (
          <NavCollapse
            key={item.id}
            level={level + 1}
            menu={item}
            parentId={parentId}
            selectedItems={selectedItems}
            selectedLevel={selectedLevel}
            setSelectedItems={setSelectedItems}
            setSelectedLevel={setSelectedLevel}
          />
        )
      }
      case 'item': {
        return <NavItem item={item} key={item.id} level={level + 1} />
      }
      default: {
        return (
          <Typography align='center' color='error' key={item.id} variant='h6'>
            Fix - Collapse or Item
          </Typography>
        )
      }
    }
  })
  const isSelected = (selected ?? '') === (menu.id ?? '')
  const borderIcon =
    level === 1 ? <BorderOutlined style={{ fontSize: '1rem' }} /> : false
  const Icon = menu.icon ?? ''
  const menuIcon =
    menu.icon === undefined ? (
      borderIcon
    ) : (
      <Icon style={{ fontSize: drawerOpen ? '1rem' : '1.25rem' }} />
    )
  const textColor =
    theme.palette.mode === ThemeMode.DARK ? 'grey.400' : 'text.primary'
  const iconSelectedColor =
    theme.palette.mode === ThemeMode.DARK && drawerOpen
      ? theme.palette.text.primary
      : theme.palette.primary.main
  const popperId = miniMenuOpened ? `collapse-pop-${menu.id ?? ''}` : undefined
  const FlexBox = {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  }

  return menuOrientation === MenuOrientation.VERTICAL || downLG ? (
    <>
      <ListItemButton
        disableRipple
        selected={(selected ?? '') === (menu.id ?? '')}
        {...(!drawerOpen && {
          onMouseEnter: handleClick,
          onMouseLeave: handleClose
        })}
        onClick={handleClick}
        sx={{
          pl: drawerOpen ? `${(level * 28).toString()}px` : 1.5,
          py: !drawerOpen && level === 1 ? 1.25 : 1,
          ...(drawerOpen && {
            '&.Mui-selected': {
              '&:hover': {
                bgcolor:
                  theme.palette.mode === ThemeMode.DARK
                    ? 'divider'
                    : 'transparent',
                color: iconSelectedColor
              },
              bgcolor: 'primary.lighter',
              color: iconSelectedColor
            },
            '&:hover': {
              bgcolor:
                theme.palette.mode === ThemeMode.DARK
                  ? 'divider'
                  : 'primary.lighter'
            }
          }),
          ...(!drawerOpen && {
            '&.Mui-selected': {
              '&:hover': {
                bgcolor: 'transparent'
              },
              bgcolor: 'transparent'
            },
            '&:hover': {
              bgcolor: 'transparent'
            }
          })
        }}
      >
        {menuIcon !== false && (
          <ListItemIcon
            onClick={handlerIconLink}
            sx={{
              color:
                (selected ?? '') === (menu.id ?? '')
                  ? 'primary.main'
                  : textColor,
              minWidth: 28,
              ...(!drawerOpen && {
                '&:hover': {
                  bgcolor:
                    theme.palette.mode === ThemeMode.DARK
                      ? 'secondary.light'
                      : 'secondary.lighter'
                },
                alignItems: 'center',
                borderRadius: 1.5,
                height: 36,
                justifyContent: 'center',
                width: 36
              }),
              ...(!drawerOpen &&
                (selected ?? '') === (menu.id ?? '') && {
                  '&:hover': {
                    bgcolor:
                      theme.palette.mode === ThemeMode.DARK
                        ? 'primary.darker'
                        : 'primary.lighter'
                  },
                  bgcolor:
                    theme.palette.mode === ThemeMode.DARK
                      ? 'primary.900'
                      : 'primary.lighter'
                })
            }}
          >
            {menuIcon}
          </ListItemIcon>
        )}
        {(drawerOpen || (!drawerOpen && level !== 1)) && (
          <ListItemText
            primary={
              <Typography
                color={
                  (selected ?? '') === (menu.id ?? '') ? 'primary' : textColor
                }
                variant='h6'
              >
                {menu.title}
              </Typography>
            }
            secondary={
              menu.caption != null && (
                <Typography color='secondary' variant='caption'>
                  {menu.caption}
                </Typography>
              )
            }
          />
        )}
        {(drawerOpen || (!drawerOpen && level !== 1)) &&
          (miniMenuOpened || open ? (
            <UpOutlined
              style={{
                color: theme.palette.primary.main,
                fontSize: '0.625rem',
                marginLeft: 1
              }}
            />
          ) : (
            <DownOutlined style={{ fontSize: '0.625rem', marginLeft: 1 }} />
          ))}
        {!drawerOpen && (
          <PopperStyled
            anchorEl={anchorEl}
            open={miniMenuOpened}
            placement='right-start'
            popperOptions={{
              modifiers: [
                {
                  name: 'offset',
                  options: {
                    offset: [-12, 1]
                  }
                }
              ]
            }}
            style={{
              zIndex: 2001
            }}
          >
            {({ TransitionProps }) => (
              <Transitions in={miniMenuOpened} {...TransitionProps}>
                <Paper
                  sx={{
                    backgroundImage: 'none',
                    border: `1px solid ${theme.palette.divider}`,
                    boxShadow: theme.customShadows.z1,
                    mt: 1.5,
                    overflow: 'hidden'
                  }}
                >
                  <ClickAwayListener onClickAway={handleClose}>
                    <SimpleBarScroll
                      sx={{
                        maxHeight: 'calc(100vh - 170px)',
                        overflowX: 'hidden',
                        overflowY: 'auto'
                      }}
                    >
                      {navCollapse}
                    </SimpleBarScroll>
                  </ClickAwayListener>
                </Paper>
              </Transitions>
            )}
          </PopperStyled>
        )}
      </ListItemButton>
      {drawerOpen && (
        <Collapse in={open} timeout='auto' unmountOnExit>
          <List sx={{ p: 0 }}>{navCollapse}</List>
        </Collapse>
      )}
    </>
  ) : (
    <ListItemButton
      aria-describedby={popperId}
      disableRipple
      id={`boundary-${popperId ?? ''}`}
      onClick={handleHover}
      onMouseEnter={handleHover}
      onMouseLeave={handleClose}
      selected={isSelected}
      sx={{
        '&.Mui-selected': {
          bgcolor: 'transparent'
        }
      }}
    >
      <Box onClick={handlerIconLink} sx={FlexBox}>
        {menuIcon !== false && (
          <ListItemIcon
            sx={{
              color: theme.palette.secondary.dark,
              minWidth: menu.icon === undefined ? 18 : 36,
              my: 'auto'
            }}
          >
            {menuIcon}
          </ListItemIcon>
        )}
        {menuIcon === false && level !== 1 && (
          <ListItemIcon
            sx={{
              '&:hover': { bgcolor: 'transparent' },
              bgcolor: 'transparent',
              minWidth: menu.icon === undefined ? 18 : 36,
              my: 'auto'
            }}
          >
            <Dot color={isSelected ? 'primary' : 'secondary'} size={4} />
          </ListItemIcon>
        )}
        <ListItemText
          primary={
            <Typography color='inherit' sx={{ my: 'auto' }} variant='body1'>
              {menu.title}
            </Typography>
          }
        />
        {miniMenuOpened ? <RightOutlined /> : <DownOutlined />}
      </Box>

      {anchorEl && (
        <PopperStyled
          anchorEl={anchorEl}
          id={popperId}
          modifiers={[
            {
              name: 'offset',
              options: {
                offset: [-10, 0]
              }
            }
          ]}
          open={miniMenuOpened}
          placement='right-start'
          style={{
            zIndex: 2001
          }}
        >
          {({ TransitionProps }) => (
            <Transitions in={miniMenuOpened} {...TransitionProps}>
              <Paper
                sx={{
                  backgroundImage: 'none',
                  boxShadow: theme.shadows[8],
                  mt: 1.5,
                  overflow: 'hidden',
                  py: 0.5
                }}
              >
                <ClickAwayListener onClickAway={handleClose}>
                  <SimpleBarScroll
                    sx={{
                      maxHeight: 'calc(100vh - 170px)',
                      overflowX: 'hidden',
                      overflowY: 'auto'
                    }}
                  >
                    {navCollapse}
                  </SimpleBarScroll>
                </ClickAwayListener>
              </Paper>
            </Transitions>
          )}
        </PopperStyled>
      )}
    </ListItemButton>
  )
}
