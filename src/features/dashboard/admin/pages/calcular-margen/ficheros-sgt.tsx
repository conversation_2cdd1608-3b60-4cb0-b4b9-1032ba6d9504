/* eslint-disable @typescript-eslint/no-empty-function */
import {
  DeleteOutlined,
  FileExcelOutlined,
  UploadOutlined
} from '@ant-design/icons'
import { OverlayAlert } from '@d/admin/components/alert'
import { ficherosSgtOpt } from '@d/admin/data/ficheros-sgt-chart'
import {
  type CarteraNoExistente,
  carteraNoExistente,
  carteraNoExistenteMetadata
} from '@d/admin/models/cartera-noexistente'
import {
  type DatosCargadosSGT,
  datosCargadosSGT,
  datosCargadosSGTMetadata
} from '@d/admin/models/datos-cargados-sgt'
import { ficherosSgt, ficherosSgtMetadata } from '@d/admin/models/ficheros-sgt'
import { FicherosSGTRepository } from '@d/admin/repositories/ficheros-sgt-repository'
import { UploadsRepository } from '@d/admin/repositories/uploads-repository'
import { AppChart } from '@d/common/components/Chart'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'
import { customExportToCSV } from '~/utils/csv'

export default () => {
  const { getCarterasNoExistentes, getDatosCargadosSGT, getListadoSGT } =
    container.get(FicherosSGTRepository)

  const { uploadFile, uploadMultipleFile } = container.get(UploadsRepository)

  const [isLoading, setIsLoading] = useState(false)
  const [showAlert] = useState(false)
  const [datosCargados, setDatosCargados] = useState<DatosCargadosSGT[]>([])
  const [carteras, setCarteras] = useState<CarteraNoExistente[]>([])

  const fileRef = useRef(document.createElement('input'))

  const selectedDate = useRef<string | null>(null)
  const nominalSeries = useRef<ApexAxisChartSeries>([])
  const nominalOptions = useRef(ficherosSgtOpt)

  const uploadFiles = () => {
    fileRef.current.click()
    fileRef.current.addEventListener('input', async event => {
      const { files } = event.target as HTMLInputElement
      if (files && files.length === 1) {
        await uploadFile(files)
      } else if (files && files.length > 1) {
        await uploadMultipleFile(files)
      }
    })
  }

  const {
    data: getListadoSGTData,
    isFetching: isUltimaImportacionPending,
    refetch: refetchUltimaImportacion
  } = useQuery({
    queryFn: () =>
      getListadoSGT().then(data => {
        nominalSeries.current = [
          {
            data: data.map(el => el.nominal),
            name: 'Nominal'
          }
        ]
        nominalOptions.current = {
          ...ficherosSgtOpt,
          xaxis: {
            ...ficherosSgtOpt.xaxis,
            categories: data.map(el => el.fecha)
          }
        }
        return data
      }),
    queryKey: ['getUltimaImportacion']
  })

  const fetchDatosCarteras = async (index: number) => {
    selectedDate.current = nth(index, getListadoSGTData ?? [])?.fecha ?? ''
    setIsLoading(true)
    setCarteras(await getCarterasNoExistentes(selectedDate.current))
    setDatosCargados(await getDatosCargadosSGT(selectedDate.current))
    setIsLoading(false)
  }

  return isUltimaImportacionPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchUltimaImportacion()} />
      <OverlayAlert open={showAlert} />
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <input
          id='uploadFicheroSgt'
          multiple
          ref={fileRef}
          style={{ display: 'none' }}
          type='file'
        />
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={8}>
            <Button
              onClick={uploadFiles}
              startIcon={<UploadOutlined />}
              sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
              variant='contained'
            >
              Importar fichero SGT
            </Button>
            <Button
              sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
              variant='contained'
            >
              Importar
            </Button>
            <Button
              startIcon={<DeleteOutlined />}
              sx={{ background: '#E5F1EE', color: 'red' }}
              variant='contained'
            >
              Eliminar posiciones
            </Button>
          </Grid>
          <Grid item xs={4}></Grid>
          <Grid item xs={7}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '325px',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={getListadoSGTData ?? []}
                entries={ficherosSgt}
                isSelected
                metadata={ficherosSgtMetadata}
                onRowClick={fetchDatosCarteras}
              />
            </Paper>
          </Grid>
          <Grid item xs={5}>
            <Paper elevation={10} sx={paperStyle}>
              <AppChart
                height={250}
                options={nominalOptions.current}
                series={nominalSeries.current}
                type='bar'
              />
            </Paper>
          </Grid>
          <Grid item xs={9}>
            <TitleSubtitle title='Datos Cargados' />
            {isLoading ? (
              <Loading />
            ) : (
              <Paper
                elevation={1}
                sx={{
                  ...paperStyle,
                  maxHeight: '410px',
                  overflowY: 'auto'
                }}
              >
                <GenericTable
                  data={datosCargados}
                  entries={datosCargadosSGT}
                  metadata={datosCargadosSGTMetadata}
                />
              </Paper>
            )}
            <br></br>
            {!isLoading && selectedDate.current !== null && (
              <Button
                onClick={() =>
                  customExportToCSV(
                    [...datosCargadosSGTMetadata].map(el => el.header),
                    datosCargados,
                    [...datosCargadosSGTMetadata].map(el => el.key),
                    'DatosCargadosSGT.csv',
                    ';'
                  )
                }
                startIcon={<FileExcelOutlined />}
                variant='contained'
              >
                Descargar excel
              </Button>
            )}
          </Grid>
          <Grid item xs={3}>
            <TitleSubtitle title='Carteras no Encontradas' />
            {isLoading ? (
              <Loading />
            ) : (
              <Paper elevation={1} sx={paperStyle}>
                <GenericTable
                  data={carteras}
                  entries={carteraNoExistente}
                  metadata={carteraNoExistenteMetadata}
                />
              </Paper>
            )}
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
