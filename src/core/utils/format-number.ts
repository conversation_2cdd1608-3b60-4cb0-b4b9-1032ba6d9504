/* eslint-disable security/detect-unsafe-regex */
/* eslint-disable sonarjs/slow-regex */
const toFixedFix = (n: number, prec: number) => {
  const k = Math.pow(10, prec)
  return Math.round(n * k) / k
}

export const numberFormat = (
  num: number,
  decimals: number,
  dec = ',',
  sep = '.'
) => {
  const n = Number.isFinite(+num) ? +num : 0
  const prec = Number.isFinite(+decimals) ? Math.abs(decimals) : 0
  const s = (prec ? toFixedFix(n, prec) : Math.round(n)).toString().split('.')
  if (s[0] !== undefined && s[0].length > 3) {
    s[0] = s[0].replaceAll(/\B(?=(?:\d{3})+(?!\d))/g, sep)
  }
  if ((s[1] ?? '').length < prec) {
    s[1] = s[1] ?? ''
    s[1] += Array.from({ length: prec - s[1].length + 1 }).join('0')
  }
  return s.join(dec)
}
