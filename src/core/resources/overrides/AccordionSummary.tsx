import { RightOutlined } from '@ant-design/icons'
import type { Theme } from '@mui/material/styles'

export const AccordionSummary = ({ palette, spacing }: Theme) => ({
  MuiAccordionSummary: {
    defaultProps: {
      expandIcon: <RightOutlined style={{ fontSize: '0.75rem' }} />
    },
    styleOverrides: {
      content: {
        marginBottom: spacing(1.25),
        marginLeft: spacing(1),
        marginTop: spacing(1.25)
      },
      expandIconWrapper: {
        '&.Mui-expanded': { transform: 'rotate(90deg)' }
      },
      root: {
        backgroundColor: palette.secondary.lighter,
        flexDirection: 'row-reverse',
        minHeight: 46
      }
    }
  }
})
