import { AppBar, type AppBarProps } from '@mui/material'
import { styled } from '@mui/material/styles'

import { DRAWER_WIDTH } from '~/constants'

export const AppBarStyled = styled(AppBar, {
  shouldForwardProp: prop => prop !== 'open'
})<AppBarProps & { open?: boolean }>(({ open, theme }) => ({
  transition: theme.transitions.create(['width', 'margin'], {
    duration: theme.transitions.duration.leavingScreen,
    easing: theme.transitions.easing.sharp
  }),
  zIndex: theme.zIndex.drawer + 1,
  ...(!(open ?? false) && {
    width: `calc(100% - ${theme.spacing(7.5)})`
  }),
  ...((open ?? false) && {
    marginLeft: DRAWER_WIDTH,
    transition: theme.transitions.create(['width', 'margin'], {
      duration: theme.transitions.duration.enteringScreen,
      easing: theme.transitions.easing.sharp
    }),
    width: `calc(100% - ${DRAWER_WIDTH.toString()}px)`
  })
}))
