import { MargenFinancieroAjustes } from '@d/admin/layouts/input-manual/margen-financiero'
import { MargenFinancieroEdicionTemplate } from '@d/admin/templates/input-manual/margen-financiero-edicion'
import { MargenFinancieroInformeTemplate } from '@d/admin/templates/input-manual/margen-financiero-informe'

export default () => (
  <MargenFinancieroAjustes>
    {({
      date,
      isInforme,
      isMargenFinancieroAjustesPending,
      margenFinancieroAjustesData,
      margenFinancieroInformeData,
      refetch
    }) =>
      isInforme ? (
        <MargenFinancieroInformeTemplate data={margenFinancieroInformeData} />
      ) : (
        <MargenFinancieroEdicionTemplate
          ajustes={margenFinancieroAjustesData}
          data={margenFinancieroInformeData}
          date={date}
          isMargenFinancieroAjustesPending={isMargenFinancieroAjustesPending}
          refetch={refetch}
        />
      )
    }
  </MargenFinancieroAjustes>
)
