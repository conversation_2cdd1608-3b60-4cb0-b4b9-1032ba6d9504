/* eslint-disable sonarjs/cognitive-complexity */
import { DownOutlined } from '@ant-design/icons'
import { SimpleBarScroll } from '@c/components/SimpleBar'
import { Transitions } from '@c/components/Transitions'
import {
  Box,
  ClickAwayListener,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Paper,
  Popper,
  Typography,
  useMediaQuery
} from '@mui/material'
import { styled, useTheme } from '@mui/material/styles'
import {
  type Dispatch,
  type FC,
  Fragment,
  type SetStateAction,
  useEffect,
  useState
} from 'react'
import { FormattedMessage } from 'react-intl'
import { useLocation } from 'react-router'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { menuStore } from '@/common/store/menu-store'
import type { NavItemType } from '~/types/menu'
import { MenuOrientation, ThemeMode } from '~/types/resources'

import { NavCollapse } from './NavCollapse'
import { NavItem } from './NavItem'

type VirtualElement = {
  contextElement?: Element
  getBoundingClientRect: () => DOMRect
}

const PopperStyled = styled(Popper)(({ theme }) => ({
  '&:before': {
    borderColor: `${theme.palette.background.paper}  transparent transparent ${theme.palette.background.paper}`,
    borderStyle: 'solid',
    borderWidth: '6px',
    content: '""',
    display: 'block',
    height: 12,
    left: 32,
    position: 'absolute',
    top: 5,
    transform: 'translateY(-50%) rotate(45deg)',
    width: 12,
    zIndex: 120
  },
  minWidth: 180,
  overflow: 'visible',
  zIndex: 1202
}))

export const NavGroup: FC<{
  item: NavItemType
  lastItem: number
  lastItemId: string
  remItems: NavItemType[]
  selectedItems: string | undefined
  selectedLevel: number
  setSelectedItems: Dispatch<SetStateAction<string | undefined>>
  setSelectedLevel: Dispatch<SetStateAction<number>>
}> = ({
  item,
  lastItem,
  lastItemId,
  remItems,
  selectedItems,
  selectedLevel,
  setSelectedItems,
  setSelectedLevel
}) => {
  const theme = useTheme()
  const { pathname } = useLocation()

  const { menuOrientation } = useContext(ResourceContext)
  const { drawerOpen, selectedID } = menuStore.use()

  const downLG = useMediaQuery(theme.breakpoints.down('lg'))

  const [anchorEl, setAnchorEl] = useState<
    VirtualElement | (() => VirtualElement) | null
  >(null)
  const [currentItem, setCurrentItem] = useState(item)

  const openMini = Boolean(anchorEl)

  useEffect(() => {
    if (lastItem) {
      if (item.id === lastItemId) {
        const localItem: NavItemType = { ...item }
        const elements = remItems.map((ele: NavItemType) => ele.elements)
        localItem.children = elements.flat(1).map(ele => ele ?? {})
        setCurrentItem(localItem)
      } else {
        setCurrentItem(item)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [item, lastItem, downLG])

  const checkOpenForParent = (child: NavItemType[], id: string) => {
    for (const ele of child) {
      if ((ele.children?.length ?? 0) > 0)
        checkOpenForParent(ele.children ?? [], currentItem.id ?? '')
      if (ele.url === pathname) menuStore.activeID(id)
    }
  }

  const checkSelectedOnload = (data: NavItemType) => {
    const childrens = data.children ?? []

    for (const itemCheck of childrens) {
      if ((itemCheck.children?.length ?? 0) > 0) {
        checkOpenForParent(itemCheck.children ?? [], currentItem.id ?? '')
      }
      if (itemCheck.url === pathname) menuStore.activeID(currentItem.id ?? '')
    }
  }

  useEffect(() => {
    checkSelectedOnload(currentItem)
    if (openMini) setAnchorEl(null)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, currentItem])

  const handleClick = (
    event:
      | React.MouseEvent<HTMLAnchorElement>
      | React.MouseEvent<HTMLDivElement>
      | undefined
  ) => {
    if (!openMini) setAnchorEl(event?.currentTarget as VirtualElement)
  }

  const handleClose = () => setAnchorEl(null)

  const Icon = currentItem.icon ?? ''
  const itemIcon =
    currentItem.icon == null ? null : (
      <Icon
        style={{
          color:
            (selectedID ?? '') === (currentItem.id ?? '')
              ? theme.palette.primary.main
              : theme.palette.secondary.dark,
          fontSize: 20,
          stroke: '1.5'
        }}
      />
    )

  const navCollapse = item.children?.map(menuItem => {
    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check
    switch (menuItem.type) {
      case 'collapse': {
        return (
          <NavCollapse
            key={menuItem.id}
            level={1}
            menu={menuItem}
            parentId={currentItem.id ?? ''}
            selectedItems={selectedItems}
            selectedLevel={selectedLevel}
            setSelectedItems={setSelectedItems}
            setSelectedLevel={setSelectedLevel}
          />
        )
      }
      case 'item': {
        return <NavItem item={menuItem} key={menuItem.id} level={1} />
      }
      default: {
        return (
          <Typography
            align='center'
            color='error'
            key={menuItem.id}
            variant='h6'
          >
            Fix - Group Collapse or Items
          </Typography>
        )
      }
    }
  })

  const moreItems = remItems.map((itemRem: NavItemType, i) => (
    <Fragment key={i}>
      {itemRem.title !== undefined && (
        <Typography sx={{ pl: 2 }} variant='caption'>
          {itemRem.title}
        </Typography>
      )}
      {itemRem.elements?.map(menu => {
        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check
        switch (menu.type) {
          case 'collapse': {
            return (
              <NavCollapse
                key={menu.id}
                level={1}
                menu={menu}
                parentId={currentItem.id ?? ''}
                selectedItems={selectedItems}
                selectedLevel={selectedLevel}
                setSelectedItems={setSelectedItems}
                setSelectedLevel={setSelectedLevel}
              />
            )
          }
          case 'item': {
            return <NavItem item={menu} key={menu.id} level={1} />
          }
          default: {
            return (
              <Typography
                align='center'
                color='error'
                key={menu.id}
                variant='h6'
              >
                Menu Items Error
              </Typography>
            )
          }
        }
      })}
    </Fragment>
  ))

  const child = currentItem.children ? currentItem.children[0] : null

  const items = child?.children?.map(menu => {
    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check
    switch (menu.type) {
      case 'collapse': {
        return (
          <NavCollapse
            key={menu.id}
            level={1}
            menu={menu}
            parentId={currentItem.id ?? ''}
            selectedItems={selectedItems}
            selectedLevel={selectedLevel}
            setSelectedItems={setSelectedItems}
            setSelectedLevel={setSelectedLevel}
          />
        )
      }
      case 'item': {
        return <NavItem item={menu} key={menu.id} level={1} />
      }
      default: {
        return (
          <Typography align='center' color='error' key={menu.id} variant='h6'>
            Menu Items Error
          </Typography>
        )
      }
    }
  })

  const popperId = openMini ? `group-pop-${item.id ?? ''}` : undefined

  return menuOrientation === MenuOrientation.VERTICAL || downLG ? (
    <List
      subheader={
        item.title !== undefined &&
        drawerOpen && (
          <Box sx={{ mb: 1.5, pl: 3 }}>
            <Typography
              color={
                theme.palette.mode === ThemeMode.DARK
                  ? 'textSecondary'
                  : 'text.secondary'
              }
              sx={{ display: 'none' }}
              variant='subtitle2'
            >
              {item.title}
            </Typography>
            {item.caption !== undefined && (
              <Typography color='secondary' variant='caption'>
                {item.caption}
              </Typography>
            )}
          </Box>
        )
      }
      sx={{
        mt: drawerOpen && item.title !== undefined ? 1.5 : 0,
        py: 0,
        zIndex: 0
      }}
    >
      {navCollapse}
    </List>
  ) : (
    <List>
      <ListItemButton
        aria-describedby={popperId}
        onClick={handleClick}
        onMouseEnter={handleClick}
        onMouseLeave={handleClose}
        selected={(selectedID ?? '') === (currentItem.id ?? '')}
        sx={{
          '&.Mui-selected': {
            bgcolor: 'primary.lighter'
          },
          '&:hover': {
            bgcolor: 'primary.lighter'
          },
          alignItems: 'center',
          backgroundColor: 'inherit',
          display: 'flex',
          mr: 1,
          my: 0.5,
          p: 1
        }}
      >
        {itemIcon && (
          <ListItemIcon sx={{ minWidth: 28 }}>
            {currentItem.id === lastItemId ? (
              <DownOutlined style={{ fontSize: 20, stroke: '1.5' }} />
            ) : (
              itemIcon
            )}
          </ListItemIcon>
        )}
        <ListItemText
          primary={
            <Typography
              color={
                (selectedID ?? '') === (currentItem.id ?? '')
                  ? theme.palette.primary.main
                  : theme.palette.secondary.dark
              }
              sx={{ whiteSpace: 'nowrap' }}
              variant='body1'
            >
              {currentItem.id === lastItemId ? (
                <FormattedMessage id='More Items' />
              ) : (
                currentItem.title
              )}
            </Typography>
          }
          sx={{ mr: 1 }}
        />
        <DownOutlined style={{ fontSize: 16, stroke: '1.5' }} />
        {anchorEl && (
          <PopperStyled
            anchorEl={anchorEl}
            id={popperId}
            open={openMini}
            placement='bottom-start'
            style={{
              zIndex: 2001
            }}
          >
            {({ TransitionProps }) => (
              <Transitions in={openMini} {...TransitionProps}>
                <Paper
                  sx={{
                    backgroundImage: 'none',
                    boxShadow: theme.shadows[8],
                    mt: 0.5,
                    py: 1.25
                  }}
                >
                  <ClickAwayListener onClickAway={handleClose}>
                    <SimpleBarScroll
                      sx={{
                        maxHeight: 'calc(100vh - 170px)',
                        overflowX: 'hidden',
                        overflowY: 'auto'
                      }}
                    >
                      {currentItem.id === lastItemId ? moreItems : items}
                    </SimpleBarScroll>
                  </ClickAwayListener>
                </Paper>
              </Transitions>
            )}
          </PopperStyled>
        )}
      </ListItemButton>
    </List>
  )
}
