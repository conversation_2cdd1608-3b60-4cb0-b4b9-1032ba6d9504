/* eslint-disable sonarjs/use-type-alias */
import type { Portfolios } from '@d/admin/models/portfolio'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class PortfoliosRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/portfolios'

  readonly deletePortfolios = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getPortfolios = () =>
    this.api.client
      .get<Portfolios[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertPortfolios = (portfolPortfolios: Portfolios) =>
    this.api.client.post<{ message: string }>(this.route, portfolPortfolios)

  readonly updatePortfolios = (id: string, portfolPortfolios: Portfolios) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      portfolPortfolios
    )
}
