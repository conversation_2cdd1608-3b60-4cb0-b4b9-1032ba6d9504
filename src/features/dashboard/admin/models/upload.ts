import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const upload = type({
  file: 'string',
  size: 'number'
})

export const uploadMetadata = new Set([
  {
    header: 'Nombre del Fichero',
    key: 'file'
  },
  {
    header: 'Tamaño del Fichero',
    key: 'size'
  }
])

export type Upload = typeof upload.infer

export const uploadValidate = (attempt?: Upload) =>
  upload(attempt) instanceof type.errors ? resetObject(attempt) : attempt
