import { LogoIndex } from '@c/components/LogoIndex'
import { Box, useMediaQuery } from '@mui/material'
import { styled, type Theme, useTheme } from '@mui/material/styles'
import type { FC } from 'react'

import { ResourceContext } from '@/common/contexts/ResourceContext'
import { MenuOrientation } from '~/types/resources'

const DrawerHeaderStyled = styled(Box, {
  shouldForwardProp: prop => prop !== 'open'
})(({ open, theme }: { open: boolean; theme: Theme }) => ({
  ...theme.mixins.toolbar,
  alignItems: 'center',
  display: 'flex',
  justifyContent: open ? 'flex-start' : 'center',
  paddingLeft: theme.spacing(open ? 3 : 0)
}))

export const DrawerHeader: FC<{ open: boolean }> = ({ open }) => {
  const theme = useTheme()
  const downLG = useMediaQuery(theme.breakpoints.down('lg'))

  const { menuOrientation } = useContext(ResourceContext)

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG

  return (
    <DrawerHeaderStyled
      open={open}
      sx={{
        minHeight: isHorizontal ? 'unset' : '60px',
        paddingBottom: isHorizontal ? { lg: '0', xs: '18px' } : '8px',
        // eslint-disable-next-line unicorn/no-nested-ternary, sonarjs/no-nested-conditional
        paddingLeft: isHorizontal ? { lg: '0', xs: '24px' } : open ? '24px' : 0,
        paddingTop: isHorizontal ? { lg: '0', xs: '10px' } : '8px',
        width: isHorizontal ? { lg: '424px', xs: '100%' } : 'inherit'
      }}
      theme={theme}
    >
      <LogoIndex
        isIcon={!open}
        sx={{ height: 35, width: open ? 'auto' : 35 }}
      />
    </DrawerHeaderStyled>
  )
}
