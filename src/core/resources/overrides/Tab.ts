import type { Theme } from '@mui/material/styles'

export const Tab = (theme: Theme) => ({
  MuiTab: {
    styleOverrides: {
      root: {
        '&:focus-visible': {
          borderRadius: 4,
          outline: `2px solid ${theme.palette.secondary.dark}`,
          outlineOffset: -3
        },
        '&:hover': {
          backgroundColor: theme.palette.primary.lighter + (60).toString(),
          color: theme.palette.primary.main
        },
        borderRadius: 4,
        color: theme.palette.text.primary,
        minHeight: 46
      }
    }
  }
})
