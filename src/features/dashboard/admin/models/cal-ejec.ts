import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const calcEjec = type({
  fecha: 'string',
  peso: 'number'
})

export const calcEjecMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Log Text',
    key: 'peso'
  }
])

export const calcEjecDefault: CalcEjec = {
  fecha: '',
  peso: 0
}

export type CalcEjec = typeof calcEjec.infer

export const calcEjecValidate = (attempt?: CalcEjec) =>
  calcEjec(attempt) instanceof type.errors ? resetObject(attempt) : attempt
