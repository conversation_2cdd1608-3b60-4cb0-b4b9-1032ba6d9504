import { upload, uploadMetadata } from '@d/admin/models/upload'
import { UploadsRepository } from '@d/admin/repositories/uploads-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Box, Button, Grid, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FC, ReactNode } from 'react'

import { container } from '~/modules/di-module'

import { DeleteDialog } from './delete-dialog'

export const UploadControl: FC<{
  children: ReactNode
  uploadTitle: string
}> = ({ children, uploadTitle }) => {
  const [dialogDelete, setDialogDelete] = useState(false)
  const [loadingDialog, setLoadingDialog] = useState(false)

  const { deleteFile, getFiles, uploadFile, uploadMultipleFile } =
    container.get(UploadsRepository)

  const fileRef = useRef(document.createElement('input'))
  const indexRef = useRef(-1)

  const {
    data: uploadsData,
    isFetching: isUploadsPending,
    refetch: uploadsRefetch
  } = useQuery({
    queryFn: getFiles,
    queryKey: ['uploads']
  })

  const showDelete = (index: number) => {
    indexRef.current = index
    setDialogDelete(true)
  }

  const handleDelete = async (isOk: boolean) => {
    setDialogDelete(false)
    const filename = uploadsData?.[indexRef.current]?.file
    if (isOk && filename !== undefined) {
      setLoadingDialog(true)
      await deleteFile(filename)
      setLoadingDialog(false)
      void uploadsRefetch()
    }
  }

  const uploadFiles = () => {
    fileRef.current.click()
    fileRef.current.addEventListener('input', async event => {
      const { files } = event.target as HTMLInputElement
      if (files && files.length === 1) {
        await uploadFile(files)
        await uploadsRefetch()
      } else if (files && files.length > 1) {
        await uploadMultipleFile(files)
        await uploadsRefetch()
      }
    })
  }

  return (
    <>
      {!isUploadsPending && <Reload onClick={() => uploadsRefetch()} />}
      <input
        id='uploadDivisa'
        multiple
        ref={fileRef}
        style={{ display: 'none' }}
        type='file'
      />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          mb: 3,
          width: '100%'
        }}
      >
        <Button
          onClick={uploadFiles}
          sx={{
            background: '#E5F1EE',
            color: 'green',
            marginRight: '5px'
          }}
          variant='contained'
        >
          {uploadTitle}
        </Button>
        {children}
      </Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          {isUploadsPending && <Loading />}
          {!isUploadsPending &&
            uploadsData !== undefined &&
            uploadsData.length === 0 && (
              <Box
                sx={{
                  alignItems: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  height: '30px',
                  justifyContent: 'center',
                  width: '100%'
                }}
              >
                <Typography
                  sx={{ fontStyle: 'italic', mt: 1 }}
                  variant='subtitle1'
                >
                  No hay archivos para procesar, por favor suba.
                </Typography>
              </Box>
            )}
          {!isUploadsPending && (uploadsData ?? []).length > 0 && (
            <GenericTable
              data={uploadsData ?? []}
              entries={upload}
              isDeleteAction
              metadata={uploadMetadata}
              onDeleteClick={showDelete}
            />
          )}
        </Grid>
        <DeleteDialog onClick={handleDelete} open={dialogDelete} />
      </Grid>
      <LoadingDialog open={loadingDialog} />
    </>
  )
}
