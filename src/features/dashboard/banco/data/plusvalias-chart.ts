import type { ApexOptions } from 'apexcharts'

import { numberFormat } from '~/utils/format-number'

export const plusvaliasChartOpt: ApexOptions = {
  chart: { id: 'apex-plusvalia' },
  colors: ['#007A5E'],
  dataLabels: {
    enabled: false
  },
  plotOptions: {
    bar: {
      borderRadius: 4,
      horizontal: true
    }
  },
  xaxis: {
    categories: [''],
    labels: {
      formatter: value => `${numberFormat(Number(value) / 1000000, 2)}M`,
      rotate: -45,
      style: { fontSize: '12px' }
    }
  },
  yaxis: {
    labels: { maxWidth: 260, minWidth: 100 }
  }
}
