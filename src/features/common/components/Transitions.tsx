import {
  Box,
  Collapse,
  Fade,
  Grow,
  Slide,
  Zoom,
  type ZoomProps
} from '@mui/material'
import { type CSSProperties, forwardRef, type ReactNode, type Ref } from 'react'

export const Transitions = forwardRef<
  HTMLDivElement,
  {
    children: ReactNode
    direction?: 'down' | 'left' | 'right' | 'up'
    in?: boolean
    position?: string
    sx?: CSSProperties
    type?: string
  }
>(
  (
    {
      children,
      direction = 'up',
      position = 'top-left',
      type = 'grow',
      ...others
    },
    ref
  ) => {
    const positionSX = { transformOrigin: '0 0 0' }

    switch (position) {
      case 'bottom': {
        positionSX.transformOrigin = 'bottom'
        break
      }
      case 'bottom-left': {
        positionSX.transformOrigin = 'bottom left'
        break
      }
      case 'bottom-right': {
        positionSX.transformOrigin = 'bottom right'
        break
      }
      case 'top': {
        positionSX.transformOrigin = 'top'
        break
      }
      case 'top-right': {
        positionSX.transformOrigin = 'top right'
        break
      }
      // eslint-disable-next-line unicorn/no-useless-switch-case
      case 'top-left':
      default: {
        positionSX.transformOrigin = '0 0 0'
        break
      }
    }

    return (
      <Box ref={ref}>
        {type === 'grow' && (
          <Grow {...others} timeout={{ appear: 0, enter: 150, exit: 150 }}>
            <Box sx={positionSX}>{children}</Box>
          </Grow>
        )}
        {type === 'collapse' && (
          <Collapse {...others} sx={positionSX}>
            {children}
          </Collapse>
        )}
        {type === 'fade' && (
          <Fade {...others} timeout={{ appear: 0, enter: 300, exit: 150 }}>
            <Box sx={positionSX}>{children}</Box>
          </Fade>
        )}
        {type === 'slide' && (
          <Slide
            {...others}
            direction={direction}
            timeout={{ appear: 0, enter: 150, exit: 150 }}
          >
            <Box sx={positionSX}>{children}</Box>
          </Slide>
        )}
        {type === 'zoom' && (
          <Zoom {...others}>
            <Box sx={positionSX}>{children}</Box>
          </Zoom>
        )}
      </Box>
    )
  }
)

export const PopupTransition = forwardRef(
  (props: ZoomProps, ref: Ref<unknown>) => (
    <Zoom ref={ref} timeout={200} {...props} />
  )
)
