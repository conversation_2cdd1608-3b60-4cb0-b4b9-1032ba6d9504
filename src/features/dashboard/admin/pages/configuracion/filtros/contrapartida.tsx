import { SearchOutlined } from '@ant-design/icons'
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type FiltroContrapartidas,
  filtroContrapartidas,
  filtroContrapartidasDefault,
  filtroContrapartidasMetadata
} from '@d/admin/models/filtro-contrapartida'
import { FiltroContrapartidaRepository } from '@d/admin/repositories/filtro-contrapartida-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, nth, prop } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteFiltroContrapartida,
    getFiltroContrapartidas,
    insertFiltroContrapartidas,
    updateFiltroContrapartidas
  } = container.get(FiltroContrapartidaRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const { activoPasivo, areas } = configStore.use()
  const [filterText, setFilterText] = useState('')
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(filtroContrapartidasDefault)

  const {
    data: allFiltroContrapartidasData,
    isFetching: isGetFiltroContrapartidasPending,
    refetch: refetchFiltroContrapartidas
  } = useQuery({
    queryFn: getFiltroContrapartidas,
    queryKey: ['getFiltroContrapartidas']
  })

  const filterContrapartidas = useMemo(
    () =>
      filterText.trim() === ''
        ? (allFiltroContrapartidasData ?? [])
        : (allFiltroContrapartidasData ?? []).filter(item =>
            (prop('tradeGroup', item) ?? '')
              .toLowerCase()
              .includes(filterText.toLowerCase())
          ),
    [filterText, allFiltroContrapartidasData]
  )

  const submitAction = async (
    incoming: FiltroContrapartidas,
    _: FormikHelpers<FiltroContrapartidas>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertFiltroContrapartidas(incoming)
      : updateFiltroContrapartidas(
          selectedData.current.id?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchFiltroContrapartidas()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteFiltroContrapartida(
        selectedData.current.id?.toString() ?? '0'
      )
      setLoadingDialog(false)
      void refetchFiltroContrapartidas()
    }
  }

  return isGetFiltroContrapartidasPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchFiltroContrapartidas()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = filtroContrapartidasDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ alignItems: 'center', display: 'flex' }} xs={12}>
            <Box sx={{ width: '100%' }}>
              <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
                <OutlinedInput
                  aria-describedby='header-search-text'
                  endAdornment={
                    <InputAdornment position='end' sx={{ mr: -0.5 }}>
                      <SearchOutlined />
                    </InputAdornment>
                  }
                  id='header-search'
                  inputProps={{ 'aria-label': 'weight' }}
                  onChange={e => setFilterText(e.target.value)}
                  placeholder='Buscar por Trade Group'
                />
              </FormControl>
            </Box>
          </Grid>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={filterContrapartidas}
                entries={filtroContrapartidas}
                isDeleteAction
                isEditAction
                metadata={filtroContrapartidasMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allFiltroContrapartidasData ?? []) ??
                    filtroContrapartidasDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<FiltroContrapartidas>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          codigo: Yup.string().max(20).required('El codigo es requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <TextFormControl<FiltroContrapartidas>
              field='codigoAdaptive'
              label='Codigo Adaptive: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.codigoAdaptive?.toString() ?? ''}
            />
            <TextFormControl<FiltroContrapartidas>
              field='tradeGroup'
              label='Codigo Group: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroup?.toString() ?? ''}
            />
            <TextFormControl<FiltroContrapartidas>
              field='product'
              label='Producto: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.product?.toString() ?? ''}
            />
            <SelectFormControl<FiltroContrapartidas>
              field='estado'
              label='Estado: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.estado.toString()}
            >
              {activoPasivo.map((act, ind) => (
                <MenuItem key={ind} value={ind}>
                  {act.estado}
                </MenuItem>
              ))}
            </SelectFormControl>
            <TextFormControl<FiltroContrapartidas>
              field='nombreContrapartida'
              label='Nombre Contrapartida: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.nombreContrapartida?.toString() ?? ''}
            />
            <TextFormControl<FiltroContrapartidas>
              field='comentarios'
              label='Comentario: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.comentarios?.toString() ?? ''}
            />
            <DateFormControl<FiltroContrapartidas>
              field='fecha'
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <SelectFormControl<FiltroContrapartidas>
              field='areaId'
              label='Area: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.areaId?.toString() ?? ''}
            >
              {areas.map((area, ind) => (
                <MenuItem key={ind} value={ind}>
                  {area.titulo}
                </MenuItem>
              ))}
            </SelectFormControl>
          </>
        )}
      </DialogForm>
    </>
  )
}
