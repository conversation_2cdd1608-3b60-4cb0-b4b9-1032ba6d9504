import type { ActivoPasivo } from '@d/common/models/activo-pasivo'
import { ActivoPasivoRepository } from '@d/common/repository/activo-pasivo-repository'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'
import { useEffect, useRef, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const margenController = () => {
  const { getActivo, getPasivo, showTradeGroupDivisa } = container.get(
    ActivoPasivoRepository
  )

  const day = calendarStore.actualDay.use()

  const balanceRef = useRef('Fuera de Balance DDA')

  const [openDialogActivo1, setOpenDialogActivo1] = useState(false)
  const [openDialogActivo2, setOpenDialogActivo2] = useState(false)
  const [openDialogPasivo1, setOpenDialogPasivo1] = useState(false)
  const [openDialogPasivo2, setOpenDialogPasivo2] = useState(false)

  const selectedActivoPasivo = useRef<{
    activoPasivo: boolean
    activoPasivoData: ActivoPasivo[]
    rowIndex: number
  } | null>(null)

  const selectedActivoPasivo2 = useRef<{
    balance: string
    product: string
    tradeGroup: string
  } | null>(null)

  const {
    data: activoData,
    error: activoError,
    isError: isActivoError,
    isFetching: isActivoPending,
    refetch: refetchActivo
  } = useQuery({
    queryFn: () => getActivo('13'),
    queryKey: ['getActivoMercadoDivisas']
  })

  const {
    data: pasivoData,
    error: pasivoError,
    isError: isPasivoError,
    isFetching: isPasivoPending,
    refetch: refetchPasivo
  } = useQuery({
    queryFn: () => getPasivo('13'),
    queryKey: ['getPasivoMercadoDivisas']
  })

  const {
    data: dialog1Data,
    error: dialog1Error,
    isError: isDialog1Error,
    isFetching: isDialog1Pending,
    refetch: refetchDialog1
  } = useQuery({
    enabled: !!selectedActivoPasivo.current,
    queryFn: async () => {
      if (selectedActivoPasivo.current === null) return []

      const { activoPasivo, activoPasivoData, rowIndex } =
        selectedActivoPasivo.current
      const activoPasivoRow = nth(rowIndex, activoPasivoData)
      const balance = activoPasivo
        ? 'Fuera de Balance DDA'
        : (activoPasivoRow?.balance ?? 'Fuera de Balance DDA')
      balanceRef.current = balance
      return showTradeGroupDivisa(
        balance,
        '13',
        activoPasivoRow?.epigrafeID?.toString() ?? ''
      ).then(data => {
        activoPasivo ? setOpenDialogActivo1(true) : setOpenDialogPasivo1(true)
        return data
      })
    },
    queryKey: ['showTradeGroupDivisa']
  })

  const onRowActivoPasivo1 = (
    activoPasivoData: ActivoPasivo[],
    rowIndex: number,
    activoPasivo: boolean
  ) => {
    selectedActivoPasivo.current = {
      activoPasivo,
      activoPasivoData,
      rowIndex
    }
    void refetchDialog1()
  }

  const onRowActivoPasivo2 = (rowIndex: number, activoPasivo: boolean) => {
    if (dialog1Data === undefined) return
    const activoPasivoRow = nth(rowIndex, dialog1Data)
    selectedActivoPasivo2.current = {
      balance: balanceRef.current,
      product: activoPasivoRow?.producto ?? '',
      tradeGroup: activoPasivoRow?.tradeGroup ?? ''
    }
    activoPasivo ? setOpenDialogActivo2(true) : setOpenDialogPasivo2(true)
  }

  const refetch = () => {
    selectedActivoPasivo.current = null
    selectedActivoPasivo2.current = null
    void refetchPasivo()
    void refetchActivo()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return {
    activoData,
    activoError,
    dialog1Data,
    dialog1Error,
    isActivoError,
    isActivoPending,
    isDialog1Error,
    isDialog1Pending,
    isPasivoError,
    isPasivoPending,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    pasivoError,
    refetch,
    selectedActivoPasivo2,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  }
}
