/* eslint-disable sonarjs/use-type-alias */
import type { Parametros } from '@d/admin/models/parametros'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class ParametrosRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/params'

  readonly deleteParametros = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getParametros = () =>
    this.api.client
      .get<Parametros[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertParametros = (paraParametros: Parametros) =>
    this.api.client.post<{ message: string }>(this.route, paraParametros)

  readonly updateParametros = (id: string, paraParametros: Parametros) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      paraParametros
    )
}
