import { WarningOutlined } from '@ant-design/icons'
import { Box, Paper, Typography } from '@mui/material'
import type { FC } from 'react'

export const ErrorMessage: FC<{
  fullHeight?: boolean
  message?: string
}> = ({ fullHeight = false, message = '' }) => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      width: '100%'
    }}
  >
    <Paper
      sx={{
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'column',
        height: fullHeight ? '55vh' : null,
        justifyContent: 'center',
        maxWidth: '35vh',
        p: 2
      }}
    >
      <WarningOutlined style={{ fontSize: '150%' }} />
      <Typography sx={{ mt: 1, textAlign: 'center' }} variant='subtitle1'>
        ERROR: inténtelo más tarde o consulte <br /> a su técnico sobre este
        mensaje...
      </Typography>
      <Typography
        sx={{
          fontStyle: 'italic',
          mt: 1,
          textAlign: 'center',
          whiteSpace: 'pre-line'
        }}
        variant='subtitle1'
      >
        {message.length > 30 ? `${message.slice(0, 30)}...` : message}
      </Typography>
    </Paper>
  </Box>
)
