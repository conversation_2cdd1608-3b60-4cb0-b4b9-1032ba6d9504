import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { CoberturasRepository } from '@d/common/repository/coberturas-repository'
import { Coberturas } from '@d/common/templates/coberturas/coberturas'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getBonos, getDerivados } = container.get(CoberturasRepository)

  const bonoSelected = useRef<{
    areaId: string
    book: string
    isin: string
  } | null>(null)

  const {
    data: bonosData,
    error: bonosError,
    isError: isBonosError,
    isFetching: isBonosPending,
    refetch: refetchBonos
  } = useQuery({
    queryFn: () => getBonos('4'),
    queryKey: ['getBonos']
  })

  const {
    data: derivadosData,
    error: derivadosError,
    isError: isDerivadosError,
    isFetching: isDerivadosPending,
    refetch: refetchDerivados
  } = useQuery({
    enabled: !!bonoSelected.current,
    queryFn: () =>
      getDerivados(
        bonoSelected.current?.areaId ?? '',
        bonoSelected.current?.isin ?? '',
        bonoSelected.current?.book ?? ''
      ),
    queryKey: ['getDerivados']
  })

  const refetch = () => {
    bonoSelected.current = null
    void refetchDerivados()
    void refetchBonos()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Fetching
        errorMessage={bonosError?.message}
        fullHeight
        isError={isBonosError}
        isFetching={isBonosPending}
      >
        <Coberturas
          bonosData={bonosData ?? []}
          derivadosData={derivadosData ?? []}
          error={derivadosError?.message}
          isError={isDerivadosError}
          onBonosClick={(isin, book) => {
            bonoSelected.current = { areaId: '4', book, isin }
            void refetchDerivados()
          }}
        />
      </Fetching>
      <LoadingDialog open={isDerivadosPending} />
    </>
  )
}
