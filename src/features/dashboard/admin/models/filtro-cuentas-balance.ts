/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const filtroCuentasBalance = type({
  accountingAreaID: 'string?',
  ajusteIntDia: 'number?',
  ajusteMargen: 'number?',
  ajusteSaldo: 'number?',
  balance: 'string?',
  costeDia: 'number?',
  currencyID: 'string?',
  epigrafeID: 'number?',
  fecha: 'string?',
  intDia: 'number?',
  margenDia: 'number?',
  mfgID: 'number?',
  nombre: 'string?',
  nombreInformeGlobal: 'string?',
  peso: 'number?',
  producto: 'string?',
  rentabilidadDia: 'number?',
  saldo: 'number?',
  tradeGroup: 'string?'
})

export type FiltroCuentasBalance = typeof filtroCuentasBalance.infer

export const filtroCuentasBalanceMetadata = new Set([
  {
    header: 'Balance',
    key: 'balance'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Ajuste Saldo',
    key: 'ajuste<PERSON><PERSON>'
  },
  {
    header: 'Int Día',
    key: 'intDia'
  },
  {
    header: 'Ajuste Int Día',
    key: 'ajusteIntDia'
  },
  {
    header: 'Coste Día',
    key: 'costeDia'
  },
  {
    header: 'Margen Día',
    key: 'margenDia'
  },
  {
    header: 'Ajuste Margen',
    key: 'ajusteMargen'
  }
])
