import { Box, FormControlLabel, Grid } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import type { Dayjs } from 'dayjs'

type Props<T extends Record<string, unknown>> = {
  disabled?: boolean
  field: keyof T
  label?: string
  required?: boolean
  setFieldValue: (field: string, value: Dayjs | null) => void
  value: Dayjs
}

export const DateFormControl = <T extends Record<string, unknown>>({
  disabled = false,
  field,
  label = 'Fecha: ',
  required,
  setFieldValue,
  value
}: Props<T>) => (
  <Grid item sx={{ marginLeft: '-16px' }} xs={12}>
    <FormControlLabel
      control={
        <Box sx={{ flexShrink: 0 }}>
          <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
            <DatePicker
              className='fecha'
              disabled={disabled}
              onChange={e => setFieldValue(field as string, e)}
              slotProps={{
                textField: { size: 'small', sx: { width: '200px' } }
              }}
              value={value}
            />
          </LocalizationProvider>
        </Box>
      }
      label={label}
      labelPlacement='start'
      required={required}
      sx={{ display: 'flex', justifyContent: 'space-between' }}
    />
  </Grid>
)
