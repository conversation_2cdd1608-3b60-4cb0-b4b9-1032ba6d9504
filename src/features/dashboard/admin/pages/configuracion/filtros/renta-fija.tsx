import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type FiltroRentaFija,
  filtroRentaFija,
  filtroRentaFijaDefault,
  filtroRentaFijaMetadata
} from '@d/admin/models/filtro-rentafija'
import { FiltroRentaFijaRepository } from '@d/admin/repositories/filtro-rentafija-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, MenuItem, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteFiltroRentaFija,
    getFiltroRentaFija,
    insertFiltroRentaFija,
    updateFiltroRentaFija
  } = container.get(FiltroRentaFijaRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const { areas } = configStore.use()
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(filtroRentaFijaDefault)

  const {
    data: allFiltroRentaFijaData,
    isFetching: isGetFiltroRentaFijaPending,
    refetch: refetchFiltroRentaFija
  } = useQuery({
    queryFn: getFiltroRentaFija,
    queryKey: ['getFiltroRentaFija']
  })

  const submitAction = async (
    incoming: FiltroRentaFija,
    _: FormikHelpers<FiltroRentaFija>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertFiltroRentaFija(incoming)
      : updateFiltroRentaFija(
          selectedData.current.filtroRFID?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchFiltroRentaFija()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteFiltroRentaFija(
        selectedData.current.filtroRFID?.toString() ?? '0'
      )
      setLoadingDialog(false)
      void refetchFiltroRentaFija()
    }
  }

  return isGetFiltroRentaFijaPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchFiltroRentaFija()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = filtroRentaFijaDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={allFiltroRentaFijaData ?? []}
                entries={filtroRentaFija}
                isDeleteAction
                isEditAction
                metadata={filtroRentaFijaMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allFiltroRentaFijaData ?? []) ??
                    filtroRentaFijaDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<FiltroRentaFija>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          codigo: Yup.string().max(20).required('El codigo es requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <TextFormControl<FiltroRentaFija>
              field='issueID'
              label='Issue Id: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.issueID?.toString() ?? ''}
            />
            <TextFormControl<FiltroRentaFija>
              field='counterpartyID'
              label='Counterparty: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroup?.toString() ?? ''}
            />
            <TextFormControl<FiltroRentaFija>
              field='issueType'
              label='Issue Type: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroup?.toString() ?? ''}
            />
            <TextFormControl<FiltroRentaFija>
              field='tradeGroup'
              label='Trade Group: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroup?.toString() ?? ''}
            />
            <TextFormControl<FiltroRentaFija>
              field='product'
              label='Producto: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.tradeGroup?.toString() ?? ''}
            />
            <TextFormControl<FiltroRentaFija>
              field='comentarios'
              label='Comentarios: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.comentarios?.toString() ?? ''}
            />
            <DateFormControl<FiltroRentaFija>
              field='fecha'
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <SelectFormControl<FiltroRentaFija>
              field='areaID'
              label='Area: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.filtroEstadoID?.toString() ?? ''}
            >
              {areas.map((area, ind) => (
                <MenuItem key={ind} value={ind}>
                  {area.titulo}
                </MenuItem>
              ))}
            </SelectFormControl>
          </>
        )}
      </DialogForm>
    </>
  )
}
