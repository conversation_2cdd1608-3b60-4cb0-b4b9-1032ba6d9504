import type { Theme } from '@mui/material/styles'

export const getShadow = (theme: Theme, shadow: string) => {
  switch (shadow) {
    case 'error': {
      return theme.customShadows.error
    }
    case 'errorButton': {
      return theme.customShadows.errorButton
    }
    case 'info': {
      return theme.customShadows.info
    }
    case 'infoButton': {
      return theme.customShadows.infoButton
    }
    case 'primaryButton': {
      return theme.customShadows.primaryButton
    }
    case 'secondary': {
      return theme.customShadows.secondary
    }
    case 'secondaryButton': {
      return theme.customShadows.secondaryButton
    }
    case 'success': {
      return theme.customShadows.success
    }
    case 'successButton': {
      return theme.customShadows.successButton
    }
    case 'warning': {
      return theme.customShadows.warning
    }
    case 'warningButton': {
      return theme.customShadows.warningButton
    }
    default: {
      return theme.customShadows.primary
    }
  }
}
