import {
  Card,
  CardContent,
  type CardContentProps,
  CardHeader,
  type CardHeaderProps,
  type CardProps,
  Divider,
  Typography,
  useTheme
} from '@mui/material'
import { type CSSProperties, forwardRef, type ReactNode } from 'react'

import { ThemeMode } from '~/types/resources'

import { Highlighter } from './Highlighter'

const headerSX = {
  '& .MuiCardHeader-action': { alignSelf: 'center', m: '0px auto' },
  p: 2.5
}

export const MainCard = forwardRef<
  HTMLDivElement,
  {
    border?: boolean
    boxShadow?: boolean
    children: ReactNode
    codeHighlight?: boolean
    codeString?: string
    content?: boolean
    contentSX?: CardContentProps['sx']
    darkTitle?: boolean
    divider?: boolean
    elevation?: number
    modal?: boolean
    secondary?: CardHeaderProps['action']
    shadow?: string
    style?: CSSProperties
    subheader?: ReactNode
    sx?: CardProps['sx']
    title?: ReactNode
  }
>(
  (
    {
      border = true,
      boxShadow = true,
      children,
      codeHighlight = false,
      codeString,
      content = true,
      contentSX = {},
      darkTitle = true,
      divider = true,
      elevation,
      modal = false,
      secondary,
      shadow,
      subheader,
      sx = {},
      title,
      ...others
    },
    ref
  ) => {
    const theme = useTheme()

    return (
      <Card
        elevation={elevation ?? 0}
        ref={ref}
        {...others}
        sx={{
          ':hover': {
            boxShadow: boxShadow
              ? (shadow ?? theme.customShadows.z1)
              : 'inherit'
          },
          border: border ? '1px solid' : 'none',
          borderColor:
            theme.palette.mode === ThemeMode.DARK
              ? theme.palette.divider
              : theme.palette.grey.A800,
          borderRadius: 1,
          boxShadow:
            boxShadow && (!border || theme.palette.mode === ThemeMode.DARK)
              ? (shadow ?? theme.customShadows.z1)
              : 'inherit',
          position: 'relative',
          ...(codeHighlight && {
            '& pre': {
              fontFamily: theme.typography.fontFamily,
              fontSize: '0.75rem',
              m: 0,
              p: '12px !important'
            }
          }),
          ...(modal && {
            '& .MuiCardContent-root': {
              maxHeight: `calc(100vh - 200px)`,
              minHeight: 'auto',
              overflowY: 'auto'
            },
            left: '50%',
            position: 'absolute' as const,
            top: '50%',
            transform: 'translate(-50%, -50%)',
            width: { sm: 'auto', xs: `calc( 100% - 50px)` }
          }),
          // eslint-disable-next-line @typescript-eslint/no-misused-spread
          ...sx
        }}
      >
        {!darkTitle && title !== undefined && (
          <CardHeader
            action={secondary}
            subheader={subheader}
            sx={headerSX}
            title={title}
          />
        )}
        {darkTitle && title !== undefined && (
          <CardHeader
            action={secondary}
            sx={headerSX}
            title={<Typography variant='h4'>{title}</Typography>}
          />
        )}

        {title !== undefined && divider && <Divider />}

        {content && <CardContent sx={contentSX}>{children}</CardContent>}
        {!content && children}

        {codeString != null && (
          <>
            <Divider sx={{ borderStyle: 'dashed' }} />
            <Highlighter
              codeHighlight={codeHighlight}
              codeString={codeString}
            />
          </>
        )}
      </Card>
    )
  }
)
