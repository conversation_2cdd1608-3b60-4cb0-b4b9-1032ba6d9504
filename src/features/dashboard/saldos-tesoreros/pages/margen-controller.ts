import type { ActivoPasivo } from '@d/common/models/activo-pasivo'
import type { DesgloseProductoActivoPasivo } from '@d/common/models/desglose-producto-activo-pasivo'
import { ActivoPasivoRepository } from '@d/common/repository/activo-pasivo-repository'
import { useQuery } from '@tanstack/react-query'
import { nth } from 'rambdax'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const margenController = () => {
  const {
    desgloseProductoActivo,
    desgloseProductoPasivo,
    getActivo,
    getPasivo,
    showTradeGroup
  } = container.get(ActivoPasivoRepository)

  const day = calendarStore.actualDay.use()

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDialogActivo1, setOpenDialogActivo1] = useState(false)
  const [openDialogActivo2, setOpenDialogActivo2] = useState(false)
  const [openDialogPasivo1, setOpenDialogPasivo1] = useState(false)
  const [openDialogPasivo2, setOpenDialogPasivo2] = useState(false)

  const [dialog1Data, setDialog1Data] = useState<ActivoPasivo[]>([])
  const [dialog2Data, setDialog2Data] = useState<
    DesgloseProductoActivoPasivo[]
  >([])

  const [balance, setBalance] = useState('Activo')

  const {
    data: activoData,
    isFetching: isActivoPending,
    refetch: refetchActivo
  } = useQuery({
    queryFn: () => getActivo('15'),
    queryKey: ['getActivoBANSaldTes']
  })

  const {
    data: pasivoData,
    isFetching: isPasivoPending,
    refetch: refetchPasivo
  } = useQuery({
    queryFn: () => getPasivo('15'),
    queryKey: ['getPasivoBANSaldTes']
  })

  const onRowActivoPasivo1 = async (
    activoPasivoData: ActivoPasivo[],
    rowIndex: number,
    activoPasivo: boolean
  ) => {
    const activoPasivoRow = nth(rowIndex, activoPasivoData)

    const balance = activoPasivo
      ? 'Activo'
      : (activoPasivoRow?.balance ?? 'Pasivo')
    setBalance(balance)
    setLoadingDialog(true)
    setDialog1Data(
      await showTradeGroup(
        balance,
        '15',
        activoPasivoRow?.epigrafeID?.toString() ?? ''
      )
    )

    setLoadingDialog(false)
    activoPasivo ? setOpenDialogActivo1(true) : setOpenDialogPasivo1(true)
  }

  const onRowActivoPasivo2 = async (
    rowIndex: number,
    activoPasivo: boolean
  ) => {
    const activoPasivoRow = nth(rowIndex, dialog1Data)
    const producto = activoPasivoRow?.producto ?? ''
    const tradeGroup = activoPasivoRow?.tradeGroup ?? ''

    setLoadingDialog(true)

    setDialog2Data(
      activoPasivo
        ? (await desgloseProductoActivo(producto, balance, tradeGroup)).map(
            el => ({
              ...el,
              expiryDate: el.expiryDate.split('T')[0] ?? '',
              startDateAdj: el.startDateAdj.split('T')[0] ?? ''
            })
          )
        : (await desgloseProductoPasivo(producto, balance, tradeGroup)).map(
            el => ({
              ...el,
              expiryDate: el.expiryDate.split('T')[0] ?? '',
              startDateAdj: el.startDateAdj.split('T')[0] ?? ''
            })
          )
    )

    setLoadingDialog(false)
    activoPasivo ? setOpenDialogActivo2(true) : setOpenDialogPasivo2(true)
  }

  const refetch = useCallback(() => {
    void refetchPasivo()
    void refetchActivo()
  }, [])

  useEffect(() => {
    refetch()
  }, [day])

  return {
    activoData,
    dialog1Data,
    dialog2Data,
    isActivoPending,
    isPasivoPending,
    loadingDialog,
    onRowActivoPasivo1,
    onRowActivoPasivo2,
    openDialogActivo1,
    openDialogActivo2,
    openDialogPasivo1,
    openDialogPasivo2,
    pasivoData,
    refetch,
    setOpenDialogActivo1,
    setOpenDialogActivo2,
    setOpenDialogPasivo1,
    setOpenDialogPasivo2
  }
}
