/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const spotAdaptiv = type({
  divisa_Nombre: 'string',
  ordenPlazo: 'number',
  pnLCont: 'number',
  pnLPlazo: 'number',
  pnLTOTAL: 'number',
  posEurContado: 'number',
  posEurPlazo: 'number',
  posLocalContado: 'number',
  posLocalPlazo: 'number',
  posTOTALDiv: 'number',
  posTOTALeur: 'number'
})

export const spotAdaptivMetadata = new Set([
  {
    header: 'Divisa',
    key: 'divisa_Nombre'
  },
  {
    header: 'P&L Total €',
    key: 'pnLTOTAL'
  },
  {
    header: 'Posición TOTAL Div',
    key: 'posTOTALDiv'
  },
  {
    header: 'Posición TOTAL €',
    key: 'posTOTALeur'
  },
  {
    header: 'Posición Contado',
    key: 'posLocalContado'
  },
  {
    header: 'Posición Contado €',
    key: 'posEurContado'
  },
  {
    header: 'P&L Contado €',
    key: 'pnLCont'
  },
  {
    header: 'Posición Plazo',
    key: 'posLocalPlazo'
  },
  {
    header: 'Posición Plazo €',
    key: 'posEurPlazo'
  },
  {
    header: 'P&L Plazo €',
    key: 'pnLPlazo'
  }
])

export type SpotAdaptiv = typeof spotAdaptiv.infer

export const spotAdaptivValidate = (attempt?: SpotAdaptiv) =>
  spotAdaptiv(attempt) instanceof type.errors ? resetObject(attempt) : attempt
