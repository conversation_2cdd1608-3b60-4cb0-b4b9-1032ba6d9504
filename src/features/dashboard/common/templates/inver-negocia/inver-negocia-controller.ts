import { InverNegociaRepository } from '@d/common/repository/inver-negocia-repository'
import { useQuery } from '@tanstack/react-query'
import type { SyntheticEvent } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const preventDefault = (event: SyntheticEvent) => event.preventDefault()

export const inverNegociaController = (areaName: string, inver?: boolean) => {
  const { actualDay } = calendarStore.use()

  const [selectInverNegocia, setSelectInverNegocia] = useState(inver ?? true)

  const { getVarNewAreas, getVarNewAreasProductoFactor } = container.get(
    InverNegociaRepository
  )

  const {
    data: varHistorico,
    error: varHistoricoError,
    isError: isVarHistoricoError,
    isFetching: isVarHistoricoPending,
    refetch: refetchVarHistorico
  } = useQuery({
    queryFn: () =>
      getVarNewAreas(
        selectInverNegocia ? 'Inversion' : 'Negociacion',
        areaName
      ),
    queryKey: ['varNewAreas']
  })

  const {
    data: varProducto,
    error: varProductoError,
    isError: isVarProductoError,
    isFetching: isVarProductoPending,
    refetch: refetchVarProducto
  } = useQuery({
    queryFn: () =>
      getVarNewAreasProductoFactor(
        selectInverNegocia ? 'Inversion' : 'Negociacion',
        areaName,
        'Producto'
      ),
    queryKey: ['varNewAreasProducto']
  })

  const {
    data: varFactor,
    error: varFactorError,
    isError: isVarFactorError,
    isFetching: isVarFactorPending,
    refetch: refetchVarFactor
  } = useQuery({
    queryFn: () =>
      getVarNewAreasProductoFactor(
        selectInverNegocia ? 'Inversion' : 'Negociacion',
        areaName,
        'Factor'
      ),
    queryKey: ['varNewAreasFactor']
  })

  const refetch = () => {
    void refetchVarHistorico()
    void refetchVarProducto()
    void refetchVarFactor()
  }

  useEffect(() => {
    refetch()
  }, [actualDay])

  return {
    isVarFactorError,
    isVarFactorPending,
    isVarHistoricoError,
    isVarHistoricoPending,
    isVarProductoError,
    isVarProductoPending,
    refetch,
    selectInverNegocia,
    setSelectInverNegocia,
    varFactor,
    varFactorError,
    varHistorico,
    varHistoricoError,
    varProducto,
    varProductoError
  }
}
