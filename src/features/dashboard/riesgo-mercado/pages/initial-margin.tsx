import { DownOutlined } from '@ant-design/icons'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import { diario, diarioMetadata } from '@d/riesgo-mercado/models/diario'
import {
  historico,
  historicoMetadata
} from '@d/riesgo-mercado/models/historico'
import {
  incumplimientos,
  incumplimientosMetadata
} from '@d/riesgo-mercado/models/incumplimientos'
import { InitialMargenRepository } from '@d/riesgo-mercado/repositories/initial-margen-repository'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Container,
  FormControl,
  Grid,
  MenuItem,
  Paper,
  Select,
  type SelectChangeEvent
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

const bankData = [
  {
    bank: 'BSCH',
    name: 'Banco Santander'
  },
  {
    bank: 'BBVA',
    name: 'BBVA'
  },
  {
    bank: 'BNP',
    name: 'BNPARI-P'
  },
  {
    bank: 'BJPMORGAN-EU',
    name: 'Morgan Stanley'
  },
  {
    bank: 'CALACAIXA',
    name: 'Caixabank'
  },
  {
    bank: 'BCALYON-P',
    name: 'Credit Agricole SA'
  },
  {
    bank: 'BCITI-EU',
    name: 'Citigroup'
  },
  {
    bank: 'NOCUMPLE',
    name: 'NOCUMPLE'
  }
]

export default () => {
  const day = calendarStore.actualDay.use()

  const { getHistorico, getIncumplimientos, getPost, getReceived } =
    container.get(InitialMargenRepository)

  const [bankSelect, setBankSelect] = useState({
    bank: 'NOCUMPLE',
    name: 'NOCUMPLE'
  })

  const selectChange = (e: SelectChangeEvent) =>
    setBankSelect(
      bankData.find(bank => bank.bank === e.target.value) ?? bankSelect
    )

  const {
    data: getPostData,
    error: getPostError,
    isError: isGetPostError,
    isFetching: isgetPostPending,
    refetch: refetchGetPost
  } = useQuery({
    queryFn: getPost,
    queryKey: ['getPost']
  })

  const {
    data: getReceivedData,
    error: getReceivedError,
    isError: isGetReceivedError,
    isFetching: isgetReceivedPending,
    refetch: refetchGetReceived
  } = useQuery({
    queryFn: getReceived,
    queryKey: ['getReceived']
  })

  const {
    data: getHistoricoData,
    error: getHistoricoError,
    isError: isGetHistoricoError,
    isFetching: isgetHistoricoPending,
    refetch: refetchGetHistorico
  } = useQuery({
    queryFn: () => getHistorico(bankSelect.bank),
    queryKey: ['getHistorico']
  })

  const {
    data: getIncumplimientosData,
    error: getIncumplimientosError,
    isError: isGetIncumplimientosError,
    isFetching: isgetIncumplimientosPending,
    refetch: refetchGetIncumplimientos
  } = useQuery({
    queryFn: () => getIncumplimientos(bankSelect.bank),
    queryKey: ['getIncumplimientos']
  })

  const refetch = () => {
    void refetchGetPost()
    void refetchGetReceived()
    void refetchGetHistorico()
    void refetchGetIncumplimientos()
  }

  useEffect(() => {
    refetch()
  }, [day])

  useEffect(() => {
    void refetchGetHistorico()
    void refetchGetIncumplimientos()
  }, [bankSelect])

  return (
    <>
      <Reload onClick={refetch} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Accordion defaultExpanded>
              <AccordionSummary
                aria-controls='panel1-content'
                expandIcon={<DownOutlined />}
                id='panel1-header'
              >
                <h3>Diario</h3>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3>Entregado</h3>
                    </div>
                    <Fetching
                      errorMessage={getPostError?.message}
                      isError={isGetPostError}
                      isFetching={isgetPostPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={getPostData ?? []}
                          entries={diario}
                          metadata={diarioMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={12}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3>Recibido</h3>
                    </div>
                    <Fetching
                      errorMessage={getReceivedError?.message}
                      isError={isGetReceivedError}
                      isFetching={isgetReceivedPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={getReceivedData ?? []}
                          entries={diario}
                          metadata={diarioMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
            <div style={{ marginTop: '20px', width: '100%' }}></div>
            <Accordion>
              <AccordionSummary
                aria-controls='panel1-content'
                expandIcon={<DownOutlined />}
                id='panel1-header'
              >
                <h3>Back Testing</h3>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid
                    item
                    sx={{
                      display: 'flex',
                      justifyContent: 'start'
                    }}
                    xs={12}
                  >
                    <p style={{ marginRight: '20px' }}>Contrapartida: </p>
                    <FormControl
                      size='small'
                      sx={{ marginTop: '10px', minWidth: 120 }}
                    >
                      <Select
                        id='demo-simple-select'
                        onChange={selectChange}
                        value={bankSelect.bank}
                      >
                        {bankData.map((value, index) => (
                          <MenuItem key={index} value={value.bank}>
                            {value.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3>Incumplimientos</h3>
                    </div>
                    <Fetching
                      errorMessage={getIncumplimientosError?.message}
                      isError={isGetIncumplimientosError}
                      isFetching={isgetIncumplimientosPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={getIncumplimientosData ?? []}
                          entries={incumplimientos}
                          metadata={incumplimientosMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                  <Grid item xs={12}>
                    <div
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        width: '100%'
                      }}
                    >
                      <h3>Histórico</h3>
                    </div>
                    <Fetching
                      errorMessage={getHistoricoError?.message}
                      isError={isGetHistoricoError}
                      isFetching={isgetHistoricoPending}
                    >
                      <Paper elevation={10} sx={paperStyle}>
                        <GenericTable
                          data={getHistoricoData ?? []}
                          entries={historico}
                          metadata={historicoMetadata}
                        />
                      </Paper>
                    </Fetching>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
