import { SearchOutlined } from '@ant-design/icons'
import {
  type MargenFinancieroInforme,
  margenFinancieroInforme,
  margenFinancieroInformeMetadata
} from '@d/admin/models/margen-financiero-informe'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import {
  Box,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Select,
  type SelectChangeEvent,
  Typography
} from '@mui/material'
import { nth, piped, prop } from 'rambdax'
import type { ChangeEvent, FC } from 'react'

export const MargenFinancieroInformeTemplate: FC<{
  data: MargenFinancieroInforme[]
}> = ({ data }) => {
  const [menuId, setMenuId] = useState(0)
  const [filterText, setFilterText] = useState('')

  const handleTextChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => setFilterText(event.currentTarget.value)

  const handleChange = (event: SelectChangeEvent) =>
    setMenuId(Number(event.target.value))

  const filterMargenFinancieroInforme = useMemo(() => {
    if (!filterText.trim()) return data

    return data.filter(item => {
      const value = prop(
        piped(
          [...margenFinancieroInformeMetadata].map(el => el.key),
          keys => nth(menuId, keys) as keyof MargenFinancieroInforme
        ),
        item
      )

      if (typeof value === 'string') {
        return value.toLowerCase().includes(filterText.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(filterText)
      }
      return false
    })
  }, [menuId, filterText, data])

  return (
    <Grid container spacing={3}>
      <Grid
        item
        sx={{ alignItems: 'center', display: 'flex', ml: '20px' }}
        xs={8}
      >
        <Typography
          sx={{ marginRight: '10px', whiteSpace: 'nowrap' }}
          variant='subtitle1'
        >
          Buscar en:{' '}
        </Typography>
        <Box sx={{ minWidth: 200 }}>
          <FormControl fullWidth>
            <Select
              id='demo-simple-select'
              onChange={handleChange}
              value={menuId.toString()}
            >
              {[...margenFinancieroInformeMetadata].map((key, index) => (
                <MenuItem key={index} value={index}>
                  {key.header}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        <Box sx={{ ml: { md: 1, xs: 0 }, width: '100%' }}>
          <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
            <OutlinedInput
              aria-describedby='header-search-text'
              endAdornment={
                <InputAdornment position='end' sx={{ mr: -0.5 }}>
                  <SearchOutlined />
                </InputAdornment>
              }
              id='header-search'
              inputProps={{ 'aria-label': 'weight' }}
              onChange={handleTextChange}
              placeholder='Buscar'
            />
          </FormControl>
        </Box>
      </Grid>
      <Grid item xs={12}>
        <GenericTable
          data={filterMargenFinancieroInforme}
          entries={margenFinancieroInforme}
          metadata={margenFinancieroInformeMetadata}
        />
      </Grid>
    </Grid>
  )
}
