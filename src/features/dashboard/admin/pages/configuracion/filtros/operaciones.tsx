import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type FiltroOperaciones,
  filtroOperaciones,
  filtroOperacionesDefault,
  filtroOperacionesMetadata
} from '@d/admin/models/filtro-operaciones'
import { FiltroOperacionesRepository } from '@d/admin/repositories/filtro-operaciones-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, MenuItem, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteFiltroOperaciones,
    getFiltroOperaciones,
    insertFiltroOperaciones,
    updateFiltroOperaciones
  } = container.get(FiltroOperacionesRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const { activoPasivo } = configStore.use()
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(filtroOperacionesDefault)

  const {
    data: allFiltroOperacionesData,
    isFetching: isGetFiltroOperacionesPending,
    refetch: refetchFiltroOperaciones
  } = useQuery({
    queryFn: getFiltroOperaciones,
    queryKey: ['getFiltroOperaciones']
  })

  const submitAction = async (
    incoming: FiltroOperaciones,
    _: FormikHelpers<FiltroOperaciones>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertFiltroOperaciones(incoming)
      : updateFiltroOperaciones(
          selectedData.current.filtroOpId?.toString() ?? '0',
          incoming
        ))
    setLoadingDialog(false)
    void refetchFiltroOperaciones()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteFiltroOperaciones(
        selectedData.current.filtroOpId?.toString() ?? '0'
      )
      setLoadingDialog(false)
      void refetchFiltroOperaciones()
    }
  }

  return isGetFiltroOperacionesPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchFiltroOperaciones()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = filtroOperacionesDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                maxHeight: '200px',
                minWidth: '100%',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={allFiltroOperacionesData ?? []}
                entries={filtroOperaciones}
                isDeleteAction
                isEditAction
                metadata={filtroOperacionesMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allFiltroOperacionesData ?? []) ??
                    filtroOperacionesDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<FiltroOperaciones>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          codigo: Yup.string().max(20).required('El codigo es requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <TextFormControl<FiltroOperaciones>
              field='instrumentId'
              label='Instrument Id: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.instrumentId?.toString() ?? ''}
            />
            <SelectFormControl<FiltroOperaciones>
              field='filtroOpId'
              label='Estado: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.filtroOpId?.toString() ?? ''}
            >
              {activoPasivo.map((act, ind) => (
                <MenuItem key={ind} value={ind}>
                  {act.estado}
                </MenuItem>
              ))}
            </SelectFormControl>
            <TextFormControl<FiltroOperaciones>
              field='comentarios'
              label='Comentarios: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.comentarios?.toString() ?? ''}
            />
            <DateFormControl<FiltroOperaciones>
              field='fecha'
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
