/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const filtroRentaFija = type({
  areaID: 'number?',
  comentarios: 'string?',
  counterpartyID: 'string?',
  fecha: 'string?',
  filtroEstadoID: 'number?',
  filtroRFID: 'number?',
  issueID: 'string?',
  issueType: 'string?',
  product: 'string?',
  tradeGroup: 'string?'
})

export type FiltroRentaFija = typeof filtroRentaFija.infer

export const filtroRentaFijaDefault: FiltroRentaFija = {
  areaID: 0,
  comentarios: '',
  counterpartyID: '',
  fecha: '',
  filtroEstadoID: 0,
  filtroRFID: 0,
  issueID: '',
  issueType: '',
  product: '',
  tradeGroup: ''
}

export const filtroRentaFijaMetadata = new Set([
  {
    header: 'Filtro RFID',
    key: 'filtroRFID'
  },
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Issue Id',
    key: 'issueID'
  },
  {
    header: 'Counterparty Id',
    key: 'counterpartyID'
  },
  {
    header: 'Issue Type',
    key: 'issueType'
  },
  {
    header: 'Trade Group',
    key: 'tradeGroup'
  },
  {
    header: 'Producto',
    key: 'product'
  },
  {
    header: 'Comentarios',
    key: 'comentarios'
  },
  {
    header: 'Filtro Estado',
    key: 'filtroEstadoID'
  }
])
