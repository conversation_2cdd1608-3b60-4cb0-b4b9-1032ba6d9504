/* eslint-disable sonarjs/void-use */
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import Select, { type SelectChangeEvent } from '@mui/material/Select'
import { Outlet, useNavigate, useNavigation } from 'react-router'

const route = '/admin/input-manual/ajustes'

export default () => {
  const [menuId, setMenuId] = useState(0)

  const navigate = useNavigate()
  const { location } = useNavigation()

  const handleChange = (event: SelectChangeEvent) => {
    const val = Number(event.target.value)
    switch (val) {
      case 0: {
        if (val !== menuId) void navigate(`${route}/margen-financiero`)
        setMenuId(0)
        break
      }
      case 1: {
        if (val !== menuId) void navigate(`${route}/ajustes-aplicados`)
        setMenuId(1)
        break
      }
      case 2: {
        if (val !== menuId) void navigate(`${route}/renta-fija-sgt`)
        setMenuId(2)
        break
      }
    }
  }

  useEffect(() => {
    const path = location?.pathname ?? ''
    if (path.includes('margen-financiero')) setMenuId(0)
    if (path.includes('ajustes-aplicados')) setMenuId(1)
    if (path.includes('renta-fija-sgt')) setMenuId(2)
  }, [])

  return (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <div style={{ alignItems: 'start', display: 'flex', width: '100%' }}>
            <Box sx={{ minWidth: 220 }}>
              <FormControl fullWidth>
                <Select
                  id='demo-simple-select'
                  onChange={handleChange}
                  value={menuId.toString()}
                >
                  <MenuItem value={0}>Margen Financiero</MenuItem>
                  <MenuItem value={1}>Ajustes Aplicados MF2</MenuItem>
                  <MenuItem value={2}>Renta Fija SGT</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Outlet />
        </Grid>
      </Grid>
    </Container>
  )
}
