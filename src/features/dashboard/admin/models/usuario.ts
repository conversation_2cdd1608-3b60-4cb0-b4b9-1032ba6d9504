/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

export const usuario = type({
  role: 'string',
  userid: 'string?',
  username: 'string?'
})

export type Usuario = typeof usuario.infer

export const usuarioDefault: Usuario = {
  role: '',
  userid: '',
  username: ''
}

export const usuarioMetadata = new Set([
  {
    header: 'Usuario',
    key: 'username'
  },
  {
    header: 'Rol',
    key: 'role'
  }
])
