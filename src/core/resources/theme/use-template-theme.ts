import { createTheme, type ThemeOptions } from '@mui/material'
import { useMemo } from 'react'

import { CustomShadows } from '~/resources/helpers/shadows'
import { overrides } from '~/resources/overrides'
import type { FontFamily, ThemeDirection, ThemeMode } from '~/types/resources'

import { Palette } from './palette'
import { Typography } from './typography'

export const useTemplateTheme = (
  fontFamily: FontFamily,
  mode: ThemeMode,
  themeDirection: ThemeDirection
) => {
  const theme = useMemo(() => Palette(mode), [mode])
  const themeTypography = useMemo(() => Typography(fontFamily), [fontFamily])
  const themeCustomShadows = useMemo(() => CustomShadows(theme), [theme])

  const themeOptions = useMemo<ThemeOptions>(
    () => ({
      breakpoints: {
        values: {
          lg: 1266,
          md: 1024,
          sm: 768,
          xl: 1440,
          xs: 0
        }
      },
      customShadows: themeCustomShadows,
      direction: themeDirection,
      mixins: {
        toolbar: {
          minHeight: 60,
          paddingBottom: 8,
          paddingTop: 8
        }
      },
      palette: {
        lighter: '',
        ...theme.palette,
        darker: ''
      },
      typography: themeTypography
    }),
    [themeDirection, theme, themeTypography, themeCustomShadows]
  )

  const templateTheme = createTheme(themeOptions)
  templateTheme.components = overrides(templateTheme)

  return templateTheme
}
