import type { Theme } from '@mui/material/styles'

export const TableCell = (theme: Theme) => {
  const commonCell = {
    '&:not(:last-of-type)': {
      '&:after': {
        backgroundColor: theme.palette.grey[300],
        content: '""',
        height: 'calc(100% - 12px)',
        position: 'absolute',
        right: 0,
        top: 8,
        width: 1
      },
      position: 'relative'
    }
  }

  return {
    MuiTableCell: {
      styleOverrides: {
        body: {
          color: theme.palette.grey[900]
        },
        footer: {
          fontSize: '0.75rem',
          textTransform: 'uppercase',
          ...commonCell
        },
        head: {
          fontSize: '0.875rem',
          fontWeight: 700,
          ...commonCell
        },
        root: {
          borderColor: theme.palette.divider,
          fontSize: '0.875rem',
          padding: 12
        },
        sizeSmall: {
          padding: 8
        }
      }
    }
  }
}
