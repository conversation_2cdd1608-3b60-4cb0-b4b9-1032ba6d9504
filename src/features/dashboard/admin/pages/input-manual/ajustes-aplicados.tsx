/* eslint-disable unicorn/no-keyword-prefix */
import {
  ajusteAplicados,
  ajusteAplicadosAnoMetadata,
  ajusteAplicadosMetadata
} from '@d/admin/models/ajustes-aplicados'
import { AjustesAplicadosRepository } from '@d/admin/repositories/ajustes-aplicados-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { Reload } from '@d/common/components/Reload'
import { FormControlLabel, Paper, Radio, RadioGroup } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getAjustesAplicados, getAjustesAplicadosAno } = container.get(
    AjustesAplicadosRepository
  )

  const [isAno, setIsAno] = useState(false)

  const {
    data: ajustesAplicadosData,
    isFetching: isAjustesAplicadosPending,
    refetch: refetchAjustesAplicados
  } = useQuery({
    queryFn: () =>
      getAjustesAplicados().then(data =>
        data.map(el => ({
          ...el,
          horaAjuste: el.horaAjuste.split('T')[0] ?? ''
        }))
      ),
    queryKey: ['getAjustesAplicados']
  })

  const {
    data: ajustesAplicadosAnoData,
    isFetching: isAjustesAplicadosAnoPending,
    refetch: refetchAjustesAplicadosAno
  } = useQuery({
    queryFn: getAjustesAplicadosAno,
    queryKey: ['getAjustesAplicadosAno']
  })

  const refetch = () =>
    isAno ? refetchAjustesAplicadosAno() : refetchAjustesAplicados()

  useEffect(() => {
    void refetch()
  }, [day])

  return isAjustesAplicadosPending || isAjustesAplicadosAnoPending ? (
    <Loading />
  ) : (
    <>
      <RadioGroup row sx={{ mb: 2, ml: 2 }}>
        <FormControlLabel
          checked={!isAno}
          control={<Radio />}
          label='Ajustes Aplicados'
          onClick={() => {
            void refetch()
            setIsAno(false)
          }}
          value='ajustesAplicados'
        />
        <FormControlLabel
          checked={isAno}
          control={<Radio />}
          label='Ajustes Aplicados Año'
          onClick={() => {
            void refetch()
            setIsAno(true)
          }}
          value='ajustesAplicadosAno'
        />
      </RadioGroup>
      <Reload onClick={refetch} />
      <Paper
        elevation={1}
        sx={{ ...paperStyle, maxHeight: '400px', overflowY: 'auto' }}
      >
        {isAno ? (
          <GenericTable
            data={ajustesAplicadosAnoData ?? []}
            entries={ajusteAplicados}
            metadata={ajusteAplicadosAnoMetadata}
          />
        ) : (
          <GenericTable
            data={ajustesAplicadosData ?? []}
            entries={ajusteAplicados}
            metadata={ajusteAplicadosMetadata}
          />
        )}
      </Paper>
    </>
  )
}
