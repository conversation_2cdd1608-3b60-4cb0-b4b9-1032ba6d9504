import type { FC, ReactNode } from 'react'

import { ErrorMessage } from './ErrorMessage'
import { Loading } from './Loading'

export const Fetching: FC<{
  children: ReactNode
  errorMessage?: string
  fullHeight?: boolean
  isError: boolean
  isFetching: boolean
}> = ({ children, errorMessage = '', fullHeight, isError, isFetching }) =>
  isFetching ? (
    <Loading fullHeight={fullHeight} />
  ) : isError ? (
    <ErrorMessage fullHeight={fullHeight} message={errorMessage} />
  ) : (
    children
  )
