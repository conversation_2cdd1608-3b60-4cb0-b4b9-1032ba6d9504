import { CodeOutlined } from '@ant-design/icons'
import { Box, CardActions, Collapse, Divider, Tooltip } from '@mui/material'
import { type FC, useState } from 'react'

import { IconButton } from './IconButton'

export const Highlighter: FC<{
  codeHighlight: boolean
  codeString: string
}> = ({ codeHighlight, codeString }) => {
  const [highlight, setHighlight] = useState(codeHighlight)

  return (
    <>
      <CardActions sx={{ justifyContent: 'flex-end' }}>
        <Box sx={{ display: 'flex' }}>
          <Divider
            flexItem
            orientation='vertical'
            sx={{ mx: 1 }}
            variant='middle'
          />
          <Tooltip placement='top-end' title='Show the source'>
            <IconButton
              color={highlight ? 'primary' : 'secondary'}
              onClick={() => setHighlight(!highlight)}
              size='small'
              sx={{ fontSize: '0.875rem' }}
            >
              <CodeOutlined />
            </IconButton>
          </Tooltip>
        </Box>
      </CardActions>
      <Collapse in={highlight}>{highlight && <p> {codeString} </p>}</Collapse>
    </>
  )
}
