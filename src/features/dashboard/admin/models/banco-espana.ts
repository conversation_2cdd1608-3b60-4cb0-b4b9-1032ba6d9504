import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const bancoEspana = type({
  bdeID: 'number?',
  currencyID: 'string',
  fecha: 'string',
  saldo: 'number?',
  tipo: 'number?'
})

export const bancoEspanaConverted = type({
  bdeID: 'number?',
  currencyID: 'string',
  fecha: 'string',
  saldo: 'number?',
  tipo: 'string?'
})

export const bancoEspanaMetadata = new Set([
  {
    header: 'Fecha',
    key: 'fecha'
  },
  {
    header: 'Saldo',
    key: 'saldo'
  },
  {
    header: 'Tipo',
    key: 'tipo'
  }
])

export type BancoEspana = typeof bancoEspana.infer
export type BancoEspanaConverted = typeof bancoEspanaConverted.infer

export const bancoEspanaDefault: BancoEspana = {
  bdeID: 0,
  currencyID: '',
  fecha: '',
  saldo: 0,
  tipo: 0
}

export const bancoEspanaDefaultConverted: BancoEspanaConverted = {
  bdeID: 0,
  currencyID: '',
  fecha: '',
  saldo: 0,
  tipo: ''
}

export const bancoEspanaValidate = (attempt?: BancoEspana) =>
  bancoEspana(attempt) instanceof type.errors ? resetObject(attempt) : attempt
