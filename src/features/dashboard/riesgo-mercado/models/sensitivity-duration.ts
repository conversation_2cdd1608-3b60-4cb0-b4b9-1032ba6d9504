/* eslint-disable perfectionist/sort-sets */
import { type } from 'arktype'

import { resetObject } from '~/utils/models'

export const sensitivityDuration = type({
  modifiedDuration: 'number?',
  sensitivity: 'number?',
  titulo: 'string?',
  weightingPV: 'number?'
})

export const sensitivityDurationMetadata = new Set([
  { header: 'Títu<PERSON>', key: 'titulo' },
  { header: 'Weighting PV', key: 'weightingPV' },
  { header: 'Sensitivity', key: 'sensitivity' },
  { header: 'Modified Duration', key: 'modifiedDuration' }
])

export type SensitivityDuration = typeof sensitivityDuration.infer

export const sensitivityDurationValidate = (attempt?: SensitivityDuration) =>
  sensitivityDuration(attempt) instanceof type.errors
    ? resetObject(attempt)
    : attempt
