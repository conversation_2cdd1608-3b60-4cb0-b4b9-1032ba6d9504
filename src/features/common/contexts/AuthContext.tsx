import { createContext, type FC, type ReactNode } from 'react'

import { authStore } from '@/common/store/auth-store'
import type { Auth } from '~/types/auth'

type AuthContextType = Auth & {
  performLogout: () => void
}

export const AuthContext = createContext<AuthContextType>({} as AuthContextType)

export const AuthProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const auth = authStore.use()

  const performLogout = () => {
    //setSession(null)
    authStore.logout()
  }

  return (
    <AuthContext.Provider value={{ ...auth, performLogout }}>
      {children}
    </AuthContext.Provider>
  )
}

/* useEffect(() => {
    const init = async () => {
      try {
        const serviceToken = window.localStorage.getItem('serviceToken');
        if (serviceToken && verifyToken(serviceToken)) {
          setSession(serviceToken);
          const response = await axios.get('/api/account/me');
          const { user } = response.data;
          dispatch({
            type: LOGIN,
            payload: {
              isLoggedIn: true,
              user
            }
          });
        } else {
          dispatch({
            type: LOGOUT
          });
        }
      } catch (err) {
        console.error(err);
        dispatch({
          type: LOGOUT
        });
      }
    };
    init();
  }, [])
  
  */
