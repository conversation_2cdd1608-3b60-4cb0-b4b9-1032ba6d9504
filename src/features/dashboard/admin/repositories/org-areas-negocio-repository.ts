/* eslint-disable sonarjs/use-type-alias */
import type { AreasNegocio } from '@d/admin/models/areas-negocio'
import { inject, injectable } from 'inversify'

import { ApiModule } from '~/modules/api-module'

@injectable()
export class AreasNegocioRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/areas'

  readonly deleteAreasNegocio = (id: string) =>
    this.api.client.delete<{ message: string }>(`${this.route}/${id}`)

  readonly getAreasNegocio = () =>
    this.api.client
      .get<AreasNegocio[]>(this.route)
      .then(apires => apires.data)
      .then(data => data ?? [])

  readonly insertAreasNegocio = (areasNegocio: AreasNegocio) =>
    this.api.client.post<{ message: string }>(this.route, areasNegocio)

  readonly updateAreasNegocio = (id: string, areasNegocio: AreasNegocio) =>
    this.api.client.put<{ message: string }>(
      `${this.route}/${id}`,
      areasNegocio
    )
}
