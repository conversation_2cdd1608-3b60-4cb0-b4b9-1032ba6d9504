import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Usuario,
  usuario,
  usuarioDefault,
  usuarioMetadata
} from '@d/admin/models/usuario'
import { UsuarioRepository } from '@d/admin/repositories/usuario-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { deleteUsuario, getUsuario, insertUsuario, updateUsuario } =
    container.get(UsuarioRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(usuarioDefault)

  const {
    data: allUsuarioData,
    isFetching: isGetUsuarioPending,
    refetch: refetchUsuario
  } = useQuery({
    queryFn: getUsuario,
    queryKey: ['getUsuario']
  })

  const submitAction = async (incoming: Usuario, _: FormikHelpers<Usuario>) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertUsuario(incoming)
      : updateUsuario(selectedData.current.userid?.toString() ?? '0', incoming))
    setLoadingDialog(false)
    void refetchUsuario()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteUsuario(selectedData.current.userid?.toString() ?? '0')
      setLoadingDialog(false)
      void refetchUsuario()
    }
  }

  return isGetUsuarioPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchUsuario()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = usuarioDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allUsuarioData ?? []}
                entries={usuario}
                isDeleteAction
                isEditAction
                metadata={usuarioMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allUsuarioData ?? []) ?? usuarioDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Usuario>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          role: Yup.string().max(25).required('Role is required'),
          user: Yup.string().max(25).required('User is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Usuario>
              field='username'
              label='Usuario: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.username?.toString() ?? '0'}
            />
            <TextFormControl<Usuario>
              field='role'
              label='Rol: '
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.role.toString()}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
