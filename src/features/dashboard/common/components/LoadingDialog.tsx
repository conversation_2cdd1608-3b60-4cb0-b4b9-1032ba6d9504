import { Box, CircularProgress, Dialog, DialogContent } from '@mui/material'
import type { FC } from 'react'

export const LoadingDialog: FC<{ open: boolean }> = ({ open }) => (
  <Dialog
    aria-describedby='alert-dialog-description'
    aria-labelledby='alert-dialog-title'
    open={open}
  >
    <DialogContent>
      <Box
        sx={{
          alignItems: 'center',
          display: 'flex'
        }}
      >
        <CircularProgress />
      </Box>
    </DialogContent>
  </Dialog>
)
