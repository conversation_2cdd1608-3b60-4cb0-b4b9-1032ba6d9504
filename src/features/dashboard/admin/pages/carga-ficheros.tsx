import { UploadControl } from '@d/admin/components/upload-control'
import { FicherosRepository } from '@d/admin/repositories/ficheros-repository'
import {
  Box,
  Button,
  Container,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  type SelectChangeEvent,
  Tooltip
} from '@mui/material'
import { prop } from 'rambdax'

import { container } from '~/modules/di-module'

import { OverlayAlert } from '../components/alert'

const paperStyle = {
  alignItems: 'center',
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  p: 2
}

const buttonTitle = [
  'Subir Ficheros FX',
  'Subir Fichero Derivados Cobertura',
  'Subir Inventario Contable'
]

const backActions = (
  historizaPLDivisaJob: () => void,
  divisaJob: () => void,
  ficheroCoberturasJob: () => void,
  flujosJob: () => void,
  coberturasJob: () => void,
  inventarioContableJob: () => void
) => ({
  0: (
    <>
      <Button
        onClick={historizaPLDivisaJob}
        sx={{
          background: '#E5F1EE',
          color: 'green',
          marginRight: '5px'
        }}
        variant='contained'
      >
        Historiza PL Divisa
      </Button>
      <Button
        onClick={divisaJob}
        sx={{
          background: '#E5F1EE',
          color: 'green',
          marginRight: '5px'
        }}
        variant='contained'
      >
        Carga Ficheros Divisa
      </Button>
    </>
  ),
  1: (
    <>
      <Button
        onClick={ficheroCoberturasJob}
        sx={{
          background: '#E5F1EE',
          color: 'green',
          marginRight: '5px'
        }}
        variant='contained'
      >
        Carga Fichero Coberturas
      </Button>
      <Button
        onClick={flujosJob}
        sx={{
          background: '#E5F1EE',
          color: 'green',
          marginRight: '5px'
        }}
        variant='contained'
      >
        Carga Fichero Flujos
      </Button>
      <Tooltip title='Presionar botón si se ha subido tarde el fichero de Deuda SGT'>
        <Button
          onClick={coberturasJob}
          sx={{
            background: '#E5F1EE',
            color: 'green',
            marginRight: '5px'
          }}
          variant='contained'
        >
          Carga Coberturas
        </Button>
      </Tooltip>
    </>
  ),
  2: (
    <Button
      onClick={inventarioContableJob}
      sx={{
        background: '#E5F1EE',
        color: 'green',
        marginRight: '5px'
      }}
      variant='contained'
    >
      Carga Inventario Contable
    </Button>
  )
})

export default () => {
  const [menuId, setMenuId] = useState(0)
  const [uploadButton, setUploadButton] = useState(buttonTitle[0] ?? '')
  const [showAlert, setShowAlert] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const {
    coberturasJob,
    divisaJob,
    ficheroCoberturasJob,
    flujosJob,
    historizaPLDivisaJob
  } = container.get(FicherosRepository)

  const handleChange = (event: SelectChangeEvent) => {
    const value = Number.parseInt(event.target.value)
    setMenuId(value)
    // eslint-disable-next-line security/detect-object-injection
    setUploadButton(buttonTitle[value] ?? '')
  }

  const actions = prop(
    menuId as keyof ReturnType<typeof backActions>,
    backActions(
      async () => {
        setIsLoading(true)
        await historizaPLDivisaJob()
        setIsLoading(false)
        setShowAlert(true)
        setTimeout(() => setShowAlert(false), 2000)
      },
      async () => {
        setIsLoading(true)
        await divisaJob()
        setIsLoading(false)
        setShowAlert(true)
        setTimeout(() => setShowAlert(false), 2000)
      },
      async () => {
        setIsLoading(true)
        await ficheroCoberturasJob()
        setIsLoading(false)
        setShowAlert(true)
        setTimeout(() => setShowAlert(false), 2000)
      },
      async () => {
        setIsLoading(true)
        await flujosJob()
        setIsLoading(false)
        setShowAlert(true)
        setTimeout(() => setShowAlert(false), 2000)
      },
      async () => {
        setIsLoading(true)
        await coberturasJob()
        setIsLoading(false)
        setShowAlert(true)
        setTimeout(() => setShowAlert(false), 2000)
      },
      () => {
        setIsLoading(true)
        //s
        setIsLoading(false)
        setShowAlert(true)
        setTimeout(() => setShowAlert(false), 2000)
      }
    )
  )

  return (
    <>
      <OverlayAlert open={showAlert} />
      <Container maxWidth='lg' sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <div
              style={{ alignItems: 'start', display: 'flex', width: '100%' }}
            >
              <Box sx={{ minWidth: 220 }}>
                <FormControl fullWidth>
                  <InputLabel id='demo-simple-select-label'>Menu</InputLabel>
                  <Select
                    id='demo-simple-select'
                    label='Menu'
                    labelId='demo-simple-select-label'
                    onChange={handleChange}
                    value={menuId.toString()}
                  >
                    <MenuItem value={0}>Divisa</MenuItem>
                    <MenuItem value={1}>Derivados de Cobertura</MenuItem>
                    <MenuItem value={2}>Inventario Contable</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </div>
            <br />
            {isLoading && <LinearProgress sx={{ my: 3 }} />}
            <Paper elevation={10} sx={paperStyle}>
              <UploadControl uploadTitle={uploadButton}>
                <Box display='flex' flexDirection='row' gap={2}>
                  {actions}
                </Box>
              </UploadControl>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  )
}
