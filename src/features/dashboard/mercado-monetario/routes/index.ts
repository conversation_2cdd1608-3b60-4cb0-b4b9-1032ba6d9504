import { route } from '@react-router/dev/routes'

export const mercadoMonetarioRouter = [
  route(
    'mercado-monetario/coberturas',
    'features/dashboard/mercado-monetario/pages/coberturas.tsx'
  ),
  route(
    'mercado-monetario/riesgo-mercado',
    'features/dashboard/mercado-monetario/pages/riesgo-mercado.tsx'
  ),
  route(
    'mercado-monetario/cartera-inversion',
    'features/dashboard/mercado-monetario/pages/cartera-inversion.tsx'
  ),
  route(
    'mercado-monetario/margen-financiero',
    'features/dashboard/mercado-monetario/pages/margen/margen.tsx'
  ),
  route(
    'mercado-monetario/cartera-vencimiento',
    'features/dashboard/mercado-monetario/pages/cartera-vencimiento.tsx'
  ),
  route(
    'mercado-monetario/trading',
    'features/dashboard/mercado-monetario/pages/trading.tsx'
  )
]
