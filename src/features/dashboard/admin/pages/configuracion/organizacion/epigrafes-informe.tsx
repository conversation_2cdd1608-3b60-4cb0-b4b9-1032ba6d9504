import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { SelectFormControl } from '@d/admin/components/select-form-control'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Epigrafes,
  epigrafes,
  epigrafesDefault,
  epigrafesMetadata
} from '@d/admin/models/epigrafe-informe'
import { EpigrafesRepository } from '@d/admin/repositories/org-epigrafe-repository'
import { configStore } from '@d/admin/store/config-store'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Container, Grid, MenuItem, Paper } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import type { FormikHelpers } from 'formik'
import { delay, nth } from 'rambdax'
import type { ChangeEvent } from 'react'
import * as Yup from 'yup'

import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const { deleteEpigrafes, getEpigrafes, insertEpigrafes, updateEpigrafes } =
    container.get(EpigrafesRepository)

  const [loadingDialog, setLoadingDialog] = useState(false)
  const { areas } = configStore.use()
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(epigrafesDefault)

  const {
    data: allEpigrafesData,
    isFetching: isGetEpigrafesPending,
    refetch: refetchEpigrafes
  } = useQuery({
    queryFn: getEpigrafes,
    queryKey: ['getEpigrafes']
  })

  const submitAction = async (
    incoming: Epigrafes,
    _: FormikHelpers<Epigrafes>
  ) => {
    setOpenCreateEditDialog(false)
    await delay(150)
    setLoadingDialog(true)
    await (selectedIndex.current === null
      ? insertEpigrafes(incoming)
      : updateEpigrafes(selectedData.current.epigrafeID.toString(), incoming))
    setLoadingDialog(false)
    void refetchEpigrafes()
  }

  const deleteAction = async (isOk: boolean) => {
    setOpenDeleteDialog(false)
    if (isOk) {
      await delay(150)
      setLoadingDialog(true)
      await deleteEpigrafes(selectedData.current.epigrafeID.toString())
      setLoadingDialog(false)
      void refetchEpigrafes()
    }
  }

  return isGetEpigrafesPending ? (
    <Loading />
  ) : (
    <>
      <Reload onClick={() => refetchEpigrafes()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = epigrafesDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item sx={{ display: 'flex' }} xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, minWidth: '100%' }}>
              <GenericTable
                data={allEpigrafesData ?? []}
                entries={epigrafes}
                isDeleteAction
                isEditAction
                metadata={epigrafesMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, allEpigrafesData ?? []) ?? epigrafesDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <LoadingDialog open={loadingDialog} />
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <DialogForm<Epigrafes>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          nombre: Yup.string().max(5).required('Nombre is required'),
          orden: Yup.number().required('Orden is required'),
          titulo: Yup.string().max(25).required('Titulo is required')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <>
            <TextFormControl<Epigrafes>
              field='nombre'
              label='Nombre: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.nombre.toString()}
            />
            <TextFormControl<Epigrafes>
              field='orden'
              label='Orden: '
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.orden.toString()}
            />
            <SelectFormControl<Epigrafes>
              field='accountingAreaID'
              label='Area: '
              onBlur={handleBlur}
              onChange={e => handleChange(e as ChangeEvent)}
              value={values.accountingAreaID.toString()}
            >
              {areas.map((area, ind) => (
                <MenuItem key={ind} value={ind}>
                  {area.titulo}
                </MenuItem>
              ))}
            </SelectFormControl>
          </>
        )}
      </DialogForm>
    </>
  )
}
