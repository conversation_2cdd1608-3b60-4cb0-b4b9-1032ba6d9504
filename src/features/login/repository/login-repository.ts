/* eslint-disable functional/no-try-statements */
import { inject, injectable } from 'inversify'
import { piped } from 'rambdax'

import { ApiModule } from '~/modules/api-module'
import { roleFromString } from '~/types/auth'

type Logon = {
  role?: string
  status: string
  token: string
  user?: string
}

@injectable()
export class LoginRepository {
  @inject(ApiModule)
  private readonly api!: ApiModule

  private readonly route = '/usuarios'

  readonly getUser = (user: string) =>
    this.api.client
      .get<{ role: string }>(`${this.route}/${user}`)
      .then(apires => apires.data)
      .then(data => data ?? { role: 'user' })
      .then(({ role }) => roleFromString(role))

  readonly jwtTokenizer = (username: string, role: string) =>
    this.api.client
      .post<Logon>('/auth/logon', {
        role,
        username
      })
      .then(res =>
        piped(res.data, data => data ?? { status: 'error', token: 'error' })
      )

  readonly login = (email: string, password: string) =>
    this.api.adClient.post('/login', { email, password })
}
