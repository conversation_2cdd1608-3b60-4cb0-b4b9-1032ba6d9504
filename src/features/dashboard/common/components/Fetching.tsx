import type { FC, ReactNode } from 'react'

import { ErrorMessage } from './ErrorMessage'
import { Loading } from './Loading'

export const Fetching: FC<{
  children: ReactNode
  errorMessage?: string
  isError: boolean
  isFetching: boolean
}> = ({ children, errorMessage = '', isError, isFetching }) =>
  isFetching ? (
    <Loading notHeight />
  ) : isError ? (
    <ErrorMessage message={errorMessage} />
  ) : (
    children
  )
