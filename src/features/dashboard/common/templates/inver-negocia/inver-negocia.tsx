import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import {
  headersFactoresMetadata,
  historicoParameticoMetadata,
  varNewAreas,
  varNewAreasProductoFactor
} from '@d/common/models/var-newareas'
import { Box, Container, Grid, Link, Paper } from '@mui/material'
import type { FC } from 'react'

import { TitleSubtitle } from '@/common/components/TitleSubtitle'
import { paperStyle } from '~/resources/config/paper'

import { Loading } from '../../components/Loading'
import { Reload } from '../../components/Reload'
import {
  inverNegociaController,
  preventDefault
} from './inver-negocia-controller'

const LinkCheck: FC<{
  isInver?: boolean
  onClick?: () => void
  underlined: boolean
}> = ({ isInver = true, onClick, underlined }) => (
  <Link
    onClick={onClick}
    underline={underlined ? 'always' : 'none'}
    variant='h4'
  >
    {isInver ? 'Inversión' : 'Negociación'}
  </Link>
)

export const InverNegocia: FC<{
  areaName: string
  inver?: boolean
}> = ({ areaName, inver }) => {
  const {
    isVarFactorError,
    isVarFactorPending,
    isVarHistoricoError,
    isVarHistoricoPending,
    isVarProductoError,
    isVarProductoPending,
    refetch,
    selectInverNegocia,
    setSelectInverNegocia,
    varFactor,
    varFactorError,
    varHistorico,
    varHistoricoError,
    varProductoError,
    varProducto
  } = inverNegociaController(areaName, inver)

  return isVarFactorPending || isVarHistoricoPending || isVarProductoPending ? (
    <Loading />
  ) : (
    <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
      <Reload onClick={refetch} />
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box
            onClick={preventDefault}
            sx={{
              '& > :not(style) ~ :not(style)': {
                ml: 2
              },
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'start',
              typography: 'body1'
            }}
          >
            {inver === undefined ? (
              <>
                <LinkCheck
                  onClick={() => {
                    setSelectInverNegocia(change => !change)
                    setTimeout(refetch, 100)
                  }}
                  underlined={selectInverNegocia}
                />
                <LinkCheck
                  isInver={false}
                  onClick={() => {
                    setSelectInverNegocia(change => !change)
                    setTimeout(refetch, 100)
                  }}
                  underlined={!selectInverNegocia}
                />
              </>
            ) : (
              <Link underline='always' variant='h3'>
                {inver ? 'Inversión' : 'Negociación'}
              </Link>
            )}
          </Box>
        </Grid>
        <Grid item xs={6}>
          <TitleSubtitle title='VaR Histórico' />
          <Paper elevation={10} sx={paperStyle}>
            <GenericTable
              data={varHistorico ?? []}
              entries={varNewAreas}
              metadata={historicoParameticoMetadata}
              noTotal={false}
            />
          </Paper>
        </Grid>
        <Grid item xs={6}>
          <TitleSubtitle title='VaR Paramétrico' />
          <Paper elevation={10} sx={paperStyle}>
            <GenericTable
              data={varProducto ?? []}
              entries={varNewAreasProductoFactor}
              metadata={historicoParameticoMetadata}
              noTotal={false}
            />
          </Paper>
        </Grid>
        <Grid item xs={6}></Grid>
        <Grid item xs={6}>
          <TitleSubtitle title='Factores de riesgo' />
          <Paper elevation={10} sx={paperStyle}>
            <GenericTable
              data={varFactor ?? []}
              entries={varNewAreasProductoFactor}
              metadata={headersFactoresMetadata}
              noTotal={false}
            />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}
