/* eslint-disable sonarjs/no-nested-functions */
/* eslint-disable unicorn/no-keyword-prefix */
import { OverlayAlert } from '@d/admin/components/alert'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import { AppChart } from '@d/common/components/Chart'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import { limitesChartOpts } from '@d/riesgo-mercado/data/limites-chart'
import { limites, limitesMetadata } from '@d/riesgo-mercado/models/limites'
import { LimitesRepository } from '@d/riesgo-mercado/repositories/limites-repository'
import {
  Box,
  Button,
  Container,
  Grid,
  LinearProgress,
  Paper,
  Typography
} from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import type { FormikHelpers } from 'formik'
import { nth, partition, piped, sortBy } from 'rambdax'
import { useEffect, useRef, useState } from 'react'
import * as Yup from 'yup'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'

const paperStyle = {
  background: 'rgba(255,255,255,0.5)',
  display: 'flex',
  flexDirection: 'column',
  mt: 3,
  p: 2
}

export default () => {
  const day = calendarStore.actualDay.use()

  const { getLimites, setCallLimitesConsumUpd, updateLimites } =
    container.get(LimitesRepository)

  const [isLoading, setIsLoading] = useState(false)
  const [showAlert, setShowAlert] = useState(false)
  const [updateLimitesFecha, setUpdateLimitesFecha] = useState<Dayjs | null>(
    dayjs(day)
  )
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)

  const series = useRef<ApexAxisChartSeries>([])
  const options = useRef(limitesChartOpts)
  const selectedData = useRef({ limiteDispuesto: '0', limiteID: '0' })

  const {
    data: limitesData,
    error: limitesError,
    isError: isLimitesError,
    isFetching: islimitesPending,
    refetch: refetchLimites
  } = useQuery({
    queryFn: () =>
      getLimites().then(data => {
        options.current = {
          ...options.current,
          xaxis: {
            ...options.current.xaxis,
            categories: data.map(el => el.nombreCorto)
          }
        }
        series.current = [
          { data: data.map(el => el.cobertura), name: 'Cobertura' }
        ]
        return piped(
          data.map(el => ({
            ...el,
            cobertura: `${(el.cobertura * 100).toFixed(2).toString()}%`
          })),
          partition(x => x.nombreCorto.startsWith('STOP')),
          ([stops, limites]) => [
            ...sortBy(x => x.nombreLargo, stops),
            ...sortBy(x => x.nombreLargo, limites)
          ]
        )
      }),
    queryKey: ['getLimites']
  })

  const performUpdateLimites = async () => {
    setIsLoading(true)
    await updateLimites()
    setIsLoading(false)
    void refetchLimites()
    setShowAlert(true)
    setTimeout(() => setShowAlert(false), 2000)
  }

  const submitAction = async (
    limite: { limiteDispuesto: string; limiteID: string },
    _: FormikHelpers<{ limiteDispuesto: string; limiteID: string }>
  ) => {
    setOpenCreateEditDialog(false)
    await setCallLimitesConsumUpd(limite.limiteDispuesto, limite.limiteID)
    void refetchLimites()
  }

  useEffect(() => {
    void refetchLimites()
  }, [day])

  return (
    <>
      <OverlayAlert open={showAlert} />
      <Reload onClick={() => refetchLimites()} />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Container>
          <Grid item xs={3}>
            <Button
              onClick={performUpdateLimites}
              sx={{ marginRight: '15px' }}
              variant='contained'
            >
              Actualizar consumos
            </Button>
            <DatePicker
              defaultValue={updateLimitesFecha}
              label=''
              onChange={setUpdateLimitesFecha}
              slotProps={{ textField: { size: 'small' } }}
            />
          </Grid>
          <Grid item xs={9}></Grid>
          {isLoading && <LinearProgress sx={{ my: 3 }} />}
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ ...paperStyle, p: 4 }}>
              <Grid item xs={7}>
                <Typography component='h2' variant='h4'>
                  Consumo de límites
                </Typography>
                <Fetching
                  errorMessage={limitesError?.message}
                  isError={isLimitesError}
                  isFetching={islimitesPending}
                >
                  <Paper
                    elevation={1}
                    sx={{
                      ...paperStyle
                    }}
                  >
                    <GenericTable
                      data={limitesData ?? []}
                      entries={limites}
                      isEditAction
                      metadata={limitesMetadata}
                      onEditClick={index => {
                        selectedData.current = {
                          limiteDispuesto:
                            nth(
                              index,
                              limitesData ?? []
                            )?.limiteDispuesto.toString() ?? '',
                          limiteID:
                            nth(
                              index,
                              limitesData ?? []
                            )?.limiteID.toString() ?? ''
                        }
                        setOpenCreateEditDialog(true)
                      }}
                    />
                  </Paper>
                </Fetching>
                <Paper elevation={1} sx={paperStyle}>
                  <Typography component='h2' sx={{ mb: 3 }} variant='h5'>
                    Cobertura (%)
                  </Typography>
                  <Box id='chart' sx={{ bgcolor: 'transparent' }}>
                    <Fetching
                      errorMessage={limitesError?.message}
                      isError={isLimitesError}
                      isFetching={islimitesPending}
                    >
                      <AppChart
                        height={305}
                        options={options.current}
                        series={series.current}
                        type='bar'
                      />
                    </Fetching>
                  </Box>
                </Paper>
              </Grid>
            </Paper>
          </Grid>
          <Grid item xs={12}></Grid>
        </Container>
      </LocalizationProvider>
      <DialogForm<{ limiteDispuesto: string; limiteID: string }>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={submitAction}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          limiteDispuesto: Yup.number().required('campo requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, values }) => (
          <TextFormControl<{ limiteDispuesto: string; limiteID: string }>
            field='limiteDispuesto'
            label='Limite Dispuesto: '
            onBlur={handleBlur}
            onChange={handleChange}
            type='number'
            value={values.limiteDispuesto.toString()}
          />
        )}
      </DialogForm>
    </>
  )
}
