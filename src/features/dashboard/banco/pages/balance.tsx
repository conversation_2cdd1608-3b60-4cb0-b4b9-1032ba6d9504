import { DownOutlined } from '@ant-design/icons'
import {
  controlDPVRFHTMRF,
  controlDPVRFHTMRFMetadata
} from '@d/banco/models/control-RF'
import { controlDPVRV, controlDPVRVMetadata } from '@d/banco/models/control-RV'
import {
  controlTrading,
  controlTradingMetadata
} from '@d/banco/models/control-trading'
import {
  margenDeltasSummary,
  margenDeltasSummaryMetadata
} from '@d/banco/models/margen-deltas-summary'
import {
  margenSummary,
  margenSummaryMetadata
} from '@d/banco/models/margen-summary'
import {
  patrimonioNeto,
  patrimonioNetoMetadata
} from '@d/banco/models/patrimonio-neto'
import { sumMargen, sumMargenMetadata } from '@d/banco/models/sum-margen'
import { BancoRepository } from '@d/banco/repository/banco-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Paper,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { Fetching } from '@/dashboard/common/components/Fetching'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const {
    getControlDPVRF,
    getControlDPVRV,
    getControlHTMRF,
    getControlTrading,
    getMargenDeltasSummary,
    getMargenSummary,
    getPatrimonioNeto,
    getSumMargen
  } = container.get(BancoRepository)

  const {
    data: getControlTradingData,
    error: getControlTradingError,
    isError: isGetControlTradingError,
    isFetching: isGetControlTradingPending,
    refetch: refetchGetControlTrading
  } = useQuery({
    queryFn: getControlTrading,
    queryKey: ['getControlTrading']
  })

  const {
    data: getMargenSummaryData,
    error: getMargenSummaryError,
    isError: isGetMargenSummaryError,
    isFetching: isGetMargenSummaryPending,
    refetch: refetchGetMargenSummary
  } = useQuery({
    queryFn: getMargenSummary,
    queryKey: ['getMargenSummary']
  })

  const {
    data: getSumMargenB1Data,
    error: getSumMargenB1Error,
    isError: isGetSumMargenB1Error,
    isFetching: isGetSumMargenB1Pending,
    refetch: refetchGetSumB1Margen
  } = useQuery({
    queryFn: () => getSumMargen('12'),
    queryKey: ['getSumMargenB1']
  })

  const {
    data: getSumMargenB2Data,
    error: getSumMargenB2Error,
    isError: isGetSumMargenB2Error,
    isFetching: isGetSumMargenB2Pending,
    refetch: refetchGetSumB2Margen
  } = useQuery({
    queryFn: () => getSumMargen('1'),
    queryKey: ['getSumMargenB2']
  })

  const {
    data: getSumMargenB3Data,
    error: getSumMargenB3Error,
    isError: isGetSumMargenB3Error,
    isFetching: isGetSumMargenB3Pending,
    refetch: refetchGetSumB3Margen
  } = useQuery({
    queryFn: () => getSumMargen('13'),
    queryKey: ['getSumMargenB3']
  })

  const {
    data: getSumMargenB4Data,
    error: getSumMargenB4Error,
    isError: isGetSumMargenB4Error,
    isFetching: isGetSumMargenB4Pending,
    refetch: refetchGetSumB4Margen
  } = useQuery({
    queryFn: () => getSumMargen('4'),
    queryKey: ['getSumMargenB4']
  })

  const {
    data: getSumMargenB5Data,
    error: getSumMargenB5Error,
    isError: isGetSumMargenB5Error,
    isFetching: isGetSumMargenB5Pending,
    refetch: refetchGetSumB5Margen
  } = useQuery({
    queryFn: () => getSumMargen('15'),
    queryKey: ['getSumMargenB5']
  })

  const {
    data: getSumMargenB6Data,
    error: getSumMargenB6Error,
    isError: isGetSumMargenB6Error,
    isFetching: isGetSumMargenB6Pending,
    refetch: refetchGetSumB6Margen
  } = useQuery({
    queryFn: () => getSumMargen('11'),
    queryKey: ['getSumMargenB6']
  })

  const {
    data: getVariacionData,
    error: getVariacionError,
    isError: isGetVariacionError,
    isFetching: isGetVariacionPending,
    refetch: refetchVariacionMargen
  } = useQuery({
    queryFn: getMargenDeltasSummary,
    queryKey: ['getVariacion']
  })

  const {
    data: getControlDPVRFData,
    error: getControlDPVRFError,
    isError: isGetControlDPVRFError,
    isFetching: isGetControlDPVRFPending,
    refetch: refetchGetControlDPVRF
  } = useQuery({
    queryFn: getControlDPVRF,
    queryKey: ['getControlDPVRF']
  })

  const {
    data: getControlDPVRVData,
    error: getControlDPVRVError,
    isError: isGetControlDPVRVError,
    isFetching: isGetControlDPVRVPending,
    refetch: refetchGetControlDPVRV
  } = useQuery({
    queryFn: getControlDPVRV,
    queryKey: ['getControlDPVRV']
  })

  const {
    data: getControlHTMRFData,
    error: getControlHTMRFError,
    isError: isGetControlHTMRFError,
    isFetching: isGetControlHTMRFPending,
    refetch: refetchGetControlHTMRF
  } = useQuery({
    queryFn: getControlHTMRF,
    queryKey: ['getControlHTMRF']
  })

  const {
    data: getPatrimonioNetoData,
    error: getPatrimonioNetoError,
    isError: isGetPatrimonioNetoError,
    isFetching: isGetPatrimonioNetoPending,
    refetch: refetchGetPatrimonioNeto
  } = useQuery({
    queryFn: getPatrimonioNeto,
    queryKey: ['getPatrimonioNeto']
  })

  const refetch = () => {
    void refetchGetControlTrading()
    void refetchGetMargenSummary()
    void refetchGetSumB1Margen()
    void refetchGetSumB2Margen()
    void refetchGetSumB3Margen()
    void refetchGetSumB4Margen()
    void refetchGetSumB5Margen()
    void refetchGetSumB6Margen()
    void refetchVariacionMargen()
    void refetchGetControlDPVRF()
    void refetchGetControlDPVRV()
    void refetchGetControlHTMRF()
    void refetchGetPatrimonioNeto()
  }

  useEffect(() => {
    refetch()
  }, [day])

  return (
    <>
      <Reload onClick={refetch} />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          width: '100%'
        }}
      >
        <Accordion defaultExpanded>
          <AccordionSummary
            aria-controls='panel1-content'
            expandIcon={<DownOutlined />}
            id='panel1-header'
          >
            <Typography component='h4' variant='h4'>
              A. Control Del Trading
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Fetching
              errorMessage={getControlTradingError?.message}
              isError={isGetControlTradingError}
              isFetching={isGetControlTradingPending}
            >
              <Paper elevation={10} sx={paperStyle}>
                <GenericTable
                  data={getControlTradingData ?? []}
                  entries={controlTrading}
                  metadata={controlTradingMetadata}
                  noTotal={false}
                />
              </Paper>
            </Fetching>
          </AccordionDetails>
        </Accordion>
        <Accordion defaultExpanded>
          <AccordionSummary
            aria-controls='panel1-content'
            expandIcon={<DownOutlined />}
            id='panel1-header'
          >
            <Typography component='h4' variant='h4'>
              B. Margen Financiero
            </Typography>
          </AccordionSummary>
          <AccordionDetails
            sx={{
              alignItems: 'center',
              display: 'grid',
              gap: 4,
              gridTemplateColumns: '1fr 1fr',
              overflow: 'auto'
            }}
          >
            <Fetching
              errorMessage={getMargenSummaryError?.message}
              isError={isGetMargenSummaryError}
              isFetching={isGetMargenSummaryPending}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gridColumn: '1 / -1'
                }}
              >
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  Total Margen Financiero
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getMargenSummaryData ?? []}
                    entries={margenSummary}
                    metadata={margenSummaryMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getSumMargenB1Error?.message}
              isError={isGetSumMargenB1Error}
              isFetching={isGetSumMargenB1Pending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  B. 1 Mercado Monetario
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getSumMargenB1Data ?? []}
                    entries={sumMargen}
                    metadata={sumMargenMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getSumMargenB2Error?.message}
              isError={isGetSumMargenB2Error}
              isFetching={isGetSumMargenB2Pending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  B. 2 Renta Fija
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getSumMargenB2Data ?? []}
                    entries={sumMargen}
                    metadata={sumMargenMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getSumMargenB3Error?.message}
              isError={isGetSumMargenB3Error}
              isFetching={isGetSumMargenB3Pending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  B. 3 Mercado de Divisas
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getSumMargenB3Data ?? []}
                    entries={sumMargen}
                    metadata={sumMargenMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getSumMargenB4Error?.message}
              isError={isGetSumMargenB4Error}
              isFetching={isGetSumMargenB4Pending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  B. 4 COAP
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getSumMargenB4Data ?? []}
                    entries={sumMargen}
                    metadata={sumMargenMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getSumMargenB5Error?.message}
              isError={isGetSumMargenB5Error}
              isFetching={isGetSumMargenB5Pending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  B. 5 Saldos Tesoreros
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getSumMargenB5Data ?? []}
                    entries={sumMargen}
                    metadata={sumMargenMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getSumMargenB6Error?.message}
              isError={isGetSumMargenB6Error}
              isFetching={isGetSumMargenB6Pending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  B. 6 Banca Corporativa
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getSumMargenB6Data ?? []}
                    entries={sumMargen}
                    metadata={sumMargenMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getVariacionError?.message}
              isError={isGetVariacionError}
              isFetching={isGetVariacionPending}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gridColumn: '1 / -1'
                }}
              >
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  Variación Margen Día Anterior
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getVariacionData ?? []}
                    entries={margenDeltasSummary}
                    metadata={margenDeltasSummaryMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
          </AccordionDetails>
        </Accordion>
        <Accordion defaultExpanded>
          <AccordionSummary
            aria-controls='panel1-content'
            expandIcon={<DownOutlined />}
            id='panel1-header'
          >
            <Typography component='h4' variant='h4'>
              C. Control Carteras de Inversión
            </Typography>
          </AccordionSummary>
          <AccordionDetails
            sx={{
              alignItems: 'center',
              display: 'grid',
              gap: 4,
              gridTemplateColumns: '1fr 1fr',
              overflow: 'auto'
            }}
          >
            <Fetching
              errorMessage={getControlDPVRFError?.message}
              isError={isGetControlDPVRFError}
              isFetching={isGetControlDPVRFPending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  C. 1 Cartera Renta Fija DPV
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getControlDPVRFData ?? []}
                    entries={controlDPVRFHTMRF}
                    metadata={controlDPVRFHTMRFMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getControlHTMRFError?.message}
              isError={isGetControlHTMRFError}
              isFetching={isGetControlHTMRFPending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  C. 2 Cartera Renta Fija a Vencimiento
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getControlHTMRFData ?? []}
                    entries={controlDPVRFHTMRF}
                    metadata={controlDPVRFHTMRFMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getControlDPVRVError?.message}
              isError={isGetControlDPVRVError}
              isFetching={isGetControlDPVRVPending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  C. 3 Cartera Renta Variable
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getControlDPVRVData ?? []}
                    entries={controlDPVRV}
                    metadata={controlDPVRVMetadata}
                    noTotal={false}
                  />
                </Paper>
              </Box>
            </Fetching>
            <Fetching
              errorMessage={getPatrimonioNetoError?.message}
              isError={isGetPatrimonioNetoError}
              isFetching={isGetPatrimonioNetoPending}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography
                  component='h5'
                  sx={{ mb: 2, ml: 1, opacity: 0.8 }}
                  variant='h5'
                >
                  C. 4 Impacto en Patrimonio Neto
                </Typography>
                <Paper elevation={10} sx={paperStyle}>
                  <GenericTable
                    data={getPatrimonioNetoData ?? []}
                    entries={patrimonioNeto}
                    metadata={patrimonioNetoMetadata}
                  />
                </Paper>
              </Box>
            </Fetching>
          </AccordionDetails>
        </Accordion>
      </Box>
    </>
  )
}
